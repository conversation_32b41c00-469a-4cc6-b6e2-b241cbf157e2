package io.terminus.tsrm.md.adapter.base;


import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import io.terminus.tsrm.md.app.base.BankExtService;
import io.terminus.tsrm.md.spi.model.base.dto.BankSyncDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "银行")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/wq/md/base/bank")
@Slf4j
public class ExtWqBankAction {

    private final BankExtService bankExtService;

    @ApiOperation("BPM银行档案同步")
    @Action(name = "BPM银行档案同步", value = "BPM_BANK_SYNC")
    @RequestMapping(value = "bpmBankSync", method = RequestMethod.POST)
    public Response<Boolean> bpmBankSync(@RequestBody BankSyncDTO bankSyncDTO) {
        log.info("ExtWqBankAction::sync, data:{}", JSON.toJSONString(bankSyncDTO));
        return Response.ok(bankExtService.bpmBankSync(bankSyncDTO));
    }
}
