package io.terminus.tsrm.md.adapter.pay;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import io.terminus.tsrm.md.app.pay.ExtWqGenPayTermHeadCfService;
import io.terminus.tsrm.md.spi.model.pay.dto.ExtWqGenPayTermHeadCfDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 付款协议 action
 *
 * <AUTHOR>
 */
@Api(tags = "付款协议")
@RequiredArgsConstructor
@RestController
@RequestMapping("/wq/md/pay/term")
public class ExtWqGenPayTermHeadCfAction {

    private final ExtWqGenPayTermHeadCfService genPayTermHeadCfService;

    @ApiOperation("新增编辑付款协议")
    @Action(name = "新增编辑付款协议", value = "PAY_TERM_SAVE_ACTION")
    @RequestMapping(value = "payTermSaveAction", method = RequestMethod.POST)
    public Response<Void> payTermSaveAction(ExtWqGenPayTermHeadCfDTO payTermHeadCfDTO) {
        genPayTermHeadCfService.savePayTermHead(payTermHeadCfDTO);
        return Response.ok();
    }
}
