package io.terminus.tsrm.md.adapter.mat;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.model.BaseModel;
import io.terminus.erp.md.spi.model.po.base.GenBrandMdPO;
import io.terminus.erp.md.spi.model.po.base.GenMatCateMdPO;
import io.terminus.erp.md.spi.model.po.mat.GenMatMdPO;
import io.terminus.erp.md.spi.util.MD;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import io.terminus.tsrm.md.infrastructure.repo.mat.MatManageCatAttriRepo;
import io.terminus.tsrm.md.infrastructure.repo.mat.MatManageCateTotalRepo;
import io.terminus.tsrm.md.infrastructure.repo.mat.MatManageSimilarListRepo;
import io.terminus.tsrm.md.infrastructure.repo.mat.SrmMatManagementTrRepo;
import io.terminus.tsrm.md.spi.convert.mat.MatManageSimilarListConverter;
import io.terminus.tsrm.md.spi.convert.mat.SrmMatManagementTrConverter;
import io.terminus.tsrm.md.spi.dict.mat.SrmMatManagementTrMatManagementStatusDict;
import io.terminus.tsrm.md.spi.model.base.dto.AiReqDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.AiMatAttrResultDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.AiMatCateAttrResultDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.AiMatCateResultDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.AiMatStandardResultDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.MatManageCatAttriDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.MatManageCateTotalDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.MatManageSimilarListDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.SrmMatExcelImportDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.SrmMatImportTrDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.SrmMatManagementTrDTO;
import io.terminus.tsrm.md.spi.model.mat.po.MatManageCatAttriPO;
import io.terminus.tsrm.md.spi.model.mat.po.MatManageCateTotalPO;
import io.terminus.tsrm.md.spi.model.mat.po.MatManageSimilarListPO;
import io.terminus.tsrm.md.spi.model.mat.po.SrmMatManagementTrPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 物料治理
 *
 * <AUTHOR>
 * @date 2025/4/22
 */
@Api(tags = "物料治理")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/srm/md/mat")
@Slf4j
public class MatManageAction {

    private final MatManageCatAttriRepo matManageCatAttriRepo;

    private final MatManageCateTotalRepo matManageCateTotalRepo;

    private final SrmMatManagementTrRepo srmMatManagementTrRepo;

    private final SrmMatManagementTrConverter srmMatManagementTrConverter;

    private final MatManageSimilarListRepo matManageSimilarListRepo;

    private final MatManageSimilarListConverter matManageSimilarListConverter;

    /**
     * 构建类目全路径
     * @param cateList 类目列表
     * @return 类目ID与其全路径的映射
     */
    @ApiOperation("构建类目全路径")
    @Action(name = "构建类目全路径", value = "BUILD_FULL_CATE_PATHS")
    @RequestMapping(value = "buildFullCatePaths", method = RequestMethod.POST)
    public Response<AiReqDTO> buildFullCatePaths(List<GenMatCateMdPO> cateList) {
        // 构建类目ID到类目对象的映射，方便查找
        Map<Long, GenMatCateMdPO> cateMap = cateList.stream()
                .collect(Collectors.toMap(BaseModel::getId, cate -> cate));

        List<JSONObject> resultList = new ArrayList<>();

        for (GenMatCateMdPO cate : cateList) {
            if (cate.getLeaf()) {
                JSONObject jsonObj = new JSONObject();
                jsonObj.put("id", cate.getId());

                // 构建当前类目的全路径
                String fullPath = buildSingleCatePath(cate, cateMap);
                // 调试阶段代码 去掉最外层的类目,实际项目中不会有这个最顶级类目,去掉这个代码
                fullPath = fullPath.replace("京博物料分类/", "");
                jsonObj.put("name", fullPath);

                resultList.add(jsonObj);
            }
        }
        AiReqDTO aiReqDTO = new AiReqDTO();
        //aiReqDTO.setJsonInput(JSON.toJSONString(resultList.stream().limit(10).collect(Collectors.toList()).toString()));
        aiReqDTO.setJsonInput(JSON.toJSONString(resultList));
        return Response.ok(aiReqDTO);
    }

    /**
     * 递归构建单个类目的全路径
     * @param cate 当前类目
     * @param cateMap 类目ID到类目对象的映射
     * @return 全路径字符串
     */
    private String buildSingleCatePath(GenMatCateMdPO cate, Map<Long, GenMatCateMdPO> cateMap) {

        // 如果没有父类目，则直接返回当前类目名称
        if (cate.getMatCateParent() == null || cate.getMatCateParent() == 0L) {
            return cate.getMatCateName();
        }

        // 获取父类目
        GenMatCateMdPO parentCate = cateMap.get(cate.getMatCateParent());
        if (parentCate == null) {
            // 父类目不在列表中，直接返回当前类目名称
            return cate.getMatCateName();
        }

        // 递归构建父类目的全路径
        String parentPath = buildSingleCatePath(parentCate, cateMap);

        // 拼接父类目路径和当前类目名称
        return parentPath + "/" + cate.getMatCateName();
    }

    /**
     * 递归构建单个类目的全路径 查表 只用来处理单个类目
     * @param cate 当前类目
     * @return 全路径字符串
     */
    private String buildSingleCatePathByDB(GenMatCateMdPO cate) {

        // 如果没有父类目，则直接返回当前类目名称
        if (cate.getMatCateParent() == null || cate.getMatCateParent() == 0L) {
            return cate.getMatCateName();
        }
        GenMatCateMdPO parentCate = MD.queryById(cate.getMatCateParent(), GenMatCateMdPO.class);
        if (parentCate == null) {
            // 父类目不在列表中，直接返回当前类目名称
            return cate.getMatCateName();
        }

        // 递归构建父类目的全路径
        String parentPath = buildSingleCatePathByDB(parentCate);

        // 拼接父类目路径和当前类目名称
        return parentPath + "/" + cate.getMatCateName();
    }

    @ApiOperation("解析物料类目推荐属性结果并入库")
    @Action(name = "解析物料类目推荐属性结果并入库", value = "TSRM_PARSE_AI_CATE_ATTR")
    @RequestMapping(value = "parseAiCateAttr", method = RequestMethod.POST)
    public Response<Void> parseAiCateAttr(AiReqDTO aiReqDTO) {
        String jsonResult = trimAiResult(aiReqDTO.getJsonResult());
        List<AiMatCateAttrResultDTO> aiMatCateAttrResultDTOList = JSON.parseArray(jsonResult, AiMatCateAttrResultDTO.class);
        BigDecimal defaultsimilarityThreshold = new BigDecimal("50");
        for (AiMatCateAttrResultDTO aiMatCateAttrResultDTO : aiMatCateAttrResultDTOList) {
            if (aiMatCateAttrResultDTO.getId() != null) {
                MatManageCateTotalPO matManageCateTotalPO = new MatManageCateTotalPO();
                matManageCateTotalPO.setMatCate(aiMatCateAttrResultDTO.getId());
                matManageCateTotalPO.setAttriTotal((long) aiMatCateAttrResultDTO.getAttr().size());
                matManageCateTotalPO.setSimilarityThreshold(defaultsimilarityThreshold);
                matManageCateTotalRepo.save(matManageCateTotalPO);

                List<MatManageCatAttriPO> matManageCatAttriPOS = aiMatCateAttrResultDTO.getAttr().entrySet().stream().map(entry -> {
                    MatManageCatAttriPO matManageCatAttriPO = new MatManageCatAttriPO();
                    matManageCatAttriPO.setMatManageCateTotalAttriListId(matManageCateTotalPO.getId());
                    matManageCatAttriPO.setAttriName(entry.getKey());
                    matManageCatAttriPO.setAttriInfo(entry.getValue());
                    matManageCatAttriPO.setAiRecommend(true);
                    return matManageCatAttriPO;
                }).collect(Collectors.toList());
                matManageCatAttriRepo.saveList(matManageCatAttriPOS, matManageCateTotalPO.getId());
            }
        }
        return Response.ok();
    }

    @ApiOperation("构建请求ai的物料列表")
    @Action(name = "构建请求ai的物料列表", value = "TSRM_BUILD_MAT_LIST")
    @RequestMapping(value = "buildMatList", method = RequestMethod.POST)
    public Response<AiReqDTO> buildMatList(List<SrmMatManagementTrPO> matList) {
        List<JSONObject> resultList = new ArrayList<>();
        for (SrmMatManagementTrPO mat : matList) {
            JSONObject jsonObj = new JSONObject();
            jsonObj.put("id", mat.getId());
            jsonObj.put("name", mat.getMatDesc() + (StringUtils.isNotBlank(mat.getMatOriginAttr()) ? "  " + mat.getMatOriginAttr() : ""));

            resultList.add(jsonObj);
        }
        AiReqDTO aiReqDTO = new AiReqDTO();
        aiReqDTO.setJsonInput(JSON.toJSONString(resultList));
        return Response.ok(aiReqDTO);
    }

    @ApiOperation("解析物料列表推荐类目并保存")
    @Action(name = "解析物料列表推荐类目并保存", value = "TSRM_PARSE_AI_MAT_CATE")
    @RequestMapping(value = "parseAiMatCate", method = RequestMethod.POST)
    public Response<Void> parseAiMatCate(AiReqDTO aiReqDTO) {
        List<AiMatCateResultDTO> aiMatCateResultDTOList = JSON.parseArray(aiReqDTO.getJsonResult(), AiMatCateResultDTO.class);
        for (AiMatCateResultDTO aiMatCateResultDTO : aiMatCateResultDTOList) {
            if (aiMatCateResultDTO.getId() != null && aiMatCateResultDTO.getCatId() != null) {
                SrmMatManagementTrPO updatePO = new SrmMatManagementTrPO();
                updatePO.setId(aiMatCateResultDTO.getId());
                updatePO.setMatCate(aiMatCateResultDTO.getCatId());
                srmMatManagementTrRepo.updateCateId(updatePO);
            }
        }
        return Response.ok();
    }

    @ApiOperation("AI物料治理导入")
    @Action(name = "AI物料治理导入", value = "TSRM_AI_MAT_MANAGEMENT_IMPORT_ACTION")
    @RequestMapping(value = "aiMatManageImportAction", method = RequestMethod.POST)
    public Response<Void> aiMatManageImport(SrmMatImportTrDTO srmMatImportTrDTO) throws Exception{
        List<SrmMatManagementTrPO> srmMatManagementTrPOList = new ArrayList<>();

        // 1、下载解析导入文件
        // 下载文件
        URL url = new URL(srmMatImportTrDTO.getImportAttachment());
        URLConnection connection = url.openConnection();
        InputStream inputStream = connection.getInputStream();
        //SrmTsrcItemTrImportDTO templateDTO = JSONObject.parseObject(templateStructObject.toString(), SrmTsrcItemTrImportDTO.class);
        List<SrmMatExcelImportDTO> objects = EasyExcel.read(inputStream).head(SrmMatExcelImportDTO.class).sheet().doReadSync();

        // 查询系统内全部品牌
        List<GenBrandMdPO> brandPOs = MD.queryList(new GenBrandMdPO(), GenBrandMdPO.class);
        List<String> brands = brandPOs.stream().map(GenBrandMdPO::getBrandName).collect(Collectors.toList());

        for (SrmMatExcelImportDTO object : objects) {
            SrmMatManagementTrPO srmMatManagementTrPO = new SrmMatManagementTrPO();
            srmMatManagementTrPO.setMatDesc(object.getMatDesc());
            srmMatManagementTrPO.setMatOriginAttr(object.getMatOriginAttr());

            if (StringUtils.isNotBlank(object.getMatCate())) {
                try {
                    srmMatManagementTrPO.setMatCate(Long.parseLong(object.getMatCate()));
                } catch (NumberFormatException e) {
                    log.error("aiMatManageImport ");
                }
            }
            srmMatManagementTrPOList.add(srmMatManagementTrPO);
        }
        // 分批插入，每10000条插入一次，保持对原始对象的引用以获取ID
        int batchSize = 10000;
        int totalSize = srmMatManagementTrPOList.size();
        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            // 使用subList直接获取原始列表的视图，保持引用关系
            List<SrmMatManagementTrPO> batchList = srmMatManagementTrPOList.subList(i, endIndex);
            srmMatManagementTrRepo.insertBatch(batchList);
        }
        return Response.ok();
    }

    @ApiOperation("构建物料规格属性补全AI请求")
    @Action(name = "构建物料规格属性补全AI请求", value = "TSRM_BUILD_MAT_LIST_FOR_ATTR")
    @RequestMapping(value = "buildMatListForAttr", method = RequestMethod.POST)
    public Response<AiReqDTO> buildMatListForAttr(MatManageCateTotalDTO matManageCateTotalDTO) {
        LambdaQueryWrapper<SrmMatManagementTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SrmMatManagementTrPO::getId, SrmMatManagementTrPO::getMatDesc, SrmMatManagementTrPO::getMatOriginAttr)
                .and(w -> w.eq(SrmMatManagementTrPO::getMatCate, matManageCateTotalDTO.getMatCate()))
                .and(w -> w.eq(SrmMatManagementTrPO::getMatManagementStatus, SrmMatManagementTrMatManagementStatusDict.UNIDENT).or().isNull(SrmMatManagementTrPO::getMatManagementStatus));
        List<SrmMatManagementTrPO> srmMatManagementTrPOList = srmMatManagementTrRepo.selectList(queryWrapper);
        JSONObject matCateInputJson = new JSONObject();
        List<String> attrList = matManageCateTotalDTO.getAttriList().stream().map(MatManageCatAttriDTO::getAttriName).collect(Collectors.toList());

        GenMatCateMdPO leafCate = MD.queryById(matManageCateTotalDTO.getMatCate(), GenMatCateMdPO.class);
        matCateInputJson.put(buildSingleCatePathByDB(leafCate), JSON.toJSONString(attrList));
        List<JSONObject> matListInputJson = new ArrayList<>();
        for (SrmMatManagementTrPO mat : srmMatManagementTrPOList) {
            JSONObject jsonObj = new JSONObject();
            jsonObj.put("id", mat.getId());
            jsonObj.put("name", mat.getMatDesc() + (StringUtils.isNotBlank(mat.getMatOriginAttr()) ? "  " + mat.getMatOriginAttr() : ""));
            matListInputJson.add(jsonObj);
        }
        AiReqDTO aiReqDTO = new AiReqDTO();
        aiReqDTO.setMatCateInputJson(JSON.toJSONString(matCateInputJson));
        aiReqDTO.setMatListInputJson(JSON.toJSONString(matListInputJson));
        return Response.ok(aiReqDTO);
    }

    @ApiOperation("解析物料规格属性补全结果并入库")
    @Action(name = "解析物料规格属性补全结果并入库", value = "TSRM_PARSE_MAT_LIST_FOR_ATTR")
    @RequestMapping(value = "parseMatListForAttr", method = RequestMethod.POST)
    public Response<Void> parseMatListForAttr(AiReqDTO aiReqDTO) {
        String jsonResult = trimAiResult(aiReqDTO.getJsonResult());
        List<AiMatAttrResultDTO> aiMatAttrResultDTOList = JSON.parseArray(jsonResult, AiMatAttrResultDTO.class);
        for (AiMatAttrResultDTO aiMatAttrResultDTO : aiMatAttrResultDTOList) {
            if (aiMatAttrResultDTO.getId() != null) {
                List<String> keyAttrs = aiMatAttrResultDTO.getAttr().entrySet().stream().map(entry -> entry.getKey() + ": "+ entry.getValue()).collect(Collectors.toList());
                SrmMatManagementTrPO srmMatManagementTrPO = new SrmMatManagementTrPO();
                srmMatManagementTrPO.setId(aiMatAttrResultDTO.getId());
                srmMatManagementTrPO.setKeyAttr(String.join("\n", keyAttrs));
                srmMatManagementTrPO.setAttrJson(JSON.toJSONString(aiMatAttrResultDTO.getAttr()));
                srmMatManagementTrRepo.updateById(srmMatManagementTrPO);
            }
        }
        return Response.ok();
    }

    /**
     * 计算总数和各属性数量
     * 判断无法识别 模糊 计算相似度
     * */
    @ApiOperation("处理当前类目下属性结果")
    @Action(name = "处理当前类目下属性结果", value = "TSRM_DEAL_MAT_LIST_ATTR_RESULT")
    @RequestMapping(value = "dealMatListAttrResult", method = RequestMethod.POST)
    public Response<Void> dealMatListAttrResult(MatManageCateTotalDTO matManageCateTotalDTO) {
        // 查询物料类目的属性 编排服务里面查好了传进来
        //LambdaQueryWrapper<MatManageCateTotalPO> totalQueryWrapper = new LambdaQueryWrapper<>();
        //totalQueryWrapper.eq(MatManageCateTotalPO::getMatCate, req.getMatCate());
        //MatManageCateTotalPO matManageCateTotalPO = matManageCateTotalRepo.selectOne(totalQueryWrapper);
        //LambdaQueryWrapper<MatManageCatAttriPO> attrQueryWrapper = new LambdaQueryWrapper<>();
        //attrQueryWrapper.eq(MatManageCatAttriPO::getMatManageCateTotalAttriListId, matManageCateTotalPO.getId());
        //List<MatManageCatAttriPO> matManageCatAttriPOList = matManageCatAttriRepo.selectList(attrQueryWrapper);
        Map<String, BigDecimal> matCateAttrWeightMap = convertToWeightMap(matManageCateTotalDTO.getAttriList());

        // 查询当前类目下的全部待治理物料
        LambdaQueryWrapper<SrmMatManagementTrPO> matQueryWrapper = new LambdaQueryWrapper<>();
        matQueryWrapper.eq(SrmMatManagementTrPO::getMatCate, matManageCateTotalDTO.getMatCate());
        List<SrmMatManagementTrPO> srmMatManagementTrPOList = srmMatManagementTrRepo.selectList(matQueryWrapper);

        if (CollectionUtils.isEmpty(srmMatManagementTrPOList)) {
            return Response.ok();
        }

        // 更新物料总数
        MatManageCateTotalPO matManageCateTotalPO = new MatManageCateTotalPO();
        matManageCateTotalPO.setId(matManageCateTotalDTO.getId());
        matManageCateTotalPO.setMatTotal((long) srmMatManagementTrPOList.size());
        matManageCateTotalRepo.updateById(matManageCateTotalPO);


        // 必填的属性
        List<String> requireAttr = matManageCateTotalDTO.getAttriList().stream().filter(item -> Boolean.TRUE.equals(item.getRequire())).map(MatManageCatAttriDTO::getAttriName).collect(Collectors.toList());

        // 待更新物料治理状态的列表
        List<SrmMatManagementTrPO> srmMatManagementTrPOListUpdateStatus = srmMatManagementTrPOList.stream().filter(item -> item.getAttrJson() == null).map(item -> {
            SrmMatManagementTrPO srmMatManagementTrPO = new SrmMatManagementTrPO();
            srmMatManagementTrPO.setId(item.getId());
            srmMatManagementTrPO.setMatManagementStatus(SrmMatManagementTrMatManagementStatusDict.UNIDENT);
            return srmMatManagementTrPO;
        }).collect(Collectors.toList());

        List<SrmMatManagementTrDTO> srmMatManagementTrDTOList = srmMatManagementTrConverter.po2DtoList(srmMatManagementTrPOList.stream().filter(item -> item.getAttrJson() != null).collect(Collectors.toList()));
        // 属性名 数量
        Map<String, Integer> attrNameCountMap = new HashMap<>();
        Iterator<SrmMatManagementTrDTO> iterator = srmMatManagementTrDTOList.iterator();
        while (iterator.hasNext()) {
            SrmMatManagementTrDTO srmMatManagementTrDTO = iterator.next();
            Map<String, String> attrMap = JSON.parseObject(srmMatManagementTrDTO.getAttrJson(), new TypeReference<Map<String, String>>() {});
            srmMatManagementTrDTO.setAttrMap(attrMap);

            // 检查物料属性是否包含所有必填的属性
            boolean containsAllRequiredAttrs = true;
            for (String requiredAttr : requireAttr) {
                if (!attrMap.containsKey(requiredAttr)) {
                    containsAllRequiredAttrs = false;
                    break;
                }
            }

            // 统计属性名 数量
            for (String attrName : attrMap.keySet()) {
                attrNameCountMap.put(attrName, attrNameCountMap.getOrDefault(attrName, 0) + 1);
            }

            // 如果不包含所有必需属性，则标记为模糊状态并从列表中移除
            if (!containsAllRequiredAttrs) {
                SrmMatManagementTrPO srmMatManagementTrPO = new SrmMatManagementTrPO();
                srmMatManagementTrPO.setId(srmMatManagementTrDTO.getId());
                srmMatManagementTrPO.setMatManagementStatus(SrmMatManagementTrMatManagementStatusDict.UNCLEAR);
                srmMatManagementTrPOListUpdateStatus.add(srmMatManagementTrPO);
                iterator.remove();
            }
        }

        BigDecimal similarityThreshold = matManageCateTotalDTO.getSimilarityThreshold().divide(new BigDecimal("100"));
        // 计算每个物料与其他物料的相似度
        List<SrmMatManagementTrDTO> srmMatManagementTrDTOListWithSimilarity = calculateSimilarity(srmMatManagementTrDTOList, matCateAttrWeightMap, similarityThreshold, srmMatManagementTrPOListUpdateStatus);

        // 更新物料治理状态
        srmMatManagementTrRepo.updateBatch(srmMatManagementTrPOListUpdateStatus);
        // 保存相似列表
        for (SrmMatManagementTrDTO srmMatManagementTrDTO : srmMatManagementTrDTOListWithSimilarity) {
            matManageSimilarListRepo.saveList(matManageSimilarListConverter.dto2PoList(srmMatManagementTrDTO.getSimilarMatList()), srmMatManagementTrDTO.getId());
        }

        // 更新属性数量
        Map<String, MatManageCatAttriDTO>  matManageCatAttriDTOMap = matManageCateTotalDTO.getAttriList().stream().collect(Collectors.toMap(MatManageCatAttriDTO::getAttriName,v->v,(e1,e2)->e1));
        for (Map.Entry<String, Integer> entry : attrNameCountMap.entrySet()) {
            // 已有的属性就更新数量, 没有的属性新增一条
            if (matManageCatAttriDTOMap.containsKey(entry.getKey())) {
                MatManageCatAttriPO matManageCatAttriPO = new MatManageCatAttriPO();
                matManageCatAttriPO.setId(matManageCatAttriDTOMap.get(entry.getKey()).getId());
                matManageCatAttriPO.setAttriNum(new BigDecimal(entry.getValue()));
                matManageCatAttriRepo.updateById(matManageCatAttriPO);
            } else {
                MatManageCatAttriPO matManageCatAttriPO = new MatManageCatAttriPO();
                matManageCatAttriPO.setAttriName(entry.getKey());
                matManageCatAttriPO.setAttriNum(new BigDecimal(entry.getValue()));
                matManageCatAttriPO.setMatManageCateTotalAttriListId(matManageCateTotalDTO.getId());
                matManageCatAttriRepo.insert(matManageCatAttriPO);
            }
        }

        return Response.ok();
    }

    /**
     * ai的返回有时候在json后面加一些说明,这里加一下过滤
     * */
    private String trimAiResult(String jsonResult) {
        // 查找最后一个']'的位置
        int lastBracketIndex = jsonResult.lastIndexOf(']');
        // 如果找到了']'且不是字符串的最后一个字符
        if (lastBracketIndex != -1 && lastBracketIndex < jsonResult.length() - 1) {
            // 提取']'后面的多余内容并记录
            String extraContent = jsonResult.substring(lastBracketIndex + 1);
            log.warn("MatManageAction JSON字符串包含额外内容: {}", extraContent);
            jsonResult = jsonResult.substring(0, lastBracketIndex + 1);
        }

        // 处理'['前面可能存在的多余字符
        int firstBracketIndex = jsonResult.indexOf('[');
        if (firstBracketIndex > 0) {
            // 提取'['前面的多余内容并记录
            String prefixContent = jsonResult.substring(0, firstBracketIndex);
            log.warn("MatManageAction JSON字符串前缀包含额外内容: {}", prefixContent);
            jsonResult = jsonResult.substring(firstBracketIndex);
        }
        return jsonResult;
    }

    /**
     * 将属性列表转换为带权重的Map
     * @param attrList 属性列表
     * @return 权重Map，key为属性名，value为权重值
     */
    private Map<String, BigDecimal> convertToWeightMap(List<MatManageCatAttriDTO> attrList) {
        Map<String, BigDecimal> weightMap = new HashMap<>();

        // 计算总权重
        BigDecimal totalWeight = BigDecimal.ZERO;
        for (MatManageCatAttriDTO attr : attrList) {
            // 必填属性权重为2，非必填为1
            BigDecimal weight = Boolean.TRUE.equals(attr.getRequire()) ?
                    new BigDecimal("2.15") : BigDecimal.ONE;
            totalWeight = totalWeight.add(weight);
        }

        // 计算归一化权重并放入Map
        for (MatManageCatAttriDTO attr : attrList) {
            BigDecimal weight = Boolean.TRUE.equals(attr.getRequire()) ?
                    new BigDecimal("2.15") : BigDecimal.ONE;
            // 归一化处理，使所有权重之和为1
            BigDecimal normalizedWeight = weight.divide(totalWeight, 6, RoundingMode.HALF_UP);
            weightMap.put(attr.getAttriName(), normalizedWeight);
        }

        return weightMap;
    }

    /**
     * 计算物料之间的相似度
     * @param matList 物料列表
     * @param weightMap 属性权重Map
     */
    private List<SrmMatManagementTrDTO> calculateSimilarity(List<SrmMatManagementTrDTO> matList, Map<String, BigDecimal> weightMap, BigDecimal similarityThreshold, List<SrmMatManagementTrPO> srmMatManagementTrPOListUpdateStatus) {
        List<SrmMatManagementTrDTO> srmMatManagementTrDTOList = new ArrayList<>(matList.size());
        for (int i = 0; i < matList.size(); i++) {
            SrmMatManagementTrDTO currentMat = matList.get(i);
            if (currentMat.getId() == null) continue;

            SrmMatManagementTrDTO srmMatManagementTrDTO = new SrmMatManagementTrDTO();
            srmMatManagementTrDTO.setId(currentMat.getId());

            List<MatManageSimilarListDTO> similarMats = new ArrayList<>();

            for (int j = 0; j < matList.size(); j++) {
                if (i == j) continue; // 跳过自己

                SrmMatManagementTrDTO compareMat = matList.get(j);

                // 计算两个物料之间的相似度
                BigDecimal similarity = calculateMatSimilarity(currentMat.getAttrMap(), compareMat.getAttrMap(), weightMap);

                // 如果相似度大于阈值，加入结果列表
                if (similarity.compareTo(similarityThreshold) >= 0) {
                    MatManageSimilarListDTO matManageSimilarListDTO = new MatManageSimilarListDTO();
                    matManageSimilarListDTO.setSimilarity(similarity);
                    matManageSimilarListDTO.setSimilarMat(compareMat.getId());
                    matManageSimilarListDTO.setSrmMatManagementTrSimilarMatListId(currentMat.getId());
                    similarMats.add(matManageSimilarListDTO);
                }
            }

            if (CollectionUtils.isNotEmpty(similarMats)) {
                // 按相似度降序排序
                similarMats.sort((a, b) -> b.getSimilarity().compareTo(a.getSimilarity()));
                srmMatManagementTrDTO.setSimilarMatList(similarMats);
                srmMatManagementTrDTOList.add(srmMatManagementTrDTO);
                // 设置状态为一物多码
                SrmMatManagementTrPO srmMatManagementTrPO = new SrmMatManagementTrPO();
                srmMatManagementTrPO.setId(currentMat.getId());
                srmMatManagementTrPO.setMatManagementStatus(SrmMatManagementTrMatManagementStatusDict.MULTI_CODE);
                srmMatManagementTrPOListUpdateStatus.add(srmMatManagementTrPO);
            } else {
                // 如果没有相似物料,在这里就认为是标准产品 后续可以直接创建物料
                SrmMatManagementTrPO srmMatManagementTrPO = new SrmMatManagementTrPO();
                srmMatManagementTrPO.setId(currentMat.getId());
                srmMatManagementTrPO.setMatManagementStatus(SrmMatManagementTrMatManagementStatusDict.IDENTIFIED);
                srmMatManagementTrPOListUpdateStatus.add(srmMatManagementTrPO);
            }
        }
        return srmMatManagementTrDTOList;
    }

    /**
     * 计算两个物料属性之间的相似度
     * @param attr1 物料1的属性Map
     * @param attr2 物料2的属性Map
     * @param weightMap 属性权重Map
     * @return 相似度值(0-1)
     */
    private BigDecimal calculateMatSimilarity(Map<String, String> attr1, Map<String, String> attr2, Map<String, BigDecimal> weightMap) {
        BigDecimal similarity = BigDecimal.ZERO;

        // 遍历属性权重Map中的每个属性
        for (Map.Entry<String, BigDecimal> entry : weightMap.entrySet()) {
            String attrName = entry.getKey();
            BigDecimal weight = entry.getValue();

            // 如果两个物料都有这个属性且值相同，则加上对应的权重
            if (attr1.containsKey(attrName) && attr2.containsKey(attrName) &&
                    Objects.equals(attr1.get(attrName), attr2.get(attrName))) {
                similarity = similarity.add(weight);
            }
        }

        return similarity;
    }

    /**
     * 非同一物料处理
     * */
    @ApiOperation("非同一物料处理")
    @Action(name = "非同一物料处理", value = "TSRM_NOT_SAME_MAT_ACTION")
    @RequestMapping(value = "notSameMat", method = RequestMethod.POST)
    public Response<Void> notSameMat(MatManageSimilarListDTO matManageSimilarListDTO) {
        matManageSimilarListRepo.deleteById(matManageSimilarListDTO.getId());
        return Response.ok();
    }

    /**
     * 同一物料处理
     * */
    @ApiOperation("同一物料处理")
    @Action(name = "同一物料处理", value = "TSRM_IS_SAME_MAT_ACTION")
    @RequestMapping(value = "isSameMat", method = RequestMethod.POST)
    public Response<Void> isSameMat(MatManageSimilarListDTO matManageSimilarListDTO) {
        // 相似物料表标记成同一物料
        MatManageSimilarListPO matManageSimilarListPO = new MatManageSimilarListPO();
        matManageSimilarListPO.setId(matManageSimilarListDTO.getId());
        matManageSimilarListPO.setSameMat(true);
        matManageSimilarListRepo.updateById(matManageSimilarListPO);
        // 主物料的属性复制给当前物料
        SrmMatManagementTrPO mainMat = srmMatManagementTrRepo.selectById(matManageSimilarListDTO.getSrmMatManagementTrSimilarMatListId());
        SrmMatManagementTrPO similarMat = srmMatManagementTrRepo.selectById(matManageSimilarListDTO.getSimilarMat());
        similarMat.setKeyAttr(mainMat.getKeyAttr());
        similarMat.setMatManagementStatus(SrmMatManagementTrMatManagementStatusDict.IDENTIFIED);
        srmMatManagementTrRepo.updateById(similarMat);
        return Response.ok();
    }

    @ApiOperation("构建物料规范识别AI请求")
    @Action(name = "构建物料规范识别AI请求", value = "TSRM_BUILD_MAT_LIST_FOR_STANDARD")
    @RequestMapping(value = "buildMatListForStandard", method = RequestMethod.POST)
    public Response<AiReqDTO> buildMatListForStandard(MatManageCateTotalDTO matManageCateTotalDTO) {
        LambdaQueryWrapper<SrmMatManagementTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SrmMatManagementTrPO::getId, SrmMatManagementTrPO::getMatDesc, SrmMatManagementTrPO::getMatOriginAttr)
                .and(w -> w.eq(SrmMatManagementTrPO::getMatCate, matManageCateTotalDTO.getMatCate()));
        List<SrmMatManagementTrPO> srmMatManagementTrPOList = srmMatManagementTrRepo.selectList(queryWrapper);
        //JSONObject matCateInputJson = new JSONObject();
        //List<String> attrList = matManageCateTotalDTO.getAttriList().stream().map(MatManageCatAttriDTO::getAttriName).collect(Collectors.toList());

        //GenMatCateMdPO leafCate = MD.queryById(matManageCateTotalDTO.getMatCate(), GenMatCateMdPO.class);
        //matCateInputJson.put(buildSingleCatePathByDB(leafCate), JSON.toJSONString(attrList));
        List<JSONObject> matListInputJson = new ArrayList<>();
        for (SrmMatManagementTrPO mat : srmMatManagementTrPOList) {
            JSONObject jsonObj = new JSONObject();
            jsonObj.put("id", mat.getId());
            jsonObj.put("name", mat.getMatDesc() + (StringUtils.isNotBlank(mat.getMatOriginAttr()) ? "  " + mat.getMatOriginAttr() : ""));
            matListInputJson.add(jsonObj);
        }
        AiReqDTO aiReqDTO = new AiReqDTO();
        //aiReqDTO.setMatCateInputJson(JSON.toJSONString(matCateInputJson));
        aiReqDTO.setMatListInputJson(JSON.toJSONString(matListInputJson));
        return Response.ok(aiReqDTO);
    }

    @ApiOperation("解析物料规范识别结果并入库")
    @Action(name = "解析物料规范识别结果并入库", value = "TSRM_PARSE_MAT_LIST_FOR_STANDARD")
    @RequestMapping(value = "parseMatListForStandard", method = RequestMethod.POST)
    public Response<Void> parseMatListForStandard(AiReqDTO aiReqDTO) {
        String jsonResult = trimAiResult(aiReqDTO.getJsonResult());
        List<AiMatStandardResultDTO> aiMatAttrResultDTOList = JSON.parseArray(jsonResult, AiMatStandardResultDTO.class);
        for (AiMatStandardResultDTO aiMatStandardResultDTO : aiMatAttrResultDTOList) {
            if (aiMatStandardResultDTO.getId() != null) {
                SrmMatManagementTrPO srmMatManagementTrPO = new SrmMatManagementTrPO();
                srmMatManagementTrPO.setId(aiMatStandardResultDTO.getId());
                srmMatManagementTrPO.setProblem(aiMatStandardResultDTO.getProblem());
                srmMatManagementTrPO.setSuggest(aiMatStandardResultDTO.getSuggest());
                srmMatManagementTrRepo.updateById(srmMatManagementTrPO);
            }
        }
        return Response.ok();
    }

    public static void main(String[] args) {
        String result = "[{\"attriName\":\"载重指数\",\"require\":true},{\"attriName\":\"层级强度\",\"require\":true},{\"attriName\":\"花纹类型\",\"require\":false},{\"attriName\":\"轮胎尺寸\",\"require\":false},{\"attriName\":\"速度级别\",\"require\":false}]";
        List<MatManageCatAttriPO> attrList = JSON.parseArray(result, MatManageCatAttriPO.class);
        //System.out.println(convertToWeightMap(attrList));
    }
}
