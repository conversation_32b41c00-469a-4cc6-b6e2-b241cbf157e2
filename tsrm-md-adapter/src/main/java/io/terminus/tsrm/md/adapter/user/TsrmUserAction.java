package io.terminus.tsrm.md.adapter.user;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.md.infrastructure.repo.org.OrgEmployeeMdRepo;
import io.terminus.erp.md.spi.model.po.org.OrgEmployeeMdPO;
import io.terminus.iam.api.request.role.RoleByKeyFindParams;
import io.terminus.iam.api.request.role.UserRoleRelationCreateParams;
import io.terminus.iam.api.request.user.CompleteUserParams;
import io.terminus.iam.api.request.user.UserPagingParams;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.user.User;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.tsrm.md.app.user.TsrmUserService;
import io.terminus.tsrm.md.spi.dict.org.OrgEmployeeMdStatusDict;
import io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertMdDTO;
import io.terminus.tsrm.md.spi.model.org.dto.OrgEmployeeBaseMdDTO;
import io.terminus.tsrm.md.spi.model.org.dto.OrgEmployeeMdDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * tsrm 用户 action
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Api(tags = "tsrm用户")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/tsrm/md/user")
@Slf4j
public class TsrmUserAction {

    private final TsrmUserService tsrmUserService;

    private final OrgEmployeeMdRepo orgEmployeeMdRepo;

    private final PortalToIamAppConverter portalToEndpointUtil;

    @ApiOperation("用户管理-员工重置密码")
    @Action(name = "用户管理-员工重置密码", value = "TSRM_USER_RESET_PASSWORD_ACTION")
    @PostMapping(value = "/userManageResetPassword")
    public Response<Void> userManageResetPassword(@RequestBody OrgEmployeeMdDTO request) {
        CompleteUserParams params = new CompleteUserParams();
        params.setPassword("Aa123456");
        params.setId(request.getUserId());
        try {
            tsrmUserService.resetPassword(params);
        } catch (BusinessException e) {
            if (!e.getMessage().equals("新密码不能和上次密码一样")) {
                throw e;
            }
        }
        return Response.ok();
    }

    @ApiOperation("用户管理-专家重置密码")
    @Action(name = "用户管理-专家重置密码", value = "TSRM_EXPERT_RESET_PASSWORD_ACTION")
    @PostMapping(value = "/expertManageResetPassword")
    public Response<Void> expertManageResetPassword(@RequestBody SrmExpertMdDTO request) {
        CompleteUserParams params = new CompleteUserParams();
        params.setPassword("Aa123456");
        if (request.getEmployeeId() != null) {
            params.setId(request.getEmployeeId().getUserId());
        } else {
            params.setId(request.getUser());
        }
        try {
            tsrmUserService.resetPassword(params);
        } catch (BusinessException e) {
            if (!e.getMessage().equals("新密码不能和上次密码一样")) {
                throw e;
            }
        }
        return Response.ok();
    }

    @ApiOperation("用户管理-新建员工自动关联角色")
    @Action(name = "用户管理-新建员工自动关联角色", value = "TSRM_USER_CREATE_WITH_ROLE_ACTION")
    @PostMapping(value = "/userCreateWithRole")
    public Response<Void> userCreateWithRole(@RequestBody OrgEmployeeBaseMdDTO request) {
        UserPagingParams params = new UserPagingParams();
        params.setMobile(request.getMobile());
        List<User> users = tsrmUserService.queryUser(params);
        if (null != users && users.size() > 0) {
            UserRoleRelationCreateParams createParams = new UserRoleRelationCreateParams();
            createParams.setUserId(users.get(0).getId());
            //根据key查询角色
            RoleByKeyFindParams findParams = new RoleByKeyFindParams();
            findParams.setKey("PUREMPLOYEE");
            Role role = tsrmUserService.listRole(findParams);
            createParams.setRoleIds(new ArrayList<>(Arrays.asList(role.getId())));
            tsrmUserService.createRoleRelation(createParams);
        }
        return Response.ok();
    }

    @ApiOperation("用户管理-启用员工")
    @Action(name = "用户管理-启用员工", value = "TSRM_USER_ENABLED_EMPLOYEE_ACTION")
    @PostMapping(value = "/userEnabledEmployee")
    public Response<Void> userEnabledEmployee(@RequestBody OrgEmployeeMdDTO request) {
        OrgEmployeeMdPO orgEmployeeMdPO = orgEmployeeMdRepo.selectById(request.getId());
//        orgEmployeeMdPO.setStatus(OrgEmployeeMdStatusDict.ENABLED);
//        orgEmployeeMdRepo.updateById(orgEmployeeMdPO);
        User user = tsrmUserService.queryById(orgEmployeeMdPO.getUserId());
        if (null != user) {
            tsrmUserService.enableUser(user.getId());
        }
        return Response.ok();
    }

    @ApiOperation("用户管理-停用员工")
    @Action(name = "用户管理-停用员工", value = "TSRM_USER_DISABLED_EMPLOYEE_ACTION")
    @PostMapping(value = "/userDisabledEmployee")
    public Response<Void> userDisabledEmployee(@RequestBody OrgEmployeeMdDTO request) {
        OrgEmployeeMdPO orgEmployeeMdPO = orgEmployeeMdRepo.selectById(request.getId());
        orgEmployeeMdPO.setStatus(OrgEmployeeMdStatusDict.DISABLED);
        orgEmployeeMdPO.setResignationAt(LocalDateTime.now());
        orgEmployeeMdRepo.updateById(orgEmployeeMdPO);
        User user = tsrmUserService.queryById(orgEmployeeMdPO.getUserId());
        if (null != user) {
            tsrmUserService.disableUser(user.getId());
        }
        return Response.ok();
    }

    @ApiOperation("用户管理-查询当前用户所属的门户id")
    @Action(name = "用户管理-查询当前用户所属的门户id", value = "TSRM_GET_USER_ENDPOINT_ACTION")
    @PostMapping(value = "/getUserEndpoint")
    public Response<OrgEmployeeMdDTO> getUserEndpoint(@RequestBody OrgEmployeeMdDTO request) {
        Long iamAppIdByPortalCode = portalToEndpointUtil.getIamAppIdByPortalCode(TrantorContext.getPortalCode());
        OrgEmployeeMdDTO result = new OrgEmployeeMdDTO();
        result.setId(iamAppIdByPortalCode);
        return Response.ok(result);
    }
}
