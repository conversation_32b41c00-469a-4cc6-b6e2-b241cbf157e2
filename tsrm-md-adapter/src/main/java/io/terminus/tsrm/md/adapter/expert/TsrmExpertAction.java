package io.terminus.tsrm.md.adapter.expert;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.iam.api.request.user.CompleteUserParams;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import io.terminus.tsrm.md.app.expert.TsrmExpertService;
import io.terminus.tsrm.md.app.user.TsrmUserService;
import io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertMdDTO;
import io.terminus.tsrm.md.spi.model.org.dto.OrgEmployeeMdDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * tsrm 专家 action
 *
 * <AUTHOR>
 * @date 2024/12/24
 */
@Api(tags = "tsrm专家")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/tsrm/md/expert")
@Slf4j
public class TsrmExpertAction {

    private final TsrmExpertService tsrmExpertService;

    @ApiOperation("专家管理-新建专家同步创建用户")
    @Action(name = "专家管理-新建专家同步创建用户", value = "TSRM_EXPERT_CREATE_WITH_USER_ACTION")
    @PostMapping(value = "/expertCreateWithUser")
    public Response<Void> expertCreateWithUser(@RequestBody SrmExpertMdDTO request) {
        tsrmExpertService.createExpert(request);
        return Response.ok();
    }

    @ApiOperation("专家管理-更新外部专家同步更新用户")
    @Action(name = "专家管理-更新外部专家同步更新用户", value = "TSRM_OUTEREXPERT_UPDATE_WITH_USER_ACTION")
    @PostMapping(value = "/outerExpertUpdateWithUser")
    public Response<Void> outerExpertUpdateWithUser(@RequestBody SrmExpertMdDTO request) {
        tsrmExpertService.outerExpertUpdateWithUser(request);
        return Response.ok();
    }

    @ApiOperation("专家管理-启用专家")
    @Action(name = "专家管理-启用专家", value = "TSRM_EXPERT_ENABLE_ACTION")
    @PostMapping(value = "/enableExpert")
    public Response<Void> enableExpert(@RequestBody SrmExpertMdDTO request) {
        tsrmExpertService.enableExpert(request);
        return Response.ok();
    }

    @ApiOperation("专家管理-停用专家")
    @Action(name = "专家管理-停用专家", value = "TSRM_EXPERT_DISABLE_ACTION")
    @PostMapping(value = "/disableExpert")
    public Response<Void> disableExpert(@RequestBody SrmExpertMdDTO request) {
        tsrmExpertService.disableExpert(request);
        return Response.ok();
    }

    @ApiOperation("专家管理-删除专家")
    @Action(name = "专家管理-删除专家", value = "TSRM_EXPERT_DELETE_ACTION")
    @PostMapping(value = "/deleteExpert")
    public Response<Void> deleteExpert(@RequestBody SrmExpertMdDTO request) {
        tsrmExpertService.deleteExpert(request);
        return Response.ok();
    }

}
