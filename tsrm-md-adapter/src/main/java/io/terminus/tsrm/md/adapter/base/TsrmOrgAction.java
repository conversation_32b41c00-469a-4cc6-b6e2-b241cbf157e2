package io.terminus.tsrm.md.adapter.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor.org.spi.model.po.OrgRelationCfPO;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import io.terminus.tsrm.md.app.org.OrgService;
import io.terminus.tsrm.md.infrastructure.repo.gateway.OrgMdGateWay;
import io.terminus.tsrm.md.infrastructure.repo.org.OrgEmployeeOrgLinkCfRepo;
import io.terminus.tsrm.md.spi.model.org.dto.*;
import io.terminus.tsrm.md.spi.model.org.po.OrgEmployeeOrgLinkCfPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * tsrm 组织 action
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Api(tags = "tsrm组织")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/tsrm/md/org")
@Slf4j
public class TsrmOrgAction {

    private final OrgMdGateWay orgMdGateWay;

    private final OrgService orgService;

    private final OrgEmployeeOrgLinkCfRepo orgEmployeeOrgLinkCfRepo;

    @ApiOperation("组织管理-保存组织")
    @Action(name = "组织管理-保存组织", value = "TSRM_ORG_STRUCT_MD_SAVE_ACTION")
    @PostMapping(value = "/saveOrgStruct")
    public Response<OrgStructMdSaveDTO> saveOrgStruct(@RequestBody OrgStructMdSaveDTO request) {
        request.setOrgEnableDate(LocalDateTime.now());
        request.setOrgStatus("ENABLED");
        OrgStructMdSaveDTO result = new OrgStructMdSaveDTO();
        result = orgMdGateWay.save(request);
//        IdRequest idRequest = new IdRequest();
//        idRequest.setId(result.getId());
//        orgMdGateWay.enabled(idRequest);
        return Response.ok(result);
    }

    @ApiOperation("组织管理-新建员工组织关联关系")
    @Action(name = "组织管理-新建员工组织关联关系", value = "TSRM_ORG_EMPLOYEE_LINK_CREATE_ACTION")
    @PostMapping(value = "/createOrgEmployeeOrgLinkCf")
    public Response<OrgEmployeeOrgLinkCfPO> createOrgEmployeeOrgLinkCf(@RequestBody OrgEmployeeOrgLinkCfPO request) {
        OrgEmployeeOrgLinkCfPO result = new OrgEmployeeOrgLinkCfPO();
        Integer insertId = orgEmployeeOrgLinkCfRepo.insert(request);
        result.setId(Long.valueOf(insertId));
        return Response.ok(result);
    }

    @ApiOperation("组织管理-保存组织时校验关联关系")
    @Action(name = "组织管理-保存组织时校验关联关系", value = "TSRM_ORG_STRUCT_MD_VALIDATE_UNIT_ACTION")
    @PostMapping(value = "/saveOrgStructValidateUnit")
    public Response<OrgRelationCfPO> saveOrgStructValidateUnit(@RequestBody List<OrgRelationCfPO> request) {
        HashSet<Long> seenOrgRelationUnitIds = new HashSet<>();
        for (OrgRelationCfPO line : request) {
            Long orgRelationUnitId = line.getOrgRelationUnitId();
            if (!seenOrgRelationUnitIds.add(orgRelationUnitId)) {
                // 如果 add 返回 false，说明该 orgRelationUnitId 已经存在
                throw new BusinessException("MD.org.duplicate");
            }
        }
        return Response.ok(null);
    }

    @ApiOperation("组织管理-分页查询部门所服务公司")
    @Action(name = "组织管理-分页查询部门所服务公司", value = "TSRM_PAGING_COMPANY_BY_DEPARTMENT_ACTION")
    @PostMapping(value = "/pagingCompanyByDepartment")
    public Response<Paging<OrgStructMdDetailDTO>> pagingCompanyByDepartment(@RequestBody OrgQueryRequestDTO request) {
        Paging<OrgStructMdDetailDTO> result = orgService.pagingCompanyByDepartment(request.getDepartmentId(), request.getPageNo(), request.getPageSize());
//        if (result.getTotal() == 0) {
//            OrgStructPageQueryDTO pageQueryDTO = new OrgStructPageQueryDTO();
//            pageQueryDTO.setOrgBusinessTypeCode("COM_ORG");
//            pageQueryDTO.setOrgStatusSet(new HashSet<>(Collections.singleton("ENABLED")));
//            result = orgMdGateWay.paging(pageQueryDTO).getData();
//        }
        return Response.ok(result);
    }

    @ApiOperation("员工模型转成组织和身份")
    @Action(name = "员工模型转成组织和身份", value = "EMPLOYEE_CONVERT_ORG_AND_IDENTITY_ACTION")
    @RequestMapping(value = "employeeConvertOrgAndIdentityAction", method = RequestMethod.POST)
    public Response<OrgAndIdentityResDTO> employeeConvertOrgAndIdentity(OrgEmployeeMdDTO employeeDTO) {
        OrgAndIdentityResDTO orgAndIdentityResDTO = new OrgAndIdentityResDTO();
        if (CollectionUtils.isNotEmpty(employeeDTO.getEmployeeOrgLinkList())) {
            List<OrgAndIdentityDTO> mainOrgList = employeeDTO.getEmployeeOrgLinkList().stream().filter(org -> org.getIsMainOrg() && org.getOrgUnitId() != null)
                    .map(org -> {
                        OrgAndIdentityDTO orgAndIdentityDTO = new OrgAndIdentityDTO();
                        orgAndIdentityDTO.setId(org.getOrgUnitId().getId());
                        orgAndIdentityDTO.setOrgId(org.getOrgUnitId().getId());
                        orgAndIdentityDTO.setOrgCode(org.getOrgUnitId().getOrgCode());
                        orgAndIdentityDTO.setOrgName(org.getOrgUnitId().getOrgName());
                        if (org.getIdentityId() != null) {
                            orgAndIdentityDTO.setIdentityName(org.getIdentityId().getName());
                        }
                        return orgAndIdentityDTO;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mainOrgList)) {
                orgAndIdentityResDTO.setOrgList(mainOrgList);
                OrgAndIdentityDTO firstOrg = mainOrgList.get(0);
                orgAndIdentityResDTO.setOrg(firstOrg);
                orgAndIdentityResDTO.setIdentityName(firstOrg.getIdentityName());
            }
        }
        return Response.ok(orgAndIdentityResDTO);
    }
}
