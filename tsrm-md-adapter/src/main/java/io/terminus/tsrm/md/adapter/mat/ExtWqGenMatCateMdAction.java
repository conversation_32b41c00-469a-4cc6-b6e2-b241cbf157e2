package io.terminus.tsrm.md.adapter.mat;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.erp.md.spi.model.po.base.GenMatCateMdPO;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import io.terminus.tsrm.md.app.mat.ExtWqGenMatCateMdService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "类目")
@RequiredArgsConstructor
@RestController
@RequestMapping("/wq/md/category/md")
public class ExtWqGenMatCateMdAction {

    private final ExtWqGenMatCateMdService extWqGenMatCateMdService;

    @ApiOperation("类目停用")
    @Action(name = "类目停用", value = "CATE_DISABLE_ACTION")
    @RequestMapping(value = "categoryDisableAction", method = RequestMethod.POST)
    public Response<Void> categoryDisable(GenMatCateMdPO genMatCateMdPO) {
        extWqGenMatCateMdService.disable(genMatCateMdPO.getId());
        return Response.ok();
    }
}
