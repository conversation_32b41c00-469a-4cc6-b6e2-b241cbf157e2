package io.terminus.tsrm.md.adapter.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.tsrm.md.app.org.OrgOpenSyncService;
import io.terminus.tsrm.md.spi.model.sync.dto.OrgSyncDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @time 2025/8/26 11:02
 */
@Api(tags = "供应商同步晶澳数据")
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/api/tsrm/md/org/open/sync")
public class OrgOpenSyncController {

    private final OrgOpenSyncService orgOpenSyncService;

    @ApiOperation("VEND-同步新增组织")
    @PostMapping(value = "addOrg")
    public Response<Void> addOrg(@RequestBody OrgSyncDTO orgSyncDTO, HttpServletResponse response) {
        try {
            log.info("接收到组织同步请求: {}", orgSyncDTO);
            orgOpenSyncService.addOrg(orgSyncDTO);
            log.info("组织同步成功: {}", orgSyncDTO.getOrganizationUuid());
            return Response.ok();
        } catch (Exception e) {
            log.error("组织同步失败: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return Response.fail(e.getMessage());
        }
    }
}
