package io.terminus.tsrm.md.adapter.mat;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.erp.md.spi.model.po.base.GenMatCateMdPO;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import io.terminus.tsrm.md.app.mat.MatCateExtService;
import io.terminus.tsrm.md.app.mat.MatExtService;
import io.terminus.tsrm.md.spi.model.mat.dto.MatSyncDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 物料 action
 *
 * <AUTHOR>
 * @date 2023/9/27
 */
@Api(tags = "物料")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/wq/md/mat")
@Slf4j
public class ExtWqMatAction {
    private final MatCateExtService matCateExtService;

    private final MatExtService matExtService;

    @ApiOperation("查询物料分类全部上级类目")
    @Action(name = "查询物料分类全部上级类目", value = "QUERY_MAT_CATE_PARENT_ACTION")
    @RequestMapping(value = "queryMatCateParent", method = RequestMethod.POST)
    public Response<List<GenMatCateMdPO>> queryMatCateParent(GenMatCateMdPO matCateDTO) {
        return Response.ok(matCateExtService.queryMatCateParent(matCateDTO));
    }


    @ApiOperation("物料同步BPM")
    @Action(name = "物料同步BPM", value = "BMP_SYNC")
    @RequestMapping(value = "sync", method = RequestMethod.POST)
    public Response<Boolean> sync(@RequestBody List<MatSyncDTO> matSyncDTOList) {
        log.info("ExtWqMatAction::sync,request:{}", JSON.toJSONString(matSyncDTOList));
        return Response.ok(matExtService.sync(matSyncDTOList));
    }
}
