# 下载模块元数据配置
[[meta.download]]
name = "dev-tsrm-mal"
# 下载元数据的console地址
consoleUrl = "t-erp-console-dev.app.terminus.io"
# 下载元数据的iam地址
iamUrl = "t-erp-console-iam-dev.app.terminus.io"
# 模块teamId
teamId = 22
# 模块分支id
branchId = 22
# 模块key
moduleKey = "TSRM"
# 同步元数据是否按照目录同步，如果目录有值，则只会同步该目录下的元数据
# 如果只需要同步多个子目录 eg:["采购/申请","采购/配置"]
folder = ["M_主数据"]

[[meta.download]]
name = "test-tsrm-mal"
# 下载元数据的console地址
consoleUrl = "t-erp-console-test.app.terminus.io"
# 下载元数据的iam地址
iamUrl = "t-erp-console-iam-test.app.terminus.io"
# 模块teamId
teamId = 22
# 模块分支id
branchId = 22
# 模块key
moduleKey = "TSRM"
# 同步元数据是否按照目录同步，如果目录有值，则只会同步该目录下的元数据
# 如果只需要同步多个子目录 eg:["采购/申请","采购/配置"]
folder = ["M_主数据"]

# 上传模块元数据配置
[[meta.upload]]
name = "test-tsrm-mal"
# 上传元数据的console地址
consoleUrl = "t-erp-console-test.app.terminus.io"
# 上传元数据的iam地址
iamUrl = "t-erp-console-iam-test.app.terminus.io"
# 模块teamId
teamId = 22
# 模块分支id
branchId = 22
# 模块key
moduleKey = "TSRM"
# 只会上传该目录下的元数据
folder = ["M_主数据"]