spring:
  application:
    name: tsrm-md
mybatis-plus:
  type-aliases-package:
    io.terminus.tsrm.md.spi.model.po
swagger2:
  base-package: io.terminus.tsrm.md

# 通过erda部署后，下列配置通过addon注入环境变量或通过环境配置进行管理，无需在yml中声明，仅在本地启动时使用

# 接口文档
SWAGGER2_ENABLED: true
# 数据库
MYSQL_HOST: localhost
MYSQL_PORT: 3306
MYSQL_DATABASE: tsrm-md
MYSQL_USERNAME: root
MYSQL_PASSWORD: root
# 缓存
REDIS_HOST: localhost
REDIS_PORT: 6379
REDIS_PASSWORD: 123456
# 消息队列
MQ_CLIENT_TYPE: ROCKETMQ
MQ_SERVER_ADDRESS: localhost:9876
MQ_TOPIC: GID_T_ERP
MQ_CONSUMER_GROUP: tsrm-md