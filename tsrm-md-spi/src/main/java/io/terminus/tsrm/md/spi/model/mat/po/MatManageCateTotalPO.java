package io.terminus.tsrm.md.spi.model.mat.po;


import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (MatManageCateTotal)存储模型
 *
 * <AUTHOR>
 * @since  2025-04-28 16:05:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "mat_manage_cate_total")
public class MatManageCateTotalPO extends BaseModel {
    private static final long serialVersionUID = -97689867819208810L;

    @ApiModelProperty("物料类目")
    @TableField("`mat_cate`")
    private Long matCate;

    @ApiModelProperty("物料总数")
    @TableField("`mat_total`")
    private Long matTotal;

    @ApiModelProperty("属性数量")
    @TableField("`attri_total`")
    private Long attriTotal;

    @ApiModelProperty("相似度阈值")
    @TableField("`similarity_threshold`")
    private BigDecimal similarityThreshold;

}
