package io.terminus.tsrm.md.spi.model.org.po;


import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 员工信息表(OrgEmployeeMd)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:02:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_employee_md")
public class OrgEmployeeMdPO extends BaseModel {
    private static final long serialVersionUID = 988180215651730530L;

    @ApiModelProperty("用户")
    @TableField("`user_id`")
    private Long userId;

    @ApiModelProperty("员工编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("姓名")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

    @ApiModelProperty("员工类型")
    @TableField("`type`")
    private String type;

    @ApiModelProperty("邮箱")
    @TableField("`email`")
    private String email;

    @ApiModelProperty("手机")
    @TableField("`mobile`")
    private String mobile;

    @ApiModelProperty("地址")
    @TableField("`address_id`")
    private Long addressId;

    @ApiModelProperty("详细地址")
    @TableField("`address_detail`")
    private String addressDetail;

    @ApiModelProperty("入职日期")
    @TableField("`entry_at`")
    private LocalDateTime entryAt;

    @ApiModelProperty("离职日期")
    @TableField("`resignation_at`")
    private LocalDateTime resignationAt;

}
