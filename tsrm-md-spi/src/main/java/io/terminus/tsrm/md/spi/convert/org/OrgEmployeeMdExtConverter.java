package io.terminus.tsrm.md.spi.convert.org;


import io.terminus.erp.md.spi.model.dto.org.OrgPurOrgTmLinkDTO;
import io.terminus.tsrm.md.spi.model.org.dto.ExtWqEmployeeAdmOrgCfDTO;
import io.terminus.tsrm.md.spi.model.org.dto.ExtWqEmployeePurOrgCfDTO;
import io.terminus.tsrm.md.spi.model.org.dto.OrgEmployeeMdExtDTO;
import io.terminus.tsrm.md.spi.model.org.po.OrgEmployeeMdPO;
import org.apache.commons.collections.MapUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 员工信息表(OrgEmployeeMd)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-27 11:23:44
 */
@Mapper(componentModel = "spring")
public interface OrgEmployeeMdExtConverter {

    @Mapping(target = "extra", expression = "java(convertToExtraMap(dto))")
    OrgPurOrgTmLinkDTO convert(OrgEmployeeMdExtDTO dto);

    default OrgEmployeeMdExtDTO convert(OrgEmployeeMdPO po) {
        OrgEmployeeMdExtDTO res = new OrgEmployeeMdExtDTO();
        this.convert(res, po);
        return res;
    }

    List<OrgPurOrgTmLinkDTO> convertToDtoList(List<OrgEmployeeMdExtDTO> req);

    List<OrgEmployeeMdExtDTO> convertToExtDtoList(List<OrgEmployeeMdPO> req);


    @Mapping(target = "extra", expression = "java(convertDtoExtFields(dto,po))")
    void convert(@MappingTarget OrgEmployeeMdExtDTO dto, OrgEmployeeMdPO po);

    default Map<String, Object> convertToExtraMap(OrgEmployeeMdExtDTO dto){
        return this.buildExtraMap(dto);
    }

    default Map<String, Object> convertDtoExtFields(@MappingTarget OrgEmployeeMdExtDTO dto, OrgEmployeeMdPO po) {
        this.buildFields(po.getExtra(),dto);
        return po.getExtra();
    }


    default Map<String, Object> buildExtraMap(OrgEmployeeMdExtDTO dto) {
        Map<String, Object> extra = dto.getExtra();
        if (MapUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        if (Objects.nonNull(dto.getExtWqAdmOrg())) {
            extra.put(OrgEmployeeMdExtDTO.Fields.extWqAdmOrg, dto.getExtWqAdmOrg());
        }

        if (Objects.nonNull(dto.getExtWqPurOrg())) {
            extra.put(OrgEmployeeMdExtDTO.Fields.extWqPurOrg, dto.getExtWqPurOrg());
        }

        if (Objects.nonNull(dto.getExtWqSignature())) {
            extra.put(OrgEmployeeMdExtDTO.Fields.extWqSignature, dto.getExtWqSignature());
        }

        return extra;
    }

    default void buildFields(Map<String, Object> extra, OrgEmployeeMdExtDTO dto) {
        if (MapUtils.isEmpty(extra)) {
            return;
        }
        if (Objects.nonNull(extra.get(OrgEmployeeMdExtDTO.Fields.extWqAdmOrg))) {
            dto.setExtWqAdmOrg((List<ExtWqEmployeeAdmOrgCfDTO>) extra.get(OrgEmployeeMdExtDTO.Fields.extWqAdmOrg));
        }

        if (Objects.nonNull(extra.get(OrgEmployeeMdExtDTO.Fields.extWqPurOrg))) {
            dto.setExtWqPurOrg((List<ExtWqEmployeePurOrgCfDTO>) extra.get(OrgEmployeeMdExtDTO.Fields.extWqPurOrg));
        }

        if (Objects.nonNull(extra.get(OrgEmployeeMdExtDTO.Fields.extWqSignature))) {
            dto.setExtWqSignature((String) extra.get(OrgEmployeeMdExtDTO.Fields.extWqSignature));
        }

    }
}
