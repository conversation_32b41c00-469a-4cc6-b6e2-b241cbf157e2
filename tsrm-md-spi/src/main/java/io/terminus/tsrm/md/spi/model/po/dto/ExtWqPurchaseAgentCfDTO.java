package io.terminus.tsrm.md.spi.model.po.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * (ExtWqPurchaseAgentCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-11-08 18:18:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExtWqPurchaseAgentCfDTO extends BaseModel {
    private static final long serialVersionUID = -19156303477224030L;

    @MetaModelField
    @ApiModelProperty("代理公司")
    private Long agentCom;

    @ApiModelProperty("分成比例")
    private BigDecimal shareRatio;

    @ApiModelProperty("状态")
    private String status;

//    @ApiModelProperty("代理员工")
//    private List<ExtWqPurchaseAgentEmployeeCfDTO> employees;

}
