package io.terminus.tsrm.md.spi.convert.org;


import io.terminus.erp.md.spi.model.dto.org.OrgPurOrgCfDTO;
import io.terminus.erp.md.spi.model.po.org.OrgPurOrgCfPO;
import io.terminus.tsrm.md.spi.model.org.dto.OrgPurOrgCfExtDTO;
import org.apache.commons.collections.MapUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 采购组织表(OrgPurOrgCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-10-09 20:22:05
 */
@Mapper(componentModel = "spring")
public interface OrgPurOrgCfExtConverter {

    @Mapping(target = "extra", expression = "java(convertToExtraMap(dto))")
    OrgPurOrgCfPO convert(OrgPurOrgCfExtDTO dto);

    default OrgPurOrgCfExtDTO convert(OrgPurOrgCfPO po) {
        OrgPurOrgCfExtDTO res = new OrgPurOrgCfExtDTO();
        this.convert(res, po);
        return res;
    }

    List<OrgPurOrgCfDTO> convertToDtoList(List<OrgPurOrgCfExtDTO> req);

    List<OrgPurOrgCfExtDTO> convertToExtDtoList(List<OrgPurOrgCfPO> req);


    @Mapping(target = "extra", expression = "java(convertDtoExtFields(dto,po))")
    void convert(@MappingTarget OrgPurOrgCfExtDTO dto, OrgPurOrgCfPO po);

    default Map<String, Object> convertToExtraMap(OrgPurOrgCfExtDTO dto){
        return this.buildExtraMap(dto);
    }

    default Map<String, Object> convertDtoExtFields(@MappingTarget OrgPurOrgCfExtDTO dto, OrgPurOrgCfPO po) {
        this.buildFields(po.getExtra(),dto);
        return po.getExtra();
    }


    default Map<String, Object> buildExtraMap(OrgPurOrgCfExtDTO dto) {
        Map<String, Object> extra = dto.getExtra();
        if (MapUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        if (Objects.nonNull(dto.getExtWqVendWarehouse())) {
            extra.put(OrgPurOrgCfExtDTO.Fields.extWqVendWarehouse, dto.getExtWqVendWarehouse());
        }

        return extra;
    }

    default void buildFields(Map<String, Object> extra, OrgPurOrgCfExtDTO dto) {
        if (MapUtils.isEmpty(extra)) {
            return;
        }
        if (Objects.nonNull(extra.get(OrgPurOrgCfExtDTO.Fields.extWqVendWarehouse))) {
            dto.setExtWqVendWarehouse((Long) extra.get(OrgPurOrgCfExtDTO.Fields.extWqVendWarehouse));
        }
    }

}
