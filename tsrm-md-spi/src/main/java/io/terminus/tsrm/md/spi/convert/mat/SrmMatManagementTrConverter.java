package io.terminus.tsrm.md.spi.convert.mat;

import io.terminus.tsrm.md.spi.model.mat.po.SrmMatManagementTrPO;
import io.terminus.tsrm.md.spi.model.mat.dto.SrmMatManagementTrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * (SrmMatManagementTr)结构映射器
 *
 * <AUTHOR>
 * @since  2025-04-23 17:56:23
 */
@Mapper(componentModel = "spring")
public interface SrmMatManagementTrConverter {

    SrmMatManagementTrDTO po2Dto(SrmMatManagementTrPO req);

    List<SrmMatManagementTrDTO> po2DtoList(List<SrmMatManagementTrPO> poList);

    SrmMatManagementTrPO dto2Po(SrmMatManagementTrDTO req);

    List<SrmMatManagementTrPO> dto2PoList(List<SrmMatManagementTrDTO> dtoList);
}
