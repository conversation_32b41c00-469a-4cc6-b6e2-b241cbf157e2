package io.terminus.tsrm.md.spi.model.org.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (OrgAdmOrgCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-10 11:17:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_adm_org_cf")
public class OrgAdmOrgCfExtPO extends BaseModel {
    private static final long serialVersionUID = 496667127174804359L;

    @ApiModelProperty("组织编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("组织名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

    @ApiModelProperty("组织简称")
    @TableField("`short_name`")
    private String shortName;

    @ApiModelProperty("NCC组织编码")
    @TableField("`ext_wq_ncc_code`")
    private String extWqNccCode;

    @ApiModelProperty("NC组织编码")
    @TableField("`ext_wq_nc_code`")
    private String extWqNcCode;

    @ApiModelProperty("SAP组织编码")
    @TableField("`ext_wq_sap_code`")
    private String extWqSapCode;

    @ApiModelProperty("K3组织编码")
    @TableField("`ext_wq_k3_code`")
    private String extWqK3Code;

    @MetaModelField
    @ApiModelProperty("需求单位")
    @TableField("`ext_wq_inv_org`")
    private Long extWqInvOrg;

    @ApiModelProperty("集成ERP")
    @TableField("`ext_wq_erp_type`")
    private String extWqErpType;
}
