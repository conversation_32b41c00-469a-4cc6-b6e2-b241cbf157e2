package io.terminus.tsrm.md.spi.model.org.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor.org.spi.model.dto.OrgStructMdDetailDTO;
//import io.terminus.trantor.org.spi.model.dto.OrgUnitDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (OrgEmployeeOrgLinkCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:28:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgEmployeeOrgLinkCfDTO extends BaseModel {
    private static final long serialVersionUID = -18984100030501430L;

    @ApiModelProperty("员工")
    @MetaModelField
    private Long employeeId;
    @ApiModelProperty("员工名称")
    private String employeeName;
    @ApiModelProperty("组织单元")
    private OrgStructMdDetailDTO orgUnitId;
    @ApiModelProperty("身份")
    private OrgIdentityDTO identityId;
    @ApiModelProperty("是否主组织")
    private Boolean isMainOrg;
    @ApiModelProperty("身份名称")
    private String identityName;

}
