package io.terminus.tsrm.md.spi.dict.pay;

/**
 * 生效日期类型(ExtWqGenPayTermItemCfEffectiveDateType)字典
 *
 * <AUTHOR>
 * @since  2023-09-26 14:05:22
 */
public interface ExtWqGenPayTermItemCfEffectiveDateTypeDict {

    /**
     * 订单生效日期
     */
    String ORDER_EFFECTIVE_DATE = "order_effective_date";
    /**
     * 入库日期
     */
    String INBOUND_DATE = "inbound_date";
    /**
     * 发票确认日期
     */
    String INVOICE_CONFIRM_DATE = "invoice_confirm_date";

}
