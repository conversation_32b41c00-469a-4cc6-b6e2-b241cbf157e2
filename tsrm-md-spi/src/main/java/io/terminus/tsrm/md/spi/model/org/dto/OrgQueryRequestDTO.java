package io.terminus.tsrm.md.spi.model.org.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;
import java.util.Set;

/**
 * 组织查询请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgQueryRequestDTO extends AbstractPageRequest {
    private static final long serialVersionUID = 4363404158671744950L;

    private Map<String, Object> pageable;

    @ApiModelProperty("部门ID")
    private Long departmentId;
}
