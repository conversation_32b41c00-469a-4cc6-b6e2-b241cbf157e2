package io.terminus.tsrm.md.spi.model.expert.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SRM专家可参与业务范围(SrmExpertBusiScopeMd)传输模型
 *
 * <AUTHOR>
 * @since 2024-11-05 14:07:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SrmExpertBusiScopeMdDTO extends BaseModel {
    private static final long serialVersionUID = -24267697234041299L;

    @MetaModelField
    @ApiModelProperty("业务专家")
    private SrmExpertMdDTO expertId;

    @ApiModelProperty("业务范围")
    private String busiScope;

}
