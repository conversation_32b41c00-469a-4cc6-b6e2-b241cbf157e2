package io.terminus.tsrm.md.spi.convert.mat;

import io.terminus.tsrm.md.spi.model.mat.po.MatManageCatAttriPO;
import io.terminus.tsrm.md.spi.model.mat.dto.MatManageCatAttriDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * (MatManageCatAttri)结构映射器
 *
 * <AUTHOR>
 * @since  2025-04-22 17:20:51
 */
@Mapper(componentModel = "spring")
public interface MatManageCatAttriConverter {

    MatManageCatAttriDTO po2Dto(MatManageCatAttriPO req);

    List<MatManageCatAttriDTO> po2DtoList(List<MatManageCatAttriPO> poList);

    MatManageCatAttriPO dto2Po(MatManageCatAttriDTO req);

    List<MatManageCatAttriPO> dto2PoList(List<MatManageCatAttriDTO> dtoList);
}
