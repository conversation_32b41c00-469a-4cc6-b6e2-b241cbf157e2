package io.terminus.tsrm.md.spi.dict.stock;

/**
 * 类型(InvRecordTrType)字典
 *
 * <AUTHOR>
 * @since  2023-11-06 16:25:25
 */
public interface InvRecordTrTypeDict {

    /**
     * 领料占用
     */
    String PICK_OCCUPY = "PICK_OCCUPY";
    /**
     * 领料释放
     */
    String PICK_RELEASE = "PICK_RELEASE";
    /**
     * 调拨占用
     */
    String ALLOT_OCCUPY = "ALLOT_OCCUPY";
    /**
     * 调拨释放
     */
    String ALLOT_RELEASE = "ALLOT_RELEASE";
    /**
     * 预留占用
     */
    String RESERVE_OCCUPY = "RESERVE_OCCUPY";
    /**
     * 预留释放
     */
    String RESERVE_RELEASE = "RESERVE_RELEASE";

}
