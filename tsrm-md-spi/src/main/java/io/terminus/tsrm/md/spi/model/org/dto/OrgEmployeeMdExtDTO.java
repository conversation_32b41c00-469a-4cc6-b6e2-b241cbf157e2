package io.terminus.tsrm.md.spi.model.org.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import java.util.List;
/**
 * 员工信息表(OrgEmployeeMd)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:23:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@ExtraMetaModel
public class OrgEmployeeMdExtDTO extends OrgEmployeeMdDTO {
    private static final long serialVersionUID = -26384436882453750L;

    @ApiModelProperty("行政组织")
    private List<ExtWqEmployeeAdmOrgCfDTO> extWqAdmOrg;

    @ApiModelProperty("采购组织")
    private List<ExtWqEmployeePurOrgCfDTO> extWqPurOrg;

    @ApiModelProperty("签名图")
    private String extWqSignature;

}
