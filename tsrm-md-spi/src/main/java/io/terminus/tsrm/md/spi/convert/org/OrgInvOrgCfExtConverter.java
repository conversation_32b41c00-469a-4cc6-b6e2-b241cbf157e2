package io.terminus.tsrm.md.spi.convert.org;

import io.terminus.erp.md.spi.model.org.dto.OrgInvOrgCfDTO;
import io.terminus.erp.md.spi.model.po.org.OrgInvOrgCfPO;
import io.terminus.tsrm.md.spi.model.org.dto.OrgInvOrgCfExtDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.Objects;
import org.apache.commons.collections.MapUtils;

/**
 * 库存组织配置表(OrgInvOrgCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-10-31 14:33:48
 */
@Mapper(componentModel = "spring")
public interface OrgInvOrgCfExtConverter {

    @Mapping(target = "extra", expression = "java(convertToExtraMap(dto))")
    OrgInvOrgCfDTO convert(OrgInvOrgCfExtDTO dto);

    default OrgInvOrgCfExtDTO convert(OrgInvOrgCfPO po) {
        OrgInvOrgCfExtDTO res = new OrgInvOrgCfExtDTO();
        this.convert(res, po);
        return res;
    }

    List<OrgInvOrgCfDTO> convertToDtoList(List<OrgInvOrgCfExtDTO> req);

    List<OrgInvOrgCfExtDTO> convertToExtDtoList(List<OrgInvOrgCfPO> req);


    @Mapping(target = "extra", expression = "java(convertDtoExtFields(dto,po))")
    void convert(@MappingTarget OrgInvOrgCfExtDTO dto, OrgInvOrgCfPO po);

    default Map<String, Object> convertToExtraMap(OrgInvOrgCfExtDTO dto){
        return this.buildExtraMap(dto);
    }

    default Map<String, Object> convertDtoExtFields(@MappingTarget OrgInvOrgCfExtDTO dto, OrgInvOrgCfPO po) {
        this.buildFields(po.getExtra(),dto);
        return po.getExtra();
    }


    default Map<String, Object> buildExtraMap(OrgInvOrgCfExtDTO dto) {
        Map<String, Object> extra = dto.getExtra();
        if (MapUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        if (Objects.nonNull(dto.getExtBelongType())) {
            extra.put(OrgInvOrgCfExtDTO.Fields.extBelongType, dto.getExtBelongType());
        }

        if (Objects.nonNull(dto.getExtWqNccCode())) {
            extra.put(OrgInvOrgCfExtDTO.Fields.extWqNccCode, dto.getExtWqNccCode());
        }

        if (Objects.nonNull(dto.getExtWqNcCode())) {
            extra.put(OrgInvOrgCfExtDTO.Fields.extWqNcCode, dto.getExtWqNcCode());
        }

        if (Objects.nonNull(dto.getExtWqSapCode())) {
            extra.put(OrgInvOrgCfExtDTO.Fields.extWqSapCode, dto.getExtWqSapCode());
        }

        if (Objects.nonNull(dto.getExtWqK3Code())) {
            extra.put(OrgInvOrgCfExtDTO.Fields.extWqK3Code, dto.getExtWqK3Code());
        }

        if (Objects.nonNull(dto.getExtWqComInfo())) {
            extra.put(OrgInvOrgCfExtDTO.Fields.extWqComInfo, dto.getExtWqComInfo());
        }

        if (Objects.nonNull(dto.getExtWqErpType())) {
            extra.put(OrgInvOrgCfExtDTO.Fields.extWqErpType, dto.getExtWqErpType());
        }

        if (Objects.nonNull(dto.getExtWqErpPurOrgCode())) {
            extra.put(OrgInvOrgCfExtDTO.Fields.extWqErpPurOrgCode, dto.getExtWqErpPurOrgCode());
        }

        if (Objects.nonNull(dto.getExtWqErpPurOrgName())) {
            extra.put(OrgInvOrgCfExtDTO.Fields.extWqErpPurOrgName, dto.getExtWqErpPurOrgName());
        }

        return extra;
    }

    default void buildFields(Map<String, Object> extra, OrgInvOrgCfExtDTO dto) {
        if (MapUtils.isEmpty(extra)) {
            return;
        }
        if (Objects.nonNull(extra.get(OrgInvOrgCfExtDTO.Fields.extBelongType))) {
            dto.setExtBelongType((String) extra.get(OrgInvOrgCfExtDTO.Fields.extBelongType));
        }

        if (Objects.nonNull(extra.get(OrgInvOrgCfExtDTO.Fields.extWqNccCode))) {
            dto.setExtWqNccCode((String) extra.get(OrgInvOrgCfExtDTO.Fields.extWqNccCode));
        }

        if (Objects.nonNull(extra.get(OrgInvOrgCfExtDTO.Fields.extWqNcCode))) {
            dto.setExtWqNcCode((String) extra.get(OrgInvOrgCfExtDTO.Fields.extWqNcCode));
        }

        if (Objects.nonNull(extra.get(OrgInvOrgCfExtDTO.Fields.extWqSapCode))) {
            dto.setExtWqSapCode((String) extra.get(OrgInvOrgCfExtDTO.Fields.extWqSapCode));
        }

        if (Objects.nonNull(extra.get(OrgInvOrgCfExtDTO.Fields.extWqK3Code))) {
            dto.setExtWqK3Code((String) extra.get(OrgInvOrgCfExtDTO.Fields.extWqK3Code));
        }

        if (Objects.nonNull(extra.get(OrgInvOrgCfExtDTO.Fields.extWqComInfo))) {
            dto.setExtWqComInfo((Long) extra.get(OrgInvOrgCfExtDTO.Fields.extWqComInfo));
        }

        if (Objects.nonNull(extra.get(OrgInvOrgCfExtDTO.Fields.extWqErpType))) {
            dto.setExtWqErpType((String) extra.get(OrgInvOrgCfExtDTO.Fields.extWqErpType));
        }

        if (Objects.nonNull(extra.get(OrgInvOrgCfExtDTO.Fields.extWqErpPurOrgCode))) {
            dto.setExtWqErpPurOrgCode((String) extra.get(OrgInvOrgCfExtDTO.Fields.extWqErpPurOrgCode));
        }

        if (Objects.nonNull(extra.get(OrgInvOrgCfExtDTO.Fields.extWqErpPurOrgName))) {
            dto.setExtWqErpPurOrgName((String) extra.get(OrgInvOrgCfExtDTO.Fields.extWqErpPurOrgName));
        }

    }
}
