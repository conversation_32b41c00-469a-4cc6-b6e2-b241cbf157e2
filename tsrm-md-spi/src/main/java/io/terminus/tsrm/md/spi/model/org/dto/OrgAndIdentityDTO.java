package io.terminus.tsrm.md.spi.model.org.dto;

import io.terminus.common.api.model.BaseModel;
//import io.terminus.trantor.org.spi.model.dto.OrgUnitDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 组织和身份DTO
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@EqualsAndHashCode
@Data
public class OrgAndIdentityDTO extends BaseModel {
    private Long orgId;

    private String orgName;

    private String orgCode;

    private String identityName;
}
