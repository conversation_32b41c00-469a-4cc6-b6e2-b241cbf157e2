package io.terminus.tsrm.md.spi.convert.org;

import io.terminus.tsrm.md.spi.model.org.dto.ExtWqEmployeeAdmOrgCfDTO;
import io.terminus.tsrm.md.spi.model.org.po.ExtWqEmployeeAdmOrgCfPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * (ExtWqEmployeeAdmOrgCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-27 11:32:16
 */
@Mapper(componentModel = "spring")
public interface ExtWqEmployeeAdmOrgCfConverter {

    ExtWqEmployeeAdmOrgCfDTO po2Dto(ExtWqEmployeeAdmOrgCfPO req);

    List<ExtWqEmployeeAdmOrgCfDTO> po2DtoList(List<ExtWqEmployeeAdmOrgCfPO> poList);

    ExtWqEmployeeAdmOrgCfPO dto2Po(ExtWqEmployeeAdmOrgCfDTO req);

    List<ExtWqEmployeeAdmOrgCfPO> dto2PoList(List<ExtWqEmployeeAdmOrgCfDTO> dtoList);
}
