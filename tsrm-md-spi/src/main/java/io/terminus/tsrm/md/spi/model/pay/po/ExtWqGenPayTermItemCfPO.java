package io.terminus.tsrm.md.spi.model.pay.po;


import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ExtWqGenPayTermItemCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-26 11:26:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ext_wq_gen_pay_term_item_cf")
public class ExtWqGenPayTermItemCfPO extends BaseModel {
    private static final long serialVersionUID = -47351279808195508L;

    @ApiModelProperty("分期")
    @TableField("`stage`")
    private Long stage;

    @ApiModelProperty("付款类型")
    @TableField("`pay_type`")
    private String payType;

    @ApiModelProperty("付款比例")
    @TableField("`pay_ratio`")
    private BigDecimal payRatio;

    @ApiModelProperty("生效日期类型")
    @TableField("`effective_date_type`")
    private String effectiveDateType;

    @ApiModelProperty("延迟天数")
    @TableField("`delay_days`")
    private Long delayDays;

    @ApiModelProperty("结算方式")
    @TableField("`settlement_method`")
    private String settlementMethod;

    @ApiModelProperty("付款协议表Id")
    @TableField("`ext_wq_gen_pay_term_head_cf_id`")
    private Long extWqGenPayTermHeadCfId;

}
