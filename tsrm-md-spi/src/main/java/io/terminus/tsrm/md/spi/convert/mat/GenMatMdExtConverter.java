package io.terminus.tsrm.md.spi.convert.mat;


import java.math.BigDecimal;

import io.terminus.erp.md.spi.model.dto.mat.GenMatMdDTO;
import io.terminus.erp.md.spi.model.po.mat.GenMatMdPO;
import io.terminus.tsrm.md.spi.model.mat.dto.GenMatMdExtDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.MatDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.Objects;
import org.apache.commons.collections.MapUtils;

/**
 * 物料主数据定义表(GenMatMd)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-26 11:33:07
 */
@Mapper(componentModel = "spring")
public interface GenMatMdExtConverter {

    @Mapping(target = "extra", expression = "java(convertToExtraMap(dto))")
    GenMatMdDTO convert(GenMatMdExtDTO dto);

    default GenMatMdExtDTO convert(GenMatMdPO po) {
        GenMatMdExtDTO res = new GenMatMdExtDTO();
        this.convert(res, po);
        return res;
    }

    List<GenMatMdDTO> convertToDtoList(List<GenMatMdExtDTO> req);

    List<GenMatMdExtDTO> convertToExtDtoList(List<GenMatMdPO> req);


    @Mapping(target = "extra", expression = "java(convertDtoExtFields(dto,po))")
    void convert(@MappingTarget GenMatMdExtDTO dto, GenMatMdPO po);

    default Map<String, Object> convertToExtraMap(GenMatMdExtDTO dto){
        return this.buildExtraMap(dto);
    }

    default Map<String, Object> convertDtoExtFields(@MappingTarget GenMatMdExtDTO dto, GenMatMdPO po) {
        this.buildFields(po.getExtra(),dto);
        return po.getExtra();
    }


    default Map<String, Object> buildExtraMap(GenMatMdExtDTO dto) {
        Map<String, Object> extra = dto.getExtra();
        if (MapUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        if (Objects.nonNull(dto.getExtWqShortName())) {
            extra.put(GenMatMdExtDTO.Fields.extWqShortName, dto.getExtWqShortName());
        }

        if (Objects.nonNull(dto.getExtWqMnemonicCode())) {
            extra.put(GenMatMdExtDTO.Fields.extWqMnemonicCode, dto.getExtWqMnemonicCode());
        }

        if (Objects.nonNull(dto.getExtWqBarcode())) {
            extra.put(GenMatMdExtDTO.Fields.extWqBarcode, dto.getExtWqBarcode());
        }

        if (Objects.nonNull(dto.getExtWqSpecification())) {
            extra.put(GenMatMdExtDTO.Fields.extWqSpecification, dto.getExtWqSpecification());
        }

        if (Objects.nonNull(dto.getExtWqMaterialQuality())) {
            extra.put(GenMatMdExtDTO.Fields.extWqMaterialQuality, dto.getExtWqMaterialQuality());
        }

        if (Objects.nonNull(dto.getExtWqUsage())) {
            extra.put(GenMatMdExtDTO.Fields.extWqUsage, dto.getExtWqUsage());
        }

        if (Objects.nonNull(dto.getExtWqReceiveStandard())) {
            extra.put(GenMatMdExtDTO.Fields.extWqReceiveStandard, dto.getExtWqReceiveStandard());
        }

        if (Objects.nonNull(dto.getExtWqRemark())) {
            extra.put(GenMatMdExtDTO.Fields.extWqRemark, dto.getExtWqRemark());
        }

        if (Objects.nonNull(dto.getExtWqImage())) {
            extra.put(GenMatMdExtDTO.Fields.extWqImage, dto.getExtWqImage());
        }

        if (Objects.nonNull(dto.getExtWqOverThreshold())) {
            extra.put(GenMatMdExtDTO.Fields.extWqOverThreshold, dto.getExtWqOverThreshold());
        }

        if (Objects.nonNull(dto.getExtPurUom())) {
            extra.put(GenMatMdExtDTO.Fields.extPurUom, dto.getExtPurUom());
        }

        return extra;
    }

    default void buildFields(Map<String, Object> extra, GenMatMdExtDTO dto) {
        if (MapUtils.isEmpty(extra)) {
            return;
        }
        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extWqShortName))) {
            dto.setExtWqShortName((String) extra.get(GenMatMdExtDTO.Fields.extWqShortName));
        }

        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extWqMnemonicCode))) {
            dto.setExtWqMnemonicCode((String) extra.get(GenMatMdExtDTO.Fields.extWqMnemonicCode));
        }

        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extWqBarcode))) {
            dto.setExtWqBarcode((String) extra.get(GenMatMdExtDTO.Fields.extWqBarcode));
        }

        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extWqSpecification))) {
            dto.setExtWqSpecification((String) extra.get(GenMatMdExtDTO.Fields.extWqSpecification));
        }

        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extWqMaterialQuality))) {
            dto.setExtWqMaterialQuality((String) extra.get(GenMatMdExtDTO.Fields.extWqMaterialQuality));
        }

        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extWqUsage))) {
            dto.setExtWqUsage((String) extra.get(GenMatMdExtDTO.Fields.extWqUsage));
        }

        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extWqReceiveStandard))) {
            dto.setExtWqReceiveStandard((String) extra.get(GenMatMdExtDTO.Fields.extWqReceiveStandard));
        }

        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extWqRemark))) {
            dto.setExtWqRemark((String) extra.get(GenMatMdExtDTO.Fields.extWqRemark));
        }

        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extWqImage))) {
            dto.setExtWqImage((String) extra.get(GenMatMdExtDTO.Fields.extWqImage));
        }

        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extWqOverThreshold))) {
            dto.setExtWqOverThreshold((BigDecimal) extra.get(GenMatMdExtDTO.Fields.extWqOverThreshold));
        }

        if (Objects.nonNull(extra.get(GenMatMdExtDTO.Fields.extPurUom))) {
            dto.setExtPurUom((Long) extra.get(GenMatMdExtDTO.Fields.extPurUom));
        }

    }


    GenMatMdPO convertMatSync2PO( MatDTO matDTO);

}
