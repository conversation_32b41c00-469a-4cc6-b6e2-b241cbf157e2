package io.terminus.tsrm.md.spi.model.org.dto;

import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 组织和身份 整体返回DTO
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@EqualsAndHashCode
@Data
public class OrgAndIdentityResDTO extends BaseModel {
    private List<OrgAndIdentityDTO> orgList;

    OrgAndIdentityDTO org;

    private String identityName;
}
