package io.terminus.tsrm.md.spi.convert.po;

import io.terminus.tsrm.md.spi.model.po.dto.ExtWqPurchaseAgentCfDTO;
import io.terminus.tsrm.md.spi.model.po.po.ExtWqPurchaseAgentCfPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * (ExtWqPurchaseAgentCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-11-08 18:18:10
 */
@Mapper(componentModel = "spring")
public interface ExtWqPurchaseAgentCfConverter {

    ExtWqPurchaseAgentCfDTO po2Dto(ExtWqPurchaseAgentCfPO req);

    List<ExtWqPurchaseAgentCfDTO> po2DtoList(List<ExtWqPurchaseAgentCfPO> poList);

    ExtWqPurchaseAgentCfPO dto2Po(ExtWqPurchaseAgentCfDTO req);

    List<ExtWqPurchaseAgentCfPO> dto2PoList(List<ExtWqPurchaseAgentCfDTO> dtoList);
}
