package io.terminus.tsrm.md.spi.model.org.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (ExtWqEmployeeUserTemp)传输模型
 *
 * <AUTHOR>
 * @since  2023-10-18 15:35:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExtWqEmployeeUserTempDTO extends BaseModel {
    private static final long serialVersionUID = 380091272823753501L;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("原始密码")
    private String originalPwd;

    @ApiModelProperty("新密码")
    private String userPwd;

}
