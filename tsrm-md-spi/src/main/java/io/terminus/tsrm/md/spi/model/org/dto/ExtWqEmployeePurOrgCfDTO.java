package io.terminus.tsrm.md.spi.model.org.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (ExtWqEmployeePurOrgCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:32:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExtWqEmployeePurOrgCfDTO extends BaseModel {
    private static final long serialVersionUID = -55822137005071753L;

    @MetaModelField
    @ApiModelProperty("采购组织")
    private Long purOrg;

    @MetaModelField
    @ApiModelProperty("org_employee_md_id")
    private Long orgEmployeeMdId;

}
