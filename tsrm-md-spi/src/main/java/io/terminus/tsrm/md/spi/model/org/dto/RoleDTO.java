package io.terminus.tsrm.md.spi.model.org.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Auther: luowei
 * @Date: 2023/10/27 - 10 - 27 - 12:35
 * @Description: io.terminus.wq.md.spi.model.org.dto
 * @version: 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RoleDTO extends BaseModel {
    private static final long serialVersionUID = 2337560290949818237L;

    @ApiModelProperty("角色ID")
    private Long roleId;

    @ApiModelProperty("角色ID")
    private String roleName;
}
