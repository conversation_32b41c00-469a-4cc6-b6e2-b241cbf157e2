package io.terminus.tsrm.md.spi.model.mat.dto;


import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (SrmMatManagementTr)传输模型
 *
 * <AUTHOR>
 * @since  2025-04-23 17:56:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SrmMatManagementTrDTO extends BaseModel {
    private static final long serialVersionUID = -53180299988251286L;

    @ApiModelProperty("企业物料描述")
    private String matDesc;

    @ApiModelProperty("物料原始属性")
    private String matOriginAttr;

    @ApiModelProperty("物料信息")
    private String matInfo;

    @MetaModelField
    @ApiModelProperty("物料类目")
    private Long matCate;

    @ApiModelProperty("物料状态")
    private String matManagementStatus;

    @ApiModelProperty("商品链接")
    private String itemUrl;

    @ApiModelProperty("商品SKU")
    private String itemSku;

    @ApiModelProperty("参考价格")
    private BigDecimal referPrice;

    @ApiModelProperty("关键属性")
    private String keyAttr;

    @ApiModelProperty("其他属性")
    private String otherAtte;

    @MetaModelField
    @ApiModelProperty("关联物料")
    private Long genMatId;

    @ApiModelProperty("属性列表")
    private List<MatManageSimilarListDTO> similarMatList;

    @ApiModelProperty("所有属性的原json")
    private String attrJson;

    @ApiModelProperty("所有属性的原json")
    private Map<String, String> attrMap;

    @ApiModelProperty("问题说明")
    private String problem;

    @ApiModelProperty("处理建议")
    private String suggest;
}
