package io.terminus.tsrm.md.spi.model.expert.dto;


import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.erp.md.spi.model.po.base.GenCounTypeCfPO;
import io.terminus.tsrm.md.spi.model.org.dto.OrgEmployeeMdDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * SRM_专家库(SrmExpertMd)传输模型
 *
 * <AUTHOR>
 * @since  2024-12-24 10:35:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SrmExpertMdDTO extends BaseModel {
    private static final long serialVersionUID = -25064114643103140L;

    @ApiModelProperty("专家编号")
    private String expertCode;

    @ApiModelProperty("专家姓名")
    private String expertName;

    @ApiModelProperty("专家类型")
    private String expertType;

    @MetaModelField
    @ApiModelProperty("关联员工")
    private OrgEmployeeMdDTO employeeId;

    @ApiModelProperty("专家证件类型")
    private String expertCertType;

    @ApiModelProperty("专家证件号")
    private String expertCertNum;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("联系邮箱")
    private String contactEmail;

    @ApiModelProperty("专家等级")
    private String expertLevel;

    @ApiModelProperty("来源方式")
    private String recommType;

    @ApiModelProperty("推荐方名称")
    private String recommPartyName;

    @MetaModelField
    @ApiModelProperty("所在国家/地区")
    private GenCounTypeCfPO country;

    @ApiModelProperty("现任公司名称")
    private String expertComName;

    @ApiModelProperty("现任公司统一社会信用代码")
    private String expertComCreditCode;

    @ApiModelProperty("附件")
    private String attachment;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("负责业务范围")
    private List<SrmExpertBusiScopeMdDTO> busiScope;

    @ApiModelProperty("负责物料类目")
    private List<SrmExpertMatCateMdDTO> matCate;

    @ApiModelProperty("详细地址")
    private String addressDetail;

    @MetaModelField
    @ApiModelProperty("地址")
    private Long addressId;

    @MetaModelField
    @ApiModelProperty("用户")
    private Long user;

}
