package io.terminus.tsrm.md.spi.model.org.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.annotation.DesensitizeClass;
import io.terminus.iam.api.annotation.DesensitizeField;
import io.terminus.iam.api.response.AbstractResult;
import io.terminus.iam.api.response.application.Application;
import lombok.*;

import java.time.LocalDateTime;

/**
 * @Auther: luowei
 * @Date: 2023/9/28 - 09 - 28 - 10:03
 * @Description: io.terminus.wq.md.spi.model.org.dto
 * @version: 1.0
 */
@DesensitizeClass
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户信息")
public class UserDTO extends AbstractResult {

    private static final long serialVersionUID = -8184031735517443231L;

    @Schema(description = "用户唯一标识符")
    private Long id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "昵称")
    private String nickname;

    @DesensitizeField(value = DesensitizeField.DesensitizeDataType.MOBILE_PHONE)
    @Schema(description = "手机号")
    private String mobile;

    @DesensitizeField(value = DesensitizeField.DesensitizeDataType.EMAIL)
    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "状态")
    private Boolean status;

    @Schema(description = "锁定时间")
    private LocalDateTime lockedAt;

    @Schema(description = "是否锁定")
    private Boolean locked;

    @JsonIgnore
    @Schema(description = "用户秘钥")
    private String secret;

    @JsonIgnore
    @Schema(description = "开启多因子认证")
    private Boolean mfaEnabled;

    @Schema(description = "注册归属应用")
    private Application application;

}
