package io.terminus.tsrm.md.spi.model.org.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织身份表
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgIdentityDTO extends BaseModel {
    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("状态")
    private String status;
}
