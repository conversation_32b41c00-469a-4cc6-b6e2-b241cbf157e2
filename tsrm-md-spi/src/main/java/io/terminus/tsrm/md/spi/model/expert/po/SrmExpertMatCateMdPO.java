package io.terminus.tsrm.md.spi.model.expert.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SRM专家可负责物料类目范围(SrmExpertMatCateMd)存储模型
 *
 * <AUTHOR>
 * @since  2024-11-05 14:07:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "srm_expert_mat_cate_md")
public class SrmExpertMatCateMdPO extends BaseModel {
    private static final long serialVersionUID = 272516341731336234L;

    @ApiModelProperty("寻源专家")
    @TableField("`expert_id`")
    private Long expertId;

    @ApiModelProperty("物料分类id")
    @TableField("`mat_cate_id`")
    private Long matCateId;

    @ApiModelProperty("物料分类code")
    @TableField("`mat_cate_code`")
    private String matCateCode;

}
