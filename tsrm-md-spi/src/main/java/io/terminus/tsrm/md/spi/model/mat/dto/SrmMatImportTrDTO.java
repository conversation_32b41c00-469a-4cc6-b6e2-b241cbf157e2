package io.terminus.tsrm.md.spi.model.mat.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (SrmMatImportTr)传输模型
 *
 * <AUTHOR>
 * @since  2025-04-23 17:56:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SrmMatImportTrDTO extends BaseModel {
    private static final long serialVersionUID = 864943658651828799L;

    @ApiModelProperty("清单名称")
    private String name;

    @ApiModelProperty("健康度")
    private Long healthRate;

    @ApiModelProperty("清单附件")
    private String importAttachment;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("请求唯一标识")
    private String requestId;

}
