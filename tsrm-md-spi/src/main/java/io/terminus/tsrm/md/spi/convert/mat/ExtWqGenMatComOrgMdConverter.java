package io.terminus.tsrm.md.spi.convert.mat;

import io.terminus.tsrm.md.spi.model.mat.dto.ExtWqGenMatComOrgMdDTO;
import io.terminus.tsrm.md.spi.model.mat.po.ExtWqGenMatComOrgMdPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * (ExtWqGenMatComOrgMd)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-26 13:48:51
 */
@Mapper(componentModel = "spring")
public interface ExtWqGenMatComOrgMdConverter {

    ExtWqGenMatComOrgMdDTO po2Dto(ExtWqGenMatComOrgMdPO req);

    List<ExtWqGenMatComOrgMdDTO> po2DtoList(List<ExtWqGenMatComOrgMdPO> poList);

    ExtWqGenMatComOrgMdPO dto2Po(ExtWqGenMatComOrgMdDTO req);

    List<ExtWqGenMatComOrgMdPO> dto2PoList(List<ExtWqGenMatComOrgMdDTO> dtoList);
}
