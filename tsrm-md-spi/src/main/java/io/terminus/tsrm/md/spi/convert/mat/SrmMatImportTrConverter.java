package io.terminus.tsrm.md.spi.convert.mat;

import io.terminus.tsrm.md.spi.model.mat.po.SrmMatImportTrPO;
import io.terminus.tsrm.md.spi.model.mat.dto.SrmMatImportTrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * (SrmMatImportTr)结构映射器
 *
 * <AUTHOR>
 * @since  2025-04-23 17:56:23
 */
@Mapper(componentModel = "spring")
public interface SrmMatImportTrConverter {

    SrmMatImportTrDTO po2Dto(SrmMatImportTrPO req);

    List<SrmMatImportTrDTO> po2DtoList(List<SrmMatImportTrPO> poList);

    SrmMatImportTrPO dto2Po(SrmMatImportTrDTO req);

    List<SrmMatImportTrPO> dto2PoList(List<SrmMatImportTrDTO> dtoList);
}
