package io.terminus.tsrm.md.spi.model.expert.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SRM_专家库(SrmExpertMd)存储模型
 *
 * <AUTHOR>
 * @since  2024-12-24 10:35:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "srm_expert_md")
public class SrmExpertMdPO extends BaseModel {
    private static final long serialVersionUID = 466726780540288740L;

    @ApiModelProperty("专家编号")
    @TableField("`expert_code`")
    private String expertCode;

    @ApiModelProperty("专家姓名")
    @TableField("`expert_name`")
    private String expertName;

    @ApiModelProperty("专家类型")
    @TableField("`expert_type`")
    private String expertType;

    @ApiModelProperty("关联员工")
    @TableField("`employee_id`")
    private Long employeeId;

    @ApiModelProperty("专家证件类型")
    @TableField("`expert_cert_type`")
    private String expertCertType;

    @ApiModelProperty("专家证件号")
    @TableField("`expert_cert_num`")
    private String expertCertNum;

    @ApiModelProperty("联系电话")
    @TableField("`contact_phone`")
    private String contactPhone;

    @ApiModelProperty("联系邮箱")
    @TableField("`contact_email`")
    private String contactEmail;

    @ApiModelProperty("专家等级")
    @TableField("`expert_level`")
    private String expertLevel;

    @ApiModelProperty("来源方式")
    @TableField("`recomm_type`")
    private String recommType;

    @ApiModelProperty("推荐方名称")
    @TableField("`recomm_party_name`")
    private String recommPartyName;

    @ApiModelProperty("所在国家/地区")
    @TableField("`country`")
    private Long country;

    @ApiModelProperty("现任公司名称")
    @TableField("`expert_com_name`")
    private String expertComName;

    @ApiModelProperty("现任公司统一社会信用代码")
    @TableField("`expert_com_credit_code`")
    private String expertComCreditCode;

    @ApiModelProperty("附件")
    @TableField("`attachment`")
    private String attachment;

    @ApiModelProperty("备注")
    @TableField("`remark`")
    private String remark;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

    @ApiModelProperty("详细地址")
    @TableField("`address_detail`")
    private String addressDetail;

    @ApiModelProperty("地址")
    @TableField("`address_id`")
    private Long addressId;

    @ApiModelProperty("用户")
    @TableField("`user`")
    private Long user;

}
