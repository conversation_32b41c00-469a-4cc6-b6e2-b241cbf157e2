package io.terminus.tsrm.md.spi.model.mat.po;


import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (SrmMatManagementTr)存储模型
 *
 * <AUTHOR>
 * @since  2025-04-23 17:56:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "srm_mat_management_tr")
public class SrmMatManagementTrPO extends BaseModel {
    private static final long serialVersionUID = 271384586576008047L;

    @ApiModelProperty("企业物料描述")
    @TableField("`mat_desc`")
    private String matDesc;

    @ApiModelProperty("物料原始属性")
    @TableField("`mat_origin_attr`")
    private String matOriginAttr;

    @ApiModelProperty("物料信息")
    @TableField("`mat_info`")
    private String matInfo;

    @ApiModelProperty("物料类目")
    @TableField("`mat_cate`")
    private Long matCate;

    @ApiModelProperty("物料状态")
    @TableField("`mat_management_status`")
    private String matManagementStatus;

    @ApiModelProperty("商品链接")
    @TableField("`item_url`")
    private String itemUrl;

    @ApiModelProperty("商品SKU")
    @TableField("`item_sku`")
    private String itemSku;

    @ApiModelProperty("参考价格")
    @TableField("`refer_price`")
    private BigDecimal referPrice;

    @ApiModelProperty("关键属性")
    @TableField("`key_attr`")
    private String keyAttr;

    @ApiModelProperty("其他属性")
    @TableField("`other_atte`")
    private String otherAtte;

    @ApiModelProperty("关联物料")
    @TableField("`gen_mat_id`")
    private Long genMatId;

    @ApiModelProperty("所有属性的原json")
    @TableField("`attr_json`")
    private String attrJson;

    @ApiModelProperty("问题说明")
    @TableField("`problem`")
    private String problem;

    @ApiModelProperty("处理建议")
    @TableField("`suggest`")
    private String suggest;

}
