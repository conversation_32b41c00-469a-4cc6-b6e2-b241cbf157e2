package io.terminus.tsrm.md.spi.convert.expert;

import io.terminus.tsrm.md.spi.model.expert.po.SrmExpertMdPO;
import io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertMdDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * SRM_专家库(SrmExpertMd)结构映射器
 *
 * <AUTHOR>
 * @since  2024-12-24 10:35:26
 */
@Mapper(componentModel = "spring")
public interface SrmExpertMdConverter {

    @Mapping(target = "employeeId.id", source = "employeeId")
    @Mapping(target = "country.id", source = "country")
    SrmExpertMdDTO po2Dto(SrmExpertMdPO req);

    List<SrmExpertMdDTO> po2DtoList(List<SrmExpertMdPO> poList);

    @Mapping(target = "employeeId", source = "employeeId.id")
    @Mapping(target = "country", source = "country.id")
    SrmExpertMdPO dto2Po(SrmExpertMdDTO req);

    List<SrmExpertMdPO> dto2PoList(List<SrmExpertMdDTO> dtoList);
}
