package io.terminus.tsrm.md.spi.model.mat.po;


import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (MatManageCatAttri)存储模型
 *
 * <AUTHOR>
 * @since  2025-04-22 17:20:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "mat_manage_cat_attri")
public class MatManageCatAttriPO extends BaseModel {
    private static final long serialVersionUID = -60429698584800593L;

    @ApiModelProperty("属性名")
    @TableField("`attri_name`")
    private String attriName;

    @ApiModelProperty("属性数量")
    @TableField("`attri_num`")
    private BigDecimal attriNum;

    @ApiModelProperty("是否必填")
    @TableField("`require`")
    private Boolean require;

    @ApiModelProperty("mat_manage_cate_total_attri_list_id")
    @TableField("`mat_manage_cate_total_attri_list_id`")
    private Long matManageCateTotalAttriListId;

    @ApiModelProperty("属性说明")
    @TableField("`attri_info`")
    private String attriInfo;

    @ApiModelProperty("是否AI推荐")
    @TableField("`ai_recommend`")
    private Boolean aiRecommend;

}
