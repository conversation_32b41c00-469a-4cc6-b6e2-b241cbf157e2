package io.terminus.tsrm.md.spi.model.stock.po;


import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存记录表(InvRecordTr)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-06 16:25:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "inv_record_tr")
public class InvRecordTrPO extends BaseModel {
    private static final long serialVersionUID = 613755150106213425L;

    @ApiModelProperty("库存记录号")
    @TableField("`inv_record_code`")
    private String invRecordCode;

    @ApiModelProperty("关联需求计划")
    @TableField("`rel_mrp`")
    private String relMrp;

    @ApiModelProperty("关联需求计划行")
    @TableField("`rel_mrp_item`")
    private String relMrpItem;

    @ApiModelProperty("关联单据")
    @TableField("`rel_order`")
    private String relOrder;

    @ApiModelProperty("关联单据明细")
    @TableField("`rel_order_item`")
    private String relOrderItem;

    @ApiModelProperty("物料")
    @TableField("`mat`")
    private Long mat;

    @ApiModelProperty("仓库")
    @TableField("`inv_loc`")
    private Long invLoc;

    @ApiModelProperty("数量")
    @TableField("`qty`")
    private BigDecimal qty;

    @ApiModelProperty("类型")
    @TableField("`type`")
    private String type;

    @ApiModelProperty("备注")
    @TableField("`remark`")
    private String remark;

    @ApiModelProperty("是否预占用")
    @TableField("`pre_occupy`")
    private String preOccupy;

    @ApiModelProperty("执行时间")
    @TableField("`execute_time`")
    private LocalDateTime executeTime;

    @ApiModelProperty("invOccupyTrId")
    @TableField("`inv_occupy_tr_id`")
    private Long invOccupyTrId;

    @ApiModelProperty("平衡利库ID")
    @TableField("`stock_balance_detail_tr_id`")
    private Long stockBalanceDetailTrId;

}
