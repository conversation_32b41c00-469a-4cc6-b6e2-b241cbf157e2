package io.terminus.tsrm.md.spi.convert.stock;

import io.terminus.tsrm.md.spi.model.stock.dto.InvOccupyTrDTO;
import io.terminus.tsrm.md.spi.model.stock.po.InvOccupyTrPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 库存占用表(InvOccupyTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-11-02 17:00:15
 */
@Mapper(componentModel = "spring")
public interface InvOccupyTrConverter {

    InvOccupyTrDTO po2Dto(InvOccupyTrPO req);

    List<InvOccupyTrDTO> po2DtoList(List<InvOccupyTrPO> poList);

    InvOccupyTrPO dto2Po(InvOccupyTrDTO req);

    List<InvOccupyTrPO> dto2PoList(List<InvOccupyTrDTO> dtoList);
}
