package io.terminus.tsrm.md.spi.model.stock.dto;


import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 库存占用表(InvOccupyTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-11-02 17:00:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InvOccupyTrDTO extends BaseModel {
    private static final long serialVersionUID = -73216810937389706L;

    @MetaModelField
    @ApiModelProperty("物料")
    private Long mat;

    @MetaModelField
    @ApiModelProperty("仓库")
    private Long invLoc;

    @MetaModelField
    @ApiModelProperty("供应商")
    private Long vend;

    @ApiModelProperty("总占用数量")
    private BigDecimal totalOccupyQty;

    @ApiModelProperty("领料占用数量")
    private BigDecimal pickOccupyQty;

    @ApiModelProperty("调拨占用数量")
    private BigDecimal allotOccupyQty;

    @ApiModelProperty("预留占用数量")
    private BigDecimal reserveOccupyQty;

    @ApiModelProperty("库存记录")
    private List<InvRecordTrDTO> invRecordList;

    @ApiModelProperty("预占用数量")
    private BigDecimal preOccupyQty;

}
