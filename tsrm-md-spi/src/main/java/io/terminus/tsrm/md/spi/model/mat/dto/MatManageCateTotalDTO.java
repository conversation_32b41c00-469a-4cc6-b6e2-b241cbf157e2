package io.terminus.tsrm.md.spi.model.mat.dto;


import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (MatManageCateTotal)传输模型
 *
 * <AUTHOR>
 * @since  2025-04-28 16:05:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MatManageCateTotalDTO extends BaseModel {
    private static final long serialVersionUID = 452026334472591633L;

    @MetaModelField
    @ApiModelProperty("物料类目")
    private Long matCate;

    @ApiModelProperty("物料总数")
    private Long matTotal;

    @ApiModelProperty("属性数量")
    private Long attriTotal;

    @ApiModelProperty("属性列表")
    private List<MatManageCatAttriDTO> attriList;

    @ApiModelProperty("相似度阈值")
    private BigDecimal similarityThreshold;

}
