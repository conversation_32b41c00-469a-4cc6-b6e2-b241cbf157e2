package io.terminus.tsrm.md.spi.convert.expert;

import io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertBusiScopeMdDTO;
import io.terminus.tsrm.md.spi.model.expert.po.SrmExpertBusiScopeMdPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * SRM专家可参与业务范围(SrmExpertBusiScopeMd)结构映射器
 *
 * <AUTHOR>
 * @since  2024-11-05 14:07:08
 */
@Mapper(componentModel = "spring")
public interface SrmExpertBusiScopeMdConverter {

    @Mapping(target = "expertId.id", source = "expertId")
    SrmExpertBusiScopeMdDTO po2Dto(SrmExpertBusiScopeMdPO req);

    List<SrmExpertBusiScopeMdDTO> po2DtoList(List<SrmExpertBusiScopeMdPO> poList);

    @Mapping(target = "expertId", source = "expertId.id")
    SrmExpertBusiScopeMdPO dto2Po(SrmExpertBusiScopeMdDTO req);

    List<SrmExpertBusiScopeMdPO> dto2PoList(List<SrmExpertBusiScopeMdDTO> dtoList);
}
