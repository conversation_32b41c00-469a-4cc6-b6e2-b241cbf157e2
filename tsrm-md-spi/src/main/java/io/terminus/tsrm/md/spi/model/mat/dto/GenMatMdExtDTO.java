package io.terminus.tsrm.md.spi.model.mat.dto;


import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.erp.md.spi.model.dto.mat.GenMatMdDTO;
import lombok.experimental.FieldNameConstants;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 物料主数据定义表(GenMatMd)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-26 11:33:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@ExtraMetaModel
public class GenMatMdExtDTO extends GenMatMdDTO {
    private static final long serialVersionUID = 811235111101598437L;

    @ApiModelProperty("简称")
    private String extWqShortName;

    @ApiModelProperty("助记码")
    private String extWqMnemonicCode;

    @ApiModelProperty("条形码")
    private String extWqBarcode;

    @ApiModelProperty("规格型号")
    private String extWqSpecification;

    @ApiModelProperty("材质")
    private String extWqMaterialQuality;

    @ApiModelProperty("用途")
    private String extWqUsage;

    @ApiModelProperty("收货标准")
    private String extWqReceiveStandard;

    @ApiModelProperty("备注")
    private String extWqRemark;

    @ApiModelProperty("图片")
    private String extWqImage;

    @ApiModelProperty("超发阈值")
    private BigDecimal extWqOverThreshold;

    @MetaModelField
    @ApiModelProperty("默认采购单位")
    private Long extPurUom;

    @ApiModelProperty("是否对接物资系统")
    private Boolean extWqIntegrateGoogssystem;

    @ApiModelProperty("尺寸")
    private String extWqMeasure;

    @ApiModelProperty("主图")
    private String extWqMainImage;

    @ApiModelProperty("是否对接物资系统")
    private String extWqMaterialSystem;

}
