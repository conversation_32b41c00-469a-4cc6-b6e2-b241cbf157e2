package io.terminus.tsrm.md.spi.model.mat.dto;


import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (MatManageSimilarList)传输模型
 *
 * <AUTHOR>
 * @since  2025-04-28 16:52:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MatManageSimilarListDTO extends BaseModel {
    private static final long serialVersionUID = 736473904686441411L;

    @MetaModelField
    @ApiModelProperty("相似物料")
    private Long similarMat;

    @ApiModelProperty("相似度")
    private BigDecimal similarity;

    @MetaModelField
    @ApiModelProperty("当前物料")
    private Long srmMatManagementTrSimilarMatListId;

    @ApiModelProperty("是否同一物料")
    private Boolean sameMat;

}
