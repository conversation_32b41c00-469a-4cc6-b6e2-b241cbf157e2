package io.terminus.tsrm.md.spi.model.mat.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.erp.md.spi.model.po.mat.GenBomHeadMdPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "gen_bom_head_md")
public class ExtWqGenBomHeadMdPO extends GenBomHeadMdPO {

    @ApiModelProperty("安全库存")
    @TableField("`ext_wq_safe_stock`")
    private BigDecimal extWqSafeStock;

    @ApiModelProperty("临界库存")
    @TableField("`ext_wq_min_stock`")
    private BigDecimal extWqMinStock;

    @ApiModelProperty("最高库存")
    @TableField("`ext_wq_max_stock`")
    private BigDecimal extWqMaxStock;

    @ApiModelProperty("关联寄售补货规则")
    @TableField("`hrt_wq_pur_con_stock_rules_id`")
    private Long hrtWqPurConStockRulesId;

    @ApiModelProperty("关联价格目录")
    @TableField("`ext_wq_rec_pur_md`")
    private Long extWqRecPurMd;
}
