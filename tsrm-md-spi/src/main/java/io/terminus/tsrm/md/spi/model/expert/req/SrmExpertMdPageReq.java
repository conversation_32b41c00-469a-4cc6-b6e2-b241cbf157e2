package io.terminus.tsrm.md.spi.model.expert.req;


import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertBusiScopeMdDTO;
import io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertMatCateMdDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SRM_专家库(SrmExpertMd)分页查询请求
 *
 * <AUTHOR>
 * @since  2024-12-24 10:35:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SrmExpertMdPageReq extends AbstractPageRequest {
    private static final long serialVersionUID = -55248500822303361L;

    @ApiModelProperty("专家编号")
    private String expertCode;

    @ApiModelProperty("专家姓名")
    private String expertName;

    @ApiModelProperty("专家类型")
    private String expertType;

    @ApiModelProperty("关联员工")
    private Long employeeId;

    @ApiModelProperty("专家证件类型")
    private String expertCertType;

    @ApiModelProperty("专家证件号")
    private String expertCertNum;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("联系邮箱")
    private String contactEmail;

    @ApiModelProperty("专家等级")
    private String expertLevel;

    @ApiModelProperty("来源方式")
    private String recommType;

    @ApiModelProperty("推荐方名称")
    private String recommPartyName;

    @ApiModelProperty("所在国家/地区")
    private Long country;

    @ApiModelProperty("现任公司名称")
    private String expertComName;

    @ApiModelProperty("现任公司统一社会信用代码")
    private String expertComCreditCode;

    @ApiModelProperty("附件")
    private String attachment;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("负责业务范围")
    private List<SrmExpertBusiScopeMdDTO> busiScope;

    @ApiModelProperty("负责物料类目")
    private List<SrmExpertMatCateMdDTO> matCate;

    @ApiModelProperty("详细地址")
    private String addressDetail;

    @ApiModelProperty("地址")
    private Long addressId;

    @ApiModelProperty("用户")
    private Long user;

}
