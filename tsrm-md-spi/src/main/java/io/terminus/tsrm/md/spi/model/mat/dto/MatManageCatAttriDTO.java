package io.terminus.tsrm.md.spi.model.mat.dto;


import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (MatManageCatAttri)传输模型
 *
 * <AUTHOR>
 * @since  2025-04-22 17:20:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MatManageCatAttriDTO extends BaseModel {
    private static final long serialVersionUID = -89278652511331433L;

    @ApiModelProperty("属性名")
    private String attriName;

    @ApiModelProperty("属性数量")
    private BigDecimal attriNum;

    @ApiModelProperty("是否必填")
    private Boolean require;

    @MetaModelField
    @ApiModelProperty("mat_manage_cate_total_attri_list_id")
    private Long matManageCateTotalAttriListId;

    @ApiModelProperty("属性说明")
    private String attriInfo;

    @ApiModelProperty("是否AI推荐")
    private Boolean aiRecommend;
}
