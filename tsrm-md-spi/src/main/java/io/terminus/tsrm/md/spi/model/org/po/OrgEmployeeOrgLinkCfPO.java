package io.terminus.tsrm.md.spi.model.org.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (OrgEmployeeOrgLinkCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:04:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_employee_org_link_cf")
public class OrgEmployeeOrgLinkCfPO extends BaseModel {
    private static final long serialVersionUID = -71581506801803013L;

    @ApiModelProperty("组织单元")
    @TableField("`org_unit_id`")
    private Long orgUnitId;

    @ApiModelProperty("orgEmployeeMdId")
    @TableField("`employee_id`")
    private Long employeeId;

}
