package io.terminus.tsrm.md.spi.model.org.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (OrgEmployeeRoleLinkCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:28:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgEmployeeRoleLinkCfDTO extends BaseModel {
    private static final long serialVersionUID = 778771214885868161L;

    @ApiModelProperty("角色")
    private Long roleId;

    @MetaModelField
    @ApiModelProperty("orgEmployeeMdId")
    private Long employeeId;

    @ApiModelProperty("角色名称")
    private String roleName;

}
