package io.terminus.tsrm.md.spi.convert.mat;

import io.terminus.tsrm.md.spi.model.mat.po.ExtWqGenBomHeadMdPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ExtWqGenBomHeadMdExtConverter {

    @Mapping(target = "extra", expression = "java(convertToExtraMap(dto))")
    List<ExtWqGenBomHeadMdPO> convert(List<ExtWqGenBomHeadMdPO> dto);

}