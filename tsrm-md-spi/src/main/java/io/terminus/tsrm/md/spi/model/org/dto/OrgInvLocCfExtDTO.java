package io.terminus.tsrm.md.spi.model.org.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.erp.md.spi.model.dto.org.OrgInvLocCfDTO;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

/**
 * 库存组织配置表(OrgInvOrgCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-10-31 14:33:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@ExtraMetaModel
public class OrgInvLocCfExtDTO extends OrgInvLocCfDTO {
    private static final long serialVersionUID = 955213960591044218L;

    @ApiModelProperty("仓库归属类型")
    private String extBelongType;

    @ApiModelProperty("NCC组织编码")
    private String extWqNccCode;

    @ApiModelProperty("NC组织编码")
    private String extWqNcCode;

    @ApiModelProperty("SAP组织编码")
    private String extWqSapCode;

    @ApiModelProperty("K3组织编码")
    private String extWqK3Code;

    @MetaModelField
    @ApiModelProperty("需求单位")
    private Long extWqInvOrg;

    @ApiModelProperty("集成ERP")
    private String extWqErpType;

}
