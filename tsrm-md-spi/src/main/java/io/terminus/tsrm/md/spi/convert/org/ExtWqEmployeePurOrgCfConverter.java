package io.terminus.tsrm.md.spi.convert.org;

import io.terminus.tsrm.md.spi.model.org.dto.ExtWqEmployeePurOrgCfDTO;
import io.terminus.tsrm.md.spi.model.org.po.ExtWqEmployeePurOrgCfPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * (ExtWqEmployeePurOrgCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-27 11:32:28
 */
@Mapper(componentModel = "spring")
public interface ExtWqEmployeePurOrgCfConverter {

    ExtWqEmployeePurOrgCfDTO po2Dto(ExtWqEmployeePurOrgCfPO req);

    List<ExtWqEmployeePurOrgCfDTO> po2DtoList(List<ExtWqEmployeePurOrgCfPO> poList);

    ExtWqEmployeePurOrgCfPO dto2Po(ExtWqEmployeePurOrgCfDTO req);

    List<ExtWqEmployeePurOrgCfPO> dto2PoList(List<ExtWqEmployeePurOrgCfDTO> dtoList);
}
