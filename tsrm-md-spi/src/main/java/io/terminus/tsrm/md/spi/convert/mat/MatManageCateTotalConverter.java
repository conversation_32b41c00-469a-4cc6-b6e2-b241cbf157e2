package io.terminus.tsrm.md.spi.convert.mat;

import io.terminus.tsrm.md.spi.model.mat.po.MatManageCateTotalPO;
import io.terminus.tsrm.md.spi.model.mat.dto.MatManageCateTotalDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * (MatManageCateTotal)结构映射器
 *
 * <AUTHOR>
 * @since  2025-04-22 17:20:51
 */
@Mapper(componentModel = "spring")
public interface MatManageCateTotalConverter {

    MatManageCateTotalDTO po2Dto(MatManageCateTotalPO req);

    List<MatManageCateTotalDTO> po2DtoList(List<MatManageCateTotalPO> poList);

    MatManageCateTotalPO dto2Po(MatManageCateTotalDTO req);

    List<MatManageCateTotalPO> dto2PoList(List<MatManageCateTotalDTO> dtoList);
}
