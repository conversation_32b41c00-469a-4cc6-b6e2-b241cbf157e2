package io.terminus.tsrm.md.spi.model.mat.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * TODO: Write something here..
 *
 * <AUTHOR>
 */
@Data
@FieldNameConstants
@ExtraMetaModel
public class MatSyncDTO {

    @ApiModelProperty("物料对象")
    private MatDTO matDTO;

    @ApiModelProperty("操作类型，ADD-新增，EDIT-编辑，DISABLE-停用")
    private String operator;


}
