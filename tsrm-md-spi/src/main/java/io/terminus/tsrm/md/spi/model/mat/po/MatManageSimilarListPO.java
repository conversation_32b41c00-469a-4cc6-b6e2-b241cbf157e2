package io.terminus.tsrm.md.spi.model.mat.po;


import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (MatManageSimilarList)存储模型
 *
 * <AUTHOR>
 * @since  2025-04-28 16:52:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "mat_manage_similar_list")
public class MatManageSimilarListPO extends BaseModel {
    private static final long serialVersionUID = -76506960625112865L;

    @ApiModelProperty("相似物料")
    @TableField("`similar_mat`")
    private Long similarMat;

    @ApiModelProperty("相似度")
    @TableField("`similarity`")
    private BigDecimal similarity;

    @ApiModelProperty("当前物料")
    @TableField("`srm_mat_management_tr_similar_mat_list_id`")
    private Long srmMatManagementTrSimilarMatListId;

    @ApiModelProperty("是否同一物料")
    @TableField("`same_mat`")
    private Boolean sameMat;

}
