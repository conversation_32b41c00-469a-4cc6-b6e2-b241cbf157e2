package io.terminus.tsrm.md.spi.convert.org;


import io.terminus.erp.md.spi.model.dto.vend.GenVendInfoMdDTO;
import io.terminus.tsrm.md.spi.model.org.dto.OrgEmployeeRoleLinkCfExtDTO;
import io.terminus.tsrm.md.spi.model.org.po.OrgEmployeeRoleLinkCfPO;
import org.apache.commons.collections.MapUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * (OrgEmployeeRoleLinkCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-27 11:27:09
 */
@Mapper(componentModel = "spring")
public interface OrgEmployeeRoleLinkCfExtConverter {

    @Mapping(target = "extra", expression = "java(convertToExtraMap(dto))")
    GenVendInfoMdDTO convert(OrgEmployeeRoleLinkCfExtDTO dto);

    default OrgEmployeeRoleLinkCfExtDTO convert(OrgEmployeeRoleLinkCfPO po) {
        OrgEmployeeRoleLinkCfExtDTO res = new OrgEmployeeRoleLinkCfExtDTO();
        this.convert(res, po);
        return res;
    }

    List<GenVendInfoMdDTO> convertToDtoList(List<OrgEmployeeRoleLinkCfExtDTO> req);

    List<OrgEmployeeRoleLinkCfExtDTO> convertToExtDtoList(List<OrgEmployeeRoleLinkCfPO> req);


    @Mapping(target = "extra", expression = "java(convertDtoExtFields(dto,po))")
    void convert(@MappingTarget OrgEmployeeRoleLinkCfExtDTO dto, OrgEmployeeRoleLinkCfPO po);

    default Map<String, Object> convertToExtraMap(OrgEmployeeRoleLinkCfExtDTO dto){
        return this.buildExtraMap(dto);
    }

    default Map<String, Object> convertDtoExtFields(@MappingTarget OrgEmployeeRoleLinkCfExtDTO dto, OrgEmployeeRoleLinkCfPO po) {
        this.buildFields(po.getExtra(),dto);
        return po.getExtra();
    }


    default Map<String, Object> buildExtraMap(OrgEmployeeRoleLinkCfExtDTO dto) {
        Map<String, Object> extra = dto.getExtra();
        if (MapUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        return extra;
    }

    default void buildFields(Map<String, Object> extra, OrgEmployeeRoleLinkCfExtDTO dto) {
        if (MapUtils.isEmpty(extra)) {
            return;
        }
    }
}
