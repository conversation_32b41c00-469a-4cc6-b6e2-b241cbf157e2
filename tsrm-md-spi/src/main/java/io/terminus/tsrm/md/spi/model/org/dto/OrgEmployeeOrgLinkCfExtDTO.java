package io.terminus.tsrm.md.spi.model.org.dto;


import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
/**
 * (OrgEmployeeOrgLinkCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:27:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@ExtraMetaModel
public class OrgEmployeeOrgLinkCfExtDTO extends OrgEmployeeOrgLinkCfDTO {
    private static final long serialVersionUID = -65524743213102128L;

}
