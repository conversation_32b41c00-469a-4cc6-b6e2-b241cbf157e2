package io.terminus.tsrm.md.spi.model.org.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ExtWqEmployeeAdmOrgCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:32:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ext_wq_employee_adm_org_cf")
public class ExtWqEmployeeAdmOrgCfPO extends BaseModel {
    private static final long serialVersionUID = -12556688533143778L;

    @ApiModelProperty("行政组织")
    @TableField("`adm_org`")
    private Long admOrg;

    @ApiModelProperty("组织角色")
    @TableField("`org_role`")
    private Long orgRole;

    @ApiModelProperty("orgEmployeeMdId")
    @TableField("`org_employee_md_id`")
    private Long orgEmployeeMdId;

    @ApiModelProperty("是否领导")
    @TableField("`leader`")
    private Boolean leader;

    @ApiModelProperty("需求单位")
    @TableField("`inv_org`")
    private Long invOrg;

    @ApiModelProperty("所属公司")
    @TableField("`com_info`")
    private Long comInfo;

}
