package io.terminus.tsrm.md.spi.model.org.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (ExtWqGenComGraphicSealCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-28 15:11:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExtWqGenComGraphicSealCfDTO extends BaseModel {
    private static final long serialVersionUID = -76429980356740026L;

    @ApiModelProperty("图形章类型")
    private String graphicSealType;

    @ApiModelProperty("图形章")
    private String graphicSeal;

    @MetaModelField
    @ApiModelProperty("gen_com_type_cf_id")
    private Long genComTypeCfId;

}
