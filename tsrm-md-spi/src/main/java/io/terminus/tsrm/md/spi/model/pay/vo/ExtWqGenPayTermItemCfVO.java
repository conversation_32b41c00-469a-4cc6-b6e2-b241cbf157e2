package io.terminus.tsrm.md.spi.model.pay.vo;


import java.math.BigDecimal;
import java.util.ArrayList;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ExtWqGenPayTermItemCf)视图模型
 *
 * <AUTHOR>
 * @since  2023-09-26 14:05:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExtWqGenPayTermItemCfVO extends BaseModel {
    private static final long serialVersionUID = 540626866729536664L;

    @ApiModelProperty("分期")
    private Long stage;

    @ApiModelProperty("付款类型")
    private String payType;

    @ApiModelProperty("付款比例")
    private BigDecimal payRatio;

    @ApiModelProperty("生效日期类型")
    private String effectiveDateType;

    @ApiModelProperty("延迟天数")
    private Long delayDays;

    @ApiModelProperty("结算方式")
    private String settlementMethod;

}
