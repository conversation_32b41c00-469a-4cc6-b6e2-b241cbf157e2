package io.terminus.tsrm.md.spi.model.org.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (OrgEmployeeRoleLinkCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:04:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_employee_role_link_cf")
public class OrgEmployeeRoleLinkCfPO extends BaseModel {
    private static final long serialVersionUID = -52802221515086144L;

    @ApiModelProperty("角色")
    @TableField("`role_id`")
    private Long roleId;

    @ApiModelProperty("orgEmployeeMdId")
    @TableField("`employee_id`")
    private Long employeeId;

    @ApiModelProperty("角色名称")
    @TableField("`role_name`")
    private String roleName;

}
