package io.terminus.tsrm.md.spi.model.mat.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ExtWqGenMatComOrgMd)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-26 13:48:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ext_wq_gen_mat_com_org_md")
public class ExtWqGenMatComOrgMdPO extends BaseModel {
    private static final long serialVersionUID = -81188571177392790L;

    @ApiModelProperty("公司组织")
    @TableField("`com_org`")
    private Long comOrg;

    @ApiModelProperty("是否平衡利库")
    @TableField("`balance_stock`")
    private Boolean balanceStock;

    @ApiModelProperty("范围")
    @TableField("`scope`")
    private String scope;

    @ApiModelProperty("物料")
    @TableField("`material`")
    private Long material;

    @ApiModelProperty("物料编码")
    @TableField("`material_code`")
    private String materialCode;

}
