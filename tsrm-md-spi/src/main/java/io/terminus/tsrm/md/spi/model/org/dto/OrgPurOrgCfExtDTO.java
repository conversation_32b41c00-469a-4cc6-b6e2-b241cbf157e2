package io.terminus.tsrm.md.spi.model.org.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.erp.md.spi.model.dto.org.OrgPurOrgCfDTO;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

/**
 * 采购组织表(OrgPurOrgCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-28 15:09:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@ExtraMetaModel
public class OrgPurOrgCfExtDTO extends OrgPurOrgCfDTO {

    @MetaModelField
    @ApiModelProperty("供应商库配置")
    private Long extWqVendWarehouse;
}
