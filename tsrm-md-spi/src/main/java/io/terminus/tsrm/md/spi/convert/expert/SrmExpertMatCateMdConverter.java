package io.terminus.tsrm.md.spi.convert.expert;

import io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertMatCateMdDTO;
import io.terminus.tsrm.md.spi.model.expert.po.SrmExpertMatCateMdPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * SRM专家可负责物料类目范围(SrmExpertMatCateMd)结构映射器
 *
 * <AUTHOR>
 * @since 2024-11-05 14:07:08
 */
@Mapper(componentModel = "spring")
public interface SrmExpertMatCateMdConverter {

    @Mapping(target = "expertId.id", source = "expertId")
    @Mapping(target = "matCateId.id", source = "matCateId")
    SrmExpertMatCateMdDTO po2Dto(SrmExpertMatCateMdPO req);

    List<SrmExpertMatCateMdDTO> po2DtoList(List<SrmExpertMatCateMdPO> poList);

    @Mapping(target = "expertId", source = "expertId.id")
    @Mapping(target = "matCateId", source = "matCateId.id")
    SrmExpertMatCateMdPO dto2Po(SrmExpertMatCateMdDTO req);

    List<SrmExpertMatCateMdPO> dto2PoList(List<SrmExpertMatCateMdDTO> dtoList);
}
