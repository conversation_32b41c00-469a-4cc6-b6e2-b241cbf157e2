package io.terminus.tsrm.md.spi.convert.stock;

import io.terminus.tsrm.md.spi.model.stock.dto.InvRecordTrDTO;
import io.terminus.tsrm.md.spi.model.stock.po.InvRecordTrPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 库存记录表(InvRecordTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-11-06 16:25:25
 */
@Mapper(componentModel = "spring")
public interface InvRecordTrConverter {

    InvRecordTrDTO po2Dto(InvRecordTrPO req);

    List<InvRecordTrDTO> po2DtoList(List<InvRecordTrPO> poList);

    InvRecordTrPO dto2Po(InvRecordTrDTO req);

    List<InvRecordTrPO> dto2PoList(List<InvRecordTrDTO> dtoList);
}
