package io.terminus.tsrm.md.spi.model.expert.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SRM专家可参与业务范围(SrmExpertBusiScopeMd)存储模型
 *
 * <AUTHOR>
 * @since  2024-11-05 14:07:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "srm_expert_busi_scope_md")
public class SrmExpertBusiScopeMdPO extends BaseModel {
    private static final long serialVersionUID = -10185918541879486L;

    @ApiModelProperty("业务专家")
    @TableField("`expert_id`")
    private Long expertId;

    @ApiModelProperty("业务范围")
    @TableField("`busi_scope`")
    private String busiScope;

}
