package io.terminus.tsrm.md.spi.model.mat.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (ExtWqGenMatComOrgMd)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-26 13:48:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExtWqGenMatComOrgMdDTO extends BaseModel {
    private static final long serialVersionUID = -39246833988262258L;

    @MetaModelField
    @ApiModelProperty("公司组织")
    private Long comOrg;

    @ApiModelProperty("是否平衡利库")
    private Boolean balanceStock;

    @ApiModelProperty("范围")
    private String scope;

    @MetaModelField
    @ApiModelProperty("物料")
    private Long material;

    @ApiModelProperty("物料编码")
    private String materialCode;

}
