package io.terminus.tsrm.md.spi.convert.org;


import io.terminus.erp.md.spi.model.dto.base.GenComTypeCfDTO;
import io.terminus.erp.md.spi.model.po.base.GenComTypeCfPO;
import io.terminus.tsrm.md.spi.model.org.dto.ExtWqGenComGraphicSealCfDTO;
import io.terminus.tsrm.md.spi.model.org.dto.GenComTypeCfExtDTO;
import org.apache.commons.collections.MapUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 公司配置表(GenComTypeCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-28 15:09:24
 */
@Mapper(componentModel = "spring")
public interface GenComTypeCfExtConverter {

    @Mapping(target = "extra", expression = "java(convertToExtraMap(dto))")
    GenComTypeCfDTO convert(GenComTypeCfExtDTO dto);

    default GenComTypeCfExtDTO convert(GenComTypeCfPO po) {
        GenComTypeCfExtDTO res = new GenComTypeCfExtDTO();
        this.convert(res, po);
        return res;
    }

    List<GenComTypeCfDTO> convertToDtoList(List<GenComTypeCfExtDTO> req);

    List<GenComTypeCfExtDTO> convertToExtDtoList(List<GenComTypeCfPO> req);


    @Mapping(target = "extra", expression = "java(convertDtoExtFields(dto,po))")
    void convert(@MappingTarget GenComTypeCfExtDTO dto, GenComTypeCfPO po);

    default Map<String, Object> convertToExtraMap(GenComTypeCfExtDTO dto){
        return this.buildExtraMap(dto);
    }

    default Map<String, Object> convertDtoExtFields(@MappingTarget GenComTypeCfExtDTO dto, GenComTypeCfPO po) {
        this.buildFields(po.getExtra(),dto);
        return po.getExtra();
    }


    default Map<String, Object> buildExtraMap(GenComTypeCfExtDTO dto) {
        Map<String, Object> extra = dto.getExtra();
        if (MapUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        if (Objects.nonNull(dto.getExtWqCapitalUnit())) {
            extra.put(GenComTypeCfExtDTO.Fields.extWqCapitalUnit, dto.getExtWqCapitalUnit());
        }

        if (Objects.nonNull(dto.getExtWqIsSignature())) {
            extra.put(GenComTypeCfExtDTO.Fields.extWqIsSignature, dto.getExtWqIsSignature());
        }

        if (Objects.nonNull(dto.getExtWqGraphicSeal())) {
            extra.put(GenComTypeCfExtDTO.Fields.extWqGraphicSeal, dto.getExtWqGraphicSeal());
        }

        return extra;
    }

    default void buildFields(Map<String, Object> extra, GenComTypeCfExtDTO dto) {
        if (MapUtils.isEmpty(extra)) {
            return;
        }
        if (Objects.nonNull(extra.get(GenComTypeCfExtDTO.Fields.extWqCapitalUnit))) {
            dto.setExtWqCapitalUnit((String) extra.get(GenComTypeCfExtDTO.Fields.extWqCapitalUnit));
        }

        if (Objects.nonNull(extra.get(GenComTypeCfExtDTO.Fields.extWqIsSignature))) {
            dto.setExtWqIsSignature((Boolean) extra.get(GenComTypeCfExtDTO.Fields.extWqIsSignature));
        }

        if (Objects.nonNull(extra.get(GenComTypeCfExtDTO.Fields.extWqGraphicSeal))) {
            dto.setExtWqGraphicSeal((List<ExtWqGenComGraphicSealCfDTO>) extra.get(GenComTypeCfExtDTO.Fields.extWqGraphicSeal));
        }

    }

}
