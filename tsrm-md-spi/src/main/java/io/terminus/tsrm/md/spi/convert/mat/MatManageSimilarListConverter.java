package io.terminus.tsrm.md.spi.convert.mat;

import io.terminus.tsrm.md.spi.model.mat.po.MatManageSimilarListPO;
import io.terminus.tsrm.md.spi.model.mat.dto.MatManageSimilarListDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * (MatManageSimilarList)结构映射器
 *
 * <AUTHOR>
 * @since  2025-04-28 16:52:33
 */
@Mapper(componentModel = "spring")
public interface MatManageSimilarListConverter {

    MatManageSimilarListDTO po2Dto(MatManageSimilarListPO req);

    List<MatManageSimilarListDTO> po2DtoList(List<MatManageSimilarListPO> poList);

    MatManageSimilarListPO dto2Po(MatManageSimilarListDTO req);

    List<MatManageSimilarListPO> dto2PoList(List<MatManageSimilarListDTO> dtoList);
}
