package io.terminus.tsrm.md.spi.msg.pay;

/**
 * TODO: demo 测试
 *
 * <AUTHOR>
 */
public interface GenPayTermHeadMsg {
    /**
     * 预付款类型的款项，只允许存在一项
     */
    String PREPAID_CANNOT_GREATER_THAN_ONE = "MD.gen.pay.term.prepaid.limit";

    /**
     * 质保金类型的款项，只允许存在一项
     */
    String RETENTION_MONEY_CANNOT_GREATER_THAN_ONE = "MD.gen.pay.term.retention.money.limit";

    /**
     * 编码已存在
     */
    String CODE_MUST_UNIQUE = "MD.gen.pay.term.code.exists";

    /**
     * 分期不能重复
     */
    String STAGE_MUST_UNIQUE = "MD.gen.pay.term.stage.duplicate";
}
