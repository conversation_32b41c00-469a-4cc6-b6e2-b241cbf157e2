package io.terminus.tsrm.md.spi.model.base.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@ExtraMetaModel
public class BankSyncDTO {

    @ApiModelProperty("联行号")
    private String bankCode;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行类别/银行名称(对应所属银行对象)")
    private String bankType;

}
