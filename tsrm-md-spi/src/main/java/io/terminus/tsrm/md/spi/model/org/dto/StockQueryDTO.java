package io.terminus.tsrm.md.spi.model.org.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import io.terminus.tsrm.md.spi.model.mat.dto.GenMatMdExtDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;

/**
 * 库存查询dto
 *
 * <AUTHOR>
 * @date 2023/10/31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
public class StockQueryDTO extends BaseModel {

    @MetaModelField
    @ApiModelProperty("物料")
    private GenMatMdExtDTO mat;

    @MetaModelField
    @ApiModelProperty("仓库")
    private OrgInvLocCfExtDTO invLoc;

    @ApiModelProperty("库存数量")
    private BigDecimal stockNum;

}
