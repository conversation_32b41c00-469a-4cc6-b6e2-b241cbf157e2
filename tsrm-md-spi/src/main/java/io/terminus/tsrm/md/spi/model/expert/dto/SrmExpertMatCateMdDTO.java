package io.terminus.tsrm.md.spi.model.expert.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.erp.md.spi.model.dto.GenMatCateMdDTO;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SRM专家可负责物料类目范围(SrmExpertMatCateMd)传输模型
 *
 * <AUTHOR>
 * @since 2024-11-05 14:07:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SrmExpertMatCateMdDTO extends BaseModel {
    private static final long serialVersionUID = -91102631834431230L;

    @MetaModelField
    @ApiModelProperty("寻源专家")
    private SrmExpertMdDTO expertId;

    @MetaModelField
    @ApiModelProperty("物料分类id")
    private GenMatCateMdDTO matCateId;

    @ApiModelProperty("物料分类code")
    private String matCateCode;

}
