package io.terminus.tsrm.md.spi.model.stock.dto;


import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 库存记录表(InvRecordTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-11-06 16:25:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InvRecordTrDTO extends BaseModel {
    private static final long serialVersionUID = -57555927279009120L;

    @ApiModelProperty("库存记录号")
    private String invRecordCode;

    @ApiModelProperty("关联需求计划")
    private String relMrp;

    @ApiModelProperty("关联需求计划行")
    private String relMrpItem;

    @ApiModelProperty("关联单据")
    private String relOrder;

    @ApiModelProperty("关联单据明细")
    private String relOrderItem;

    @MetaModelField
    @ApiModelProperty("物料")
    private Long mat;

    @MetaModelField
    @ApiModelProperty("仓库")
    private Long invLoc;

    @ApiModelProperty("数量")
    private BigDecimal qty;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("预占用类型")
    private String preOccupy;

    @ApiModelProperty("执行时间")
    private LocalDateTime executeTime;

    @MetaModelField
    @ApiModelProperty("invOccupyTrId")
    private Long invOccupyTrId;

    @ApiModelProperty("平衡利库ID")
    private Long stockBalanceDetailTrId;

}
