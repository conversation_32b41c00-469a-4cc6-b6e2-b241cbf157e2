package io.terminus.tsrm.md.spi.model.org.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ExtWqGenComGraphicSealCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-28 15:11:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ext_wq_gen_com_graphic_seal_cf")
public class ExtWqGenComGraphicSealCfPO extends BaseModel {
    private static final long serialVersionUID = 713454248364090175L;

    @ApiModelProperty("图形章类型")
    @TableField("`graphic_seal_type`")
    private String graphicSealType;

    @ApiModelProperty("图形章")
    @TableField("`graphic_seal`")
    private String graphicSeal;

    @ApiModelProperty("gen_com_type_cf_id")
    @TableField("`gen_com_type_cf_id`")
    private Long genComTypeCfId;

}
