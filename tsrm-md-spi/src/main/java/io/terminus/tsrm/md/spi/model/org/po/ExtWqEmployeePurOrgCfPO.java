package io.terminus.tsrm.md.spi.model.org.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ExtWqEmployeePurOrgCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:32:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ext_wq_employee_pur_org_cf")
public class ExtWqEmployeePurOrgCfPO extends BaseModel {
    private static final long serialVersionUID = 399107699291127541L;

    @ApiModelProperty("采购组织")
    @TableField("`pur_org`")
    private Long purOrg;

    @ApiModelProperty("org_employee_md_id")
    @TableField("`org_employee_md_id`")
    private Long orgEmployeeMdId;

}
