package io.terminus.tsrm.md.spi.model.pay.so;


import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.tsrm.md.spi.model.pay.dto.ExtWqGenPayTermItemCfDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ExtWqGenPayTermHeadCf)搜索模型
 *
 * <AUTHOR>
 * @since  2023-09-26 11:11:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExtWqGenPayTermHeadCfSO extends BaseModel {
    private static final long serialVersionUID = -67356780403983670L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("描述")
    private String remark;

    @ApiModelProperty("NCC付款协议编码")
    private String nccPayTermCode;

    @ApiModelProperty("NC付款协议编码")
    private String ncPayTermCode;

    @ApiModelProperty("K3付款协议编码")
    private String k3PayTermCode;

    @ApiModelProperty("SAP付款协议编码")
    private String sapPayTermCode;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("付款协议明细")
    private List<ExtWqGenPayTermItemCfDTO> payTermItems;

}
