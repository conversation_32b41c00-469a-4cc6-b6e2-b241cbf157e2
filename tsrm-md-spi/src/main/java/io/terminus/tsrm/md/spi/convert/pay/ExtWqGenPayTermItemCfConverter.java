package io.terminus.tsrm.md.spi.convert.pay;

import io.terminus.tsrm.md.spi.model.pay.dto.ExtWqGenPayTermItemCfDTO;
import io.terminus.tsrm.md.spi.model.pay.po.ExtWqGenPayTermItemCfPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * (ExtWqGenPayTermItemCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-26 14:05:22
 */
@Mapper(componentModel = "spring")
public interface ExtWqGenPayTermItemCfConverter {

    ExtWqGenPayTermItemCfDTO po2Dto(ExtWqGenPayTermItemCfPO req);

    List<ExtWqGenPayTermItemCfDTO> po2DtoList(List<ExtWqGenPayTermItemCfPO> poList);

    ExtWqGenPayTermItemCfPO dto2Po(ExtWqGenPayTermItemCfDTO req);

    List<ExtWqGenPayTermItemCfPO> dto2PoList(List<ExtWqGenPayTermItemCfDTO> dtoList);
}
