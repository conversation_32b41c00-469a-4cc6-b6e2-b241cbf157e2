package io.terminus.tsrm.md.spi.model.pay.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ExtWqGenPayTermHeadCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-26 11:11:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ext_wq_gen_pay_term_head_cf")
public class ExtWqGenPayTermHeadCfPO extends BaseModel {
    private static final long serialVersionUID = 171850269865553785L;

    @ApiModelProperty("编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("描述")
    @TableField("`remark`")
    private String remark;

    @ApiModelProperty("NCC付款协议编码")
    @TableField("`ncc_pay_term_code`")
    private String nccPayTermCode;

    @ApiModelProperty("NC付款协议编码")
    @TableField("`nc_pay_term_code`")
    private String ncPayTermCode;

    @ApiModelProperty("K3付款协议编码")
    @TableField("`k3_pay_term_code`")
    private String k3PayTermCode;

    @ApiModelProperty("SAP付款协议编码")
    @TableField("`sap_pay_term_code`")
    private String sapPayTermCode;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

}
