package io.terminus.tsrm.md.spi.model.pay.req;


import java.math.BigDecimal;
import java.util.ArrayList;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ExtWqGenPayTermItemCf)创建请求
 *
 * <AUTHOR>
 * @since  2023-09-26 14:05:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExtWqGenPayTermItemCfCreateReq extends AbstractRequest {
    private static final long serialVersionUID = 599903968681812479L;

    @ApiModelProperty("分期")
    private Long stage;

    @ApiModelProperty("付款类型")
    private String payType;

    @ApiModelProperty("付款比例")
    private BigDecimal payRatio;

    @ApiModelProperty("生效日期类型")
    private String effectiveDateType;

    @ApiModelProperty("延迟天数")
    private Long delayDays;

    @ApiModelProperty("结算方式")
    private String settlementMethod;

}
