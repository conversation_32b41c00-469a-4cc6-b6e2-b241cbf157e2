package io.terminus.tsrm.md.spi.convert.pay;

import io.terminus.tsrm.md.spi.model.pay.dto.ExtWqGenPayTermHeadCfDTO;
import io.terminus.tsrm.md.spi.model.pay.po.ExtWqGenPayTermHeadCfPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * (ExtWqGenPayTermHeadCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-26 11:11:27
 */
@Mapper(componentModel = "spring")
public interface ExtWqGenPayTermHeadCfConverter {

    ExtWqGenPayTermHeadCfDTO po2Dto(ExtWqGenPayTermHeadCfPO req);

    List<ExtWqGenPayTermHeadCfDTO> po2DtoList(List<ExtWqGenPayTermHeadCfPO> poList);

    ExtWqGenPayTermHeadCfPO dto2Po(ExtWqGenPayTermHeadCfDTO req);

    List<ExtWqGenPayTermHeadCfPO> dto2PoList(List<ExtWqGenPayTermHeadCfDTO> dtoList);
}
