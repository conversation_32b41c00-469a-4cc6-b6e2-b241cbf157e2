package io.terminus.tsrm.md.spi.model.org.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (ExtWqEmployeeAdmOrgCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:32:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExtWqEmployeeAdmOrgCfDTO extends BaseModel {
    private static final long serialVersionUID = 596779449642626596L;

    @MetaModelField
    @ApiModelProperty("行政组织")
    private Long admOrg;

    @MetaModelField
    @ApiModelProperty("组织角色")
    private Long orgRole;

    @MetaModelField
    @ApiModelProperty("orgEmployeeMdId")
    private Long orgEmployeeMdId;

    @ApiModelProperty("是否领导")
    private Boolean leader;

    @MetaModelField
    @ApiModelProperty("需求单位")
    private Long invOrg;

    @MetaModelField
    @ApiModelProperty("所属公司")
    private Long comInfo;

}
