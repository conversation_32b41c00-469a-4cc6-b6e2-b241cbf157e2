package io.terminus.tsrm.md.spi.model.org.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 员工信息表(OrgEmployeeMd)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-27 11:19:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgEmployeeBaseMdDTO extends BaseModel {
    private static final long serialVersionUID = -20669855080224711L;

    @MetaModelField
    @ApiModelProperty("用户")
    private Long userId;

    @ApiModelProperty("员工编码")
    private String code;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("员工类型")
    private String type;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机")
    private String mobile;

    @MetaModelField
    @ApiModelProperty("地址")
    private Long addressId;

    @ApiModelProperty("详细地址")
    private String addressDetail;

    @ApiModelProperty("入职日期")
    private LocalDateTime entryAt;

    @ApiModelProperty("离职日期")
    private LocalDateTime resignationAt;

    @ApiModelProperty("角色")
    private List<OrgEmployeeRoleLinkCfDTO> employeeRoleLinkList;

}
