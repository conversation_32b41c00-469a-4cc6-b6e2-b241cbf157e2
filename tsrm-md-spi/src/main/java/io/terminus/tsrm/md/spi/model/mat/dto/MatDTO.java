package io.terminus.tsrm.md.spi.model.mat.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.List;

/**
 * TODO: Write something here..
 *
 * <AUTHOR>
 */
@Data
@FieldNameConstants
@ExtraMetaModel
public class MatDTO {

    @ApiModelProperty("物料编码")
    private String matCode;

    @ApiModelProperty("物料名称")
    private String matName;

    @ApiModelProperty("简称")
    private String extWqShortName;

    /**
     * 传 物料类型模型对应的 code
     */
    @ApiModelProperty("物料类型")
    private String genMatType;

    @ApiModelProperty("用途")
    private String extWqUsage;

    @ApiModelProperty("规格型号")
    private String extWqSpecification;

    @ApiModelProperty("品牌")
    private String  wqBrand;

    @ApiModelProperty("助记码")
    private String extWqMnemonicCode;

    @ApiModelProperty("条形码")
    private String extWqBarcode;

    @ApiModelProperty("图片列表")
    private List<String> imageList;

    /**
     * 传 计量单位配置表对应的 uom_code
     */
    @ApiModelProperty("主计量单位")
    private String mainUnit;

    @ApiModelProperty("辅计量单位")
    private String assistUnit;

    @ApiModelProperty("类目编码")
    private String catCode;

    /**
     * 公司组织
     */
    @ApiModelProperty("所属集团")
    private String comOrgCode;

    /**
     * 采购组织
     */
    @ApiModelProperty("所属组织")
    private String purOrgCode;

    @ApiModelProperty("税码")
    private String taxCode;


}
