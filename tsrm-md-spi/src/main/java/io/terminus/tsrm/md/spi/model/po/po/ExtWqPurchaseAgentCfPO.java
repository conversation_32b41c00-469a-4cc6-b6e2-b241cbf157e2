package io.terminus.tsrm.md.spi.model.po.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * (ExtWqPurchaseAgentCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-08 18:18:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ext_wq_purchase_agent_cf")
public class ExtWqPurchaseAgentCfPO extends BaseModel {
    private static final long serialVersionUID = -74815817547233514L;

    @ApiModelProperty("代理公司")
    @TableField("`agent_com`")
    private Long agentCom;

    @ApiModelProperty("分成比例")
    @TableField("`share_ratio`")
    private BigDecimal shareRatio;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

}
