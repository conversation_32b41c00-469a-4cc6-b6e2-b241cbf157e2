package io.terminus.tsrm.md.spi.model.org.dto;


import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.erp.md.spi.model.dto.base.GenComTypeCfDTO;
import lombok.experimental.FieldNameConstants;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;

/**
 * 公司配置表(GenComTypeCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-28 15:09:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@ExtraMetaModel
public class GenComTypeCfExtDTO extends GenComTypeCfDTO {
    private static final long serialVersionUID = -65498158027594000L;

    @ApiModelProperty("注册资本单位")
    private String extWqCapitalUnit;

    @ApiModelProperty("是否开通签章")
    private Boolean extWqIsSignature;

    @ApiModelProperty("公司图形章")
    private List<ExtWqGenComGraphicSealCfDTO> extWqGraphicSeal;

}
