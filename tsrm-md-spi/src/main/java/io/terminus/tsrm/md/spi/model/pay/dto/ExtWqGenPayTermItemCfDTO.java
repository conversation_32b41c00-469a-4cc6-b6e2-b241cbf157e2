package io.terminus.tsrm.md.spi.model.pay.dto;


import java.math.BigDecimal;
import java.util.ArrayList;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (ExtWqGenPayTermItemCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-26 14:05:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExtWqGenPayTermItemCfDTO extends BaseModel {
    private static final long serialVersionUID = 378408235153534343L;

    @ApiModelProperty("分期")
    private Long stage;

    @ApiModelProperty("付款类型")
    private String payType;

    @ApiModelProperty("付款比例")
    private BigDecimal payRatio;

    @ApiModelProperty("生效日期类型")
    private String effectiveDateType;

    @ApiModelProperty("延迟天数")
    private Long delayDays;

    @ApiModelProperty("结算方式")
    private String settlementMethod;

    @ApiModelProperty("付款协议表Id")
    private Long extWqGenPayTermHeadCfId;

}
