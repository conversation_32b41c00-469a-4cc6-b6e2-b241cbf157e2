package io.terminus.tsrm.md.spi.model.mat.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (SrmMatImportTr)存储模型
 *
 * <AUTHOR>
 * @since  2025-04-23 17:56:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "srm_mat_import_tr")
public class SrmMatImportTrPO extends BaseModel {
    private static final long serialVersionUID = 370880486099920679L;

    @ApiModelProperty("清单名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("健康度")
    @TableField("`health_rate`")
    private Long healthRate;

    @ApiModelProperty("清单附件")
    @TableField("`import_attachment`")
    private String importAttachment;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

    @ApiModelProperty("请求唯一标识")
    @TableField("`request_id`")
    private String requestId;

}
