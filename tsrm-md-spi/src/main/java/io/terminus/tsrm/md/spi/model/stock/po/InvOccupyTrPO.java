package io.terminus.tsrm.md.spi.model.stock.po;


import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存占用表(InvOccupyTr)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-02 17:00:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "inv_occupy_tr")
public class InvOccupyTrPO extends BaseModel {
    private static final long serialVersionUID = 244636764947269741L;

    @ApiModelProperty("物料")
    @TableField("`mat`")
    private Long mat;

    @ApiModelProperty("仓库")
    @TableField("`inv_loc`")
    private Long invLoc;

    @ApiModelProperty("供应商")
    @TableField("`vend`")
    private Long vend;

    @ApiModelProperty("总占用数量")
    @TableField("`total_occupy_qty`")
    private BigDecimal totalOccupyQty;

    @ApiModelProperty("领料占用数量")
    @TableField("`pick_occupy_qty`")
    private BigDecimal pickOccupyQty;

    @ApiModelProperty("调拨占用数量")
    @TableField("`allot_occupy_qty`")
    private BigDecimal allotOccupyQty;

    @ApiModelProperty("预留占用数量")
    @TableField("`reserve_occupy_qty`")
    private BigDecimal reserveOccupyQty;

    @ApiModelProperty("预占用数量")
    @TableField("`pre_occupy_qty`")
    private BigDecimal preOccupyQty;

}
