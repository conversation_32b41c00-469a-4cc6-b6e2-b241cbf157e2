package io.terminus.tsrm.md.spi.convert.org;

import io.terminus.tsrm.md.spi.model.org.dto.ExtWqGenComGraphicSealCfDTO;
import io.terminus.tsrm.md.spi.model.org.po.ExtWqGenComGraphicSealCfPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * (ExtWqGenComGraphicSealCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-28 15:11:24
 */
@Mapper(componentModel = "spring")
public interface ExtWqGenComGraphicSealCfConverter {

    ExtWqGenComGraphicSealCfDTO po2Dto(ExtWqGenComGraphicSealCfPO req);

    List<ExtWqGenComGraphicSealCfDTO> po2DtoList(List<ExtWqGenComGraphicSealCfPO> poList);

    ExtWqGenComGraphicSealCfPO dto2Po(ExtWqGenComGraphicSealCfDTO req);

    List<ExtWqGenComGraphicSealCfPO> dto2PoList(List<ExtWqGenComGraphicSealCfDTO> dtoList);
}
