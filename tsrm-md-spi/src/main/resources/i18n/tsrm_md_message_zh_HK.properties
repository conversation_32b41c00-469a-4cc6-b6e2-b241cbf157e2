# BankExtService
MD.bank.code.empty=éè¡ä»£ç¢¼ä¸è½çºç©º

# TsrmOrgAction
MD.org.duplicate=å­å¨éè¤çéè¯çµç¹!

# TsrmExpertService
MD.expert.phone.registered=ææ©èå·²è¨»åï¼è«ç¢ºèªèª¿æ´!
MD.expert.employee.already.linked=å¡å·¥å·²ç¶è¢«éè¯ï¼è«ç¢ºèªèª¿æ´!
MD.expert.phone.already.used=ææ©èå·²è¢«ä½¿ç¨ï¼è«ç¢ºèªèª¿æ´

# MatExtService
MD.mat.sync.dto.list.empty=ç©æåæ­¥åè¡¨çºç©º
MD.mat.empty=ç©æçºç©º!
MD.mat.code.empty=ç©æä»£ç¢¼çºç©º!
MD.mat.type.empty=ç©æé¡åçºç©º!
MD.mat.main.unit.empty=ä¸»å®ä½çºç©º!
MD.mat.type.not.exist=ç©æé¡å: {0} ä¸å­å¨
MD.mat.main.unit.not.exist=ä¸»å®ä½: {0} ä¸å­å¨

# GenPayTermHeadMsg
MD.gen.pay.term.prepaid.limit=é ä»æ¬¾é¡åçæ¬¾é ï¼åªåè¨±å­å¨ä¸é 
MD.gen.pay.term.retention.money.limit=è³ªä¿éé¡åçæ¬¾é ï¼åªåè¨±å­å¨ä¸é 
MD.gen.pay.term.code.exists=ç·¨ç¢¼å·²å­å¨
MD.gen.pay.term.stage.duplicate=åæä¸è½éè¤
