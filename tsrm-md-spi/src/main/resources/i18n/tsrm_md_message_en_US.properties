# BankExtService
MD.bank.code.empty=Bank code cannot be empty

# TsrmOrgAction
MD.org.duplicate=Duplicate associated organization exists!

# TsrmExpertService
MD.expert.phone.registered=Phone number already registered, please check and adjust!
MD.expert.employee.already.linked=Employee has already been linked, please check and adjust!
MD.expert.phone.already.used=Phone number is already in use, please check and adjust

# MatExtService
MD.mat.sync.dto.list.empty=Material synchronization list is empty
MD.mat.empty=Material is empty!
MD.mat.code.empty=Material code is empty!
MD.mat.type.empty=Material type is empty!
MD.mat.main.unit.empty=Main unit is empty!
MD.mat.type.not.exist=Material type: {0} does not exist
MD.mat.main.unit.not.exist=Main unit: {0} does not exist

# GenPayTermHeadMsg
MD.gen.pay.term.prepaid.limit=Only one prepayment type item is allowed
MD.gen.pay.term.retention.money.limit=Only one retention money type item is allowed
MD.gen.pay.term.code.exists=Code already exists
MD.gen.pay.term.stage.duplicate=Installment cannot be duplicated
