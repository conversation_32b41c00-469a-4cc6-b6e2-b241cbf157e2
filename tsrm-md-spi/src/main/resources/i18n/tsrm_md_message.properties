# BankExtService
MD.bank.code.empty=é¶è¡ä»£ç ä¸è½ä¸ºç©º

# TsrmOrgAction
MD.org.duplicate=å­å¨éå¤çå³èç»ç»!

# TsrmExpertService
MD.expert.phone.registered=ææºå·å·²æ³¨åï¼è¯·ç¡®è®¤è°æ´!
MD.expert.employee.already.linked=åå·¥å·²ç»è¢«å³èï¼è¯·ç¡®è®¤è°æ´!
MD.expert.phone.already.used=ææºå·å·²è¢«ä½¿ç¨ï¼è¯·ç¡®è®¤è°æ´

# MatExtService
MD.mat.sync.dto.list.empty=ç©æåæ­¥åè¡¨ä¸ºç©º
MD.mat.empty=ç©æä¸ºç©º!
MD.mat.code.empty=ç©æä»£ç ä¸ºç©º!
MD.mat.type.empty=ç©æç±»åä¸ºç©º!
MD.mat.main.unit.empty=ä¸»åä½ä¸ºç©º!
MD.mat.type.not.exist=ç©æç±»å: {0} ä¸å­å¨
MD.mat.main.unit.not.exist=ä¸»åä½: {0} ä¸å­å¨

# GenPayTermHeadMsg
MD.gen.pay.term.prepaid.limit=é¢ä»æ¬¾ç±»åçæ¬¾é¡¹ï¼åªåè®¸å­å¨ä¸é¡¹
MD.gen.pay.term.retention.money.limit=è´¨ä¿éç±»åçæ¬¾é¡¹ï¼åªåè®¸å­å¨ä¸é¡¹
MD.gen.pay.term.code.exists=ç¼ç å·²å­å¨
MD.gen.pay.term.stage.duplicate=åæä¸è½éå¤
