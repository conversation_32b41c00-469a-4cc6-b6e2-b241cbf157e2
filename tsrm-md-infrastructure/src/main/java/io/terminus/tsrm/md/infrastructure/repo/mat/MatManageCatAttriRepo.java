package io.terminus.tsrm.md.infrastructure.repo.mat;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.tsrm.md.spi.model.mat.po.MatManageCatAttriPO;

import java.util.List;

/**
 * (MatManageCatAttri)表数据库访问层
 *
 * <AUTHOR>
 * @since  2025-04-22 17:20:51
 */
@Repository
public interface MatManageCatAttriRepo extends BaseRepository<MatManageCatAttriPO> {

    default void saveList(List<MatManageCatAttriPO> matManageCatAttriPOList, Long mainId) {
        // 删除当前物料类目所有属性然后重新插入
        QueryWrapper<MatManageCatAttriPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mat_manage_cate_total_attri_list_id", mainId);
        this.delete(queryWrapper);
        this.insertBatch(matManageCatAttriPOList);
    }
}
