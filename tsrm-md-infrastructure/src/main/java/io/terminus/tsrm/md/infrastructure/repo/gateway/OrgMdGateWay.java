package io.terminus.tsrm.md.infrastructure.repo.gateway;

import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.erp.api.annotation.TService;
import io.terminus.trantor.org.spi.model.dto.OrgStructMdDetailDTO;
import io.terminus.trantor.org.spi.model.dto.OrgStructMdSaveDTO;
import io.terminus.trantor.org.spi.model.dto.OrgStructPageQueryDTO;
import io.terminus.trantor2.doc.annotation.Action;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * TODO: demo 测试
 *
 * <AUTHOR>
 */
@TService
public interface OrgMdGateWay {

    @Action(name = "保存EHR组织单元", value = "ORG_STRUCT_MD_SAVE_ACTION")
    OrgStructMdSaveDTO save(@RequestBody OrgStructMdSaveDTO request);

    @Action(name = "启动EHR组织单元", value = "ORG_STRUCT_MD_ENABLED_ACTION")
    Response<Void> enabled(@RequestBody IdRequest request);

    @Action(name = "新版组织分页查询", value = "ORG_API_STRUCT_MD_PAGE_ACTION")
    Response<Paging<OrgStructMdDetailDTO>> paging(@RequestBody OrgStructPageQueryDTO request);

}
