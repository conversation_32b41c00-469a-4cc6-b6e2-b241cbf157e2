package io.terminus.tsrm.md.infrastructure.repo.stock;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.tsrm.md.spi.model.stock.po.InvRecordTrPO;

/**
 * 库存记录表(InvRecordTr)表数据库访问层
 *
 * <AUTHOR>
 * @since  2023-11-02 15:43:19
 */
@Repository
public interface InvRecordTrRepo extends BaseRepository<InvRecordTrPO> {

    default InvRecordTrPO queryByStockBalanceDetailId(Long stockBalanceDetailId){
        QueryWrapper<InvRecordTrPO> invRecordTrPOQueryWrapper = new QueryWrapper<>();
        invRecordTrPOQueryWrapper.lambda().and(condition-> condition.eq(InvRecordTrPO::getStockBalanceDetailTrId,stockBalanceDetailId));
        return this.selectOne(invRecordTrPOQueryWrapper);
    }

}
