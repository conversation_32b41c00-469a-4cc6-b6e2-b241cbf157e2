package io.terminus.tsrm.md.infrastructure.repo.expert;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.tsrm.md.spi.model.mat.po.ExtWqGenMatComOrgMdPO;
import org.springframework.stereotype.Repository;
import io.terminus.tsrm.md.spi.model.expert.po.SrmExpertMdPO;

import java.util.List;

/**
 * SRM_专家库(SrmExpertMd)表数据库访问层
 *
 * <AUTHOR>
 * @since  2024-12-24 10:35:26
 */
@Repository
public interface SrmExpertMdRepo extends BaseRepository<SrmExpertMdPO> {

    default List<SrmExpertMdPO> selectByEmployeeId(Long employeeId) {
        QueryWrapper<SrmExpertMdPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().and(w -> w.eq(SrmExpertMdPO::getEmployeeId, employeeId));
        return this.selectList(wrapper);
    }

}
