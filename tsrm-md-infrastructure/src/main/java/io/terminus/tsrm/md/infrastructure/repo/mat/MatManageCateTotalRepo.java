package io.terminus.tsrm.md.infrastructure.repo.mat;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.tsrm.md.spi.model.mat.po.MatManageCatAttriPO;
import org.springframework.stereotype.Repository;
import io.terminus.tsrm.md.spi.model.mat.po.MatManageCateTotalPO;

import java.util.List;

/**
 * (MatManageCateTotal)表数据库访问层
 *
 * <AUTHOR>
 * @since  2025-04-22 17:20:51
 */
@Repository
public interface MatManageCateTotalRepo extends BaseRepository<MatManageCateTotalPO> {

    default void save(MatManageCateTotalPO savePO) {
        QueryWrapper<MatManageCateTotalPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mat_cate", savePO.getMatCate());
        MatManageCateTotalPO dbPO = this.selectOne(queryWrapper);
        if (dbPO == null) {
            this.insert(savePO);
        } else {
            savePO.setId(dbPO.getId());
            this.updateById(savePO);
        }
    }
}
