package io.terminus.tsrm.md.infrastructure.repo.pay;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.tsrm.md.spi.dict.pay.ExtWqGenPayTermItemCfPayTypeDict;
import org.springframework.stereotype.Repository;
import io.terminus.tsrm.md.spi.model.pay.po.ExtWqGenPayTermItemCfPO;

import java.util.List;

/**
 * (ExtWqGenPayTermItemCf)表数据库访问层
 *
 * <AUTHOR>
 * @since  2023-09-26 11:27:00
 */
@Repository
public interface ExtWqGenPayTermItemCfRepo extends BaseRepository<ExtWqGenPayTermItemCfPO> {

    default List<ExtWqGenPayTermItemCfPO> selectPayRatio(Long genPayTermHeadCfId) {
        QueryWrapper<ExtWqGenPayTermItemCfPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().and(q -> q.eq(ExtWqGenPayTermItemCfPO::getExtWqGenPayTermHeadCfId, genPayTermHeadCfId)).and(s -> s.eq(ExtWqGenPayTermItemCfPO::getPayType, ExtWqGenPayTermItemCfPayTypeDict.PREPAID));
        return this.selectList(wrapper);
    }

    default List<ExtWqGenPayTermItemCfPO> selectItemsByHeadId(Long genPayTermHeadCfId) {
        QueryWrapper<ExtWqGenPayTermItemCfPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().and(q -> q.eq(ExtWqGenPayTermItemCfPO::getExtWqGenPayTermHeadCfId, genPayTermHeadCfId));
        return this.selectList(wrapper);
    }
}
