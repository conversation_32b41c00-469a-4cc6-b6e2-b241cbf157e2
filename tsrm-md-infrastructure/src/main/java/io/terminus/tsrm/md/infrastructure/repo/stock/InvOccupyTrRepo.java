package io.terminus.tsrm.md.infrastructure.repo.stock;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.tsrm.md.spi.model.stock.po.InvOccupyTrPO;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 库存占用表(InvOccupyTr)表数据库访问层
 *
 * <AUTHOR>
 * @since  2023-11-01 14:49:55
 */
@Repository
public interface InvOccupyTrRepo extends BaseRepository<InvOccupyTrPO> {

    /**
     * 根据仓库和物料查询库存占用记录
     * @param invId
     * @param materialId
     * @return
     */
     default InvOccupyTrPO queryInvOccupyTrPOByInvAndMat(Long invId, Long materialId){
        QueryWrapper<InvOccupyTrPO> invOccupyTrPOQueryWrapper = new QueryWrapper<>();
        invOccupyTrPOQueryWrapper.lambda().and(condition->condition.eq(InvOccupyTrPO::getInvLoc,invId)).and(condition->condition.eq(InvOccupyTrPO::getMat,materialId));
        return this.selectOne(invOccupyTrPOQueryWrapper);
    }

    /**
     * 根据仓库和物料查询库存占用记录 没有则初始化一条
     * @param invId
     * @param materialId
     * @return
     */
     default InvOccupyTrPO queryInvOccupyTrPOByInvAndMatIfNullInsert(Long invId, Long materialId){
        InvOccupyTrPO invOccupyTrPO = queryInvOccupyTrPOByInvAndMat(invId, materialId);
        if (Objects.isNull(invOccupyTrPO)){
            InvOccupyTrPO insertPO = new InvOccupyTrPO();
            insertPO.setMat(materialId);
            insertPO.setInvLoc(invId);
            insertPO.setAllotOccupyQty(BigDecimal.ZERO);
            insertPO.setPickOccupyQty(BigDecimal.ZERO);
            insertPO.setTotalOccupyQty(BigDecimal.ZERO);
            insertPO.setReserveOccupyQty(BigDecimal.ZERO);
            insertPO.setPreOccupyQty(BigDecimal.ZERO);
            this.insert(insertPO);
            return insertPO;
        }
        return invOccupyTrPO;
    }




}
