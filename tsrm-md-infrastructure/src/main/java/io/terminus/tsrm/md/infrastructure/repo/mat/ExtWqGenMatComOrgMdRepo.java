package io.terminus.tsrm.md.infrastructure.repo.mat;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.tsrm.md.spi.model.mat.po.ExtWqGenMatComOrgMdPO;

/**
 * (ExtWqGenMatComOrgMd)表数据库访问层
 *
 * <AUTHOR>
 * @since  2023-09-26 13:48:51
 */
@Repository
public interface ExtWqGenMatComOrgMdRepo extends BaseRepository<ExtWqGenMatComOrgMdPO> {

    default ExtWqGenMatComOrgMdPO selectByMatAndComOrg(Long matId, Long comOrgId) {
        QueryWrapper<ExtWqGenMatComOrgMdPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().and(w -> w.eq(ExtWqGenMatComOrgMdPO::getMaterial, matId)).and(w -> w.eq(ExtWqGenMatComOrgMdPO::getComOrg, comOrgId));
        return this.selectOne(wrapper);
    }
}
