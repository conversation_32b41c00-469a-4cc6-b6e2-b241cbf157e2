package io.terminus.tsrm.md.infrastructure.repo.pay;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.tsrm.md.spi.model.pay.po.ExtWqGenPayTermHeadCfPO;

import java.util.List;

/**
 * (ExtWqGenPayTermHeadCf)表数据库访问层
 *
 * <AUTHOR>
 * @since  2023-09-26 11:11:27
 */
@Repository
public interface ExtWqGenPayTermHeadCfRepo extends BaseRepository<ExtWqGenPayTermHeadCfPO> {

    // 根据code查询
    default List<ExtWqGenPayTermHeadCfPO> selectByCode(String code) {
        QueryWrapper<ExtWqGenPayTermHeadCfPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().and(c -> c.eq(ExtWqGenPayTermHeadCfPO::getCode, code));
        return this.selectList(wrapper);
    }

    // 根据code查询id不等于自身的数据
    default List<ExtWqGenPayTermHeadCfPO> selectByCode(String code, Long id) {
        QueryWrapper<ExtWqGenPayTermHeadCfPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().and(c -> c.eq(ExtWqGenPayTermHeadCfPO::getCode, code)).and(i -> i.ne(ExtWqGenPayTermHeadCfPO::getId, id));
        return this.selectList(wrapper);
    }
}
