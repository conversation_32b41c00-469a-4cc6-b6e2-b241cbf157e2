package io.terminus.tsrm.md.infrastructure.repo.mat;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.tsrm.md.spi.model.mat.po.MatManageSimilarListPO;

import java.util.List;

/**
 * (MatManageSimilarList)表数据库访问层
 *
 * <AUTHOR>
 * @since  2025-04-28 16:52:33
 */
@Repository
public interface MatManageSimilarListRepo extends BaseRepository<MatManageSimilarListPO> {

    default void saveList(List<MatManageSimilarListPO> matManageSimilarListPOList, Long mainId) {
        // 删除当前物料类目所有属性然后重新插入
        QueryWrapper<MatManageSimilarListPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("srm_mat_management_tr_similar_mat_list_id", mainId);
        this.delete(queryWrapper);
        this.insertBatch(matManageSimilarListPOList);
    }

}
