package io.terminus.tsrm.md.infrastructure.repo.mat;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.tsrm.md.spi.model.mat.po.SrmMatManagementTrPO;

/**
 * (SrmMatManagementTr)表数据库访问层
 *
 * <AUTHOR>
 * @since  2025-04-23 17:56:23
 */
@Repository
public interface SrmMatManagementTrRepo extends BaseRepository<SrmMatManagementTrPO> {

    default void updateCateId(SrmMatManagementTrPO updatePO) {
        LambdaUpdateWrapper<SrmMatManagementTrPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SrmMatManagementTrPO::getId, updatePO.getId());
        updateWrapper.set(SrmMatManagementTrPO::getMatCate, updatePO.getMatCate());
        this.update(null, updateWrapper);
    }
}
