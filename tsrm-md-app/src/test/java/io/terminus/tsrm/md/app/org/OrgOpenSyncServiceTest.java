package io.terminus.tsrm.md.app.org;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import io.terminus.tsrm.md.infrastructure.repo.org.TsrmOrgStructMdRepo;
import io.terminus.tsrm.md.spi.model.sync.dto.OrgSyncDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * OrgOpenSyncService 测试类
 *
 * <AUTHOR>
 * @time 2025/8/26
 */
@ExtendWith(MockitoExtension.class)
class OrgOpenSyncServiceTest {

    @Mock
    private TsrmOrgStructMdRepo tsrmOrgStructMdRepo;

    @InjectMocks
    private OrgOpenSyncService orgOpenSyncService;

    private OrgSyncDTO orgSyncDTO;
    private OrgSyncDTO.ExtendFieldsDTO extendFieldsDTO;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        orgSyncDTO = new OrgSyncDTO();
        orgSyncDTO.setOrganization("测试组织");
        orgSyncDTO.setOrganizationUuid("ORG-001");
        orgSyncDTO.setRootNode(true);
        orgSyncDTO.setParentUuid(null);

        extendFieldsDTO = new OrgSyncDTO.ExtendFieldsDTO();
        extendFieldsDTO.setCompanycode("COMP-001");
        extendFieldsDTO.setCompanyname("测试公司");
        extendFieldsDTO.setOrgfullpath("/测试公司/测试组织");

        orgSyncDTO.setExtendFields(Collections.singletonList(extendFieldsDTO));
    }

    @Test
    void testAddOrg_CreateNewRootOrg_Success() {
        // Given
        when(tsrmOrgStructMdRepo.selectOne(any(QueryWrapper.class))).thenReturn(null);
        when(tsrmOrgStructMdRepo.insert(any(OrgStructMdPO.class))).thenReturn(1);

        // When
        orgOpenSyncService.addOrg(orgSyncDTO);

        // Then
        verify(tsrmOrgStructMdRepo, times(1)).selectOne(any(QueryWrapper.class));
        verify(tsrmOrgStructMdRepo, times(1)).insert(any(OrgStructMdPO.class));
        verify(tsrmOrgStructMdRepo, never()).updateById(any(OrgStructMdPO.class));
    }

    @Test
    void testAddOrg_CreateNewChildOrg_Success() {
        // Given
        orgSyncDTO.setRootNode(false);
        orgSyncDTO.setParentUuid("PARENT-001");

        // Mock parent org exists
        OrgStructMdPO parentOrg = new OrgStructMdPO();
        parentOrg.setId(1L);
        parentOrg.setOrgCode("PARENT-001");
        parentOrg.setOrgName("父级组织");

        when(tsrmOrgStructMdRepo.selectOne(any(QueryWrapper.class)))
                .thenReturn(null)  // 第一次查询：当前组织不存在
                .thenReturn(parentOrg);  // 第二次查询：父级组织存在

        when(tsrmOrgStructMdRepo.insert(any(OrgStructMdPO.class))).thenReturn(1);

        // When
        orgOpenSyncService.addOrg(orgSyncDTO);

        // Then
        verify(tsrmOrgStructMdRepo, times(2)).selectOne(any(QueryWrapper.class));
        verify(tsrmOrgStructMdRepo, times(1)).insert(any(OrgStructMdPO.class));
    }

    @Test
    void testAddOrg_UpdateExistingOrg_Success() {
        // Given
        OrgStructMdPO existingOrg = new OrgStructMdPO();
        existingOrg.setId(1L);
        existingOrg.setOrgCode("ORG-001");
        existingOrg.setOrgName("旧组织名称");
        existingOrg.setCreatedAt(LocalDateTime.now().minusDays(1));

        when(tsrmOrgStructMdRepo.selectOne(any(QueryWrapper.class))).thenReturn(existingOrg);
        when(tsrmOrgStructMdRepo.updateById(any(OrgStructMdPO.class))).thenReturn(1);

        // When
        orgOpenSyncService.addOrg(orgSyncDTO);

        // Then
        verify(tsrmOrgStructMdRepo, times(1)).selectOne(any(QueryWrapper.class));
        verify(tsrmOrgStructMdRepo, times(1)).updateById(any(OrgStructMdPO.class));
        verify(tsrmOrgStructMdRepo, never()).insert(any(OrgStructMdPO.class));
    }

    @Test
    void testAddOrg_NullInput_ThrowsException() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            orgOpenSyncService.addOrg(null);
        });
        assertEquals("组织同步数据不能为空", exception.getMessage());
    }

    @Test
    void testAddOrg_EmptyOrganizationName_ThrowsException() {
        // Given
        orgSyncDTO.setOrganization("");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            orgOpenSyncService.addOrg(orgSyncDTO);
        });
        assertEquals("组织名称不能为空", exception.getMessage());
    }

    @Test
    void testAddOrg_EmptyOrganizationUuid_ThrowsException() {
        // Given
        orgSyncDTO.setOrganizationUuid("");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            orgOpenSyncService.addOrg(orgSyncDTO);
        });
        assertEquals("组织UUID不能为空", exception.getMessage());
    }

    @Test
    void testAddOrg_NonRootOrgWithoutParentUuid_ThrowsException() {
        // Given
        orgSyncDTO.setRootNode(false);
        orgSyncDTO.setParentUuid("");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            orgOpenSyncService.addOrg(orgSyncDTO);
        });
        assertEquals("非根节点的父级UUID不能为空", exception.getMessage());
    }

    @Test
    void testAddOrg_ParentOrgNotExists_ThrowsException() {
        // Given
        orgSyncDTO.setRootNode(false);
        orgSyncDTO.setParentUuid("NON-EXIST-PARENT");

        when(tsrmOrgStructMdRepo.selectOne(any(QueryWrapper.class)))
                .thenReturn(null)  // 第一次查询：当前组织不存在
                .thenReturn(null); // 第二次查询：父级组织也不存在

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            orgOpenSyncService.addOrg(orgSyncDTO);
        });
        assertEquals("父级组织不存在，UUID: NON-EXIST-PARENT", exception.getMessage());
    }

    @Test
    void testAddOrg_WithExtendFields_Success() {
        // Given
        when(tsrmOrgStructMdRepo.selectOne(any(QueryWrapper.class))).thenReturn(null);
        when(tsrmOrgStructMdRepo.insert(any(OrgStructMdPO.class))).thenReturn(1);

        // When
        orgOpenSyncService.addOrg(orgSyncDTO);

        // Then
        verify(tsrmOrgStructMdRepo, times(1)).insert(any(OrgStructMdPO.class));
    }

    @Test
    void testAddOrg_WithMultipleExtendFields_Success() {
        // Given
        OrgSyncDTO.ExtendFieldsDTO extendField2 = new OrgSyncDTO.ExtendFieldsDTO();
        extendField2.setCompanycode("COMP-002");
        extendField2.setCompanyname("测试公司2");

        orgSyncDTO.setExtendFields(Arrays.asList(extendFieldsDTO, extendField2));

        when(tsrmOrgStructMdRepo.selectOne(any(QueryWrapper.class))).thenReturn(null);
        when(tsrmOrgStructMdRepo.insert(any(OrgStructMdPO.class))).thenReturn(1);

        // When
        orgOpenSyncService.addOrg(orgSyncDTO);

        // Then
        verify(tsrmOrgStructMdRepo, times(1)).insert(any(OrgStructMdPO.class));
    }
}
