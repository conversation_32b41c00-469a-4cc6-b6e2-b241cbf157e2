package io.terminus.tsrm.md.app.expert;

import com.google.common.base.Throwables;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.erp.md.spi.model.po.org.OrgEmployeeMdPO;
import io.terminus.iam.api.request.role.RoleByKeyFindParams;
import io.terminus.iam.api.request.role.RoleRelationDeleteParams;
import io.terminus.iam.api.request.role.RoleRelationFindParams;
import io.terminus.iam.api.request.role.UserRoleRelationCreateParams;
import io.terminus.iam.api.request.user.CompleteUserParams;
import io.terminus.iam.api.request.user.UserPagingParams;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.role.RoleRelation;
import io.terminus.iam.api.response.user.User;
import io.terminus.iam.sdk.client.role.RoleRelationClient;
import io.terminus.iam.sdk.client.user.UserManagementClient;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.iam.IamClientFactory;
import io.terminus.tsrm.md.app.user.TsrmUserService;
import io.terminus.tsrm.md.infrastructure.repo.expert.SrmExpertBusiScopeMdRepo;
import io.terminus.tsrm.md.infrastructure.repo.expert.SrmExpertMatCateMdRepo;
import io.terminus.tsrm.md.infrastructure.repo.expert.SrmExpertMdRepo;
import io.terminus.tsrm.md.spi.convert.expert.SrmExpertBusiScopeMdConverter;
import io.terminus.tsrm.md.spi.convert.expert.SrmExpertMatCateMdConverter;
import io.terminus.tsrm.md.spi.convert.expert.SrmExpertMdConverter;
import io.terminus.tsrm.md.spi.dict.expert.SrmExpertMdExpertTypeDict;
import io.terminus.tsrm.md.spi.dict.expert.SrmExpertMdStatusDict;
import io.terminus.tsrm.md.spi.dict.org.OrgEmployeeMdStatusDict;
import io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertBusiScopeMdDTO;
import io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertMatCateMdDTO;
import io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertMdDTO;
import io.terminus.tsrm.md.spi.model.expert.po.SrmExpertBusiScopeMdPO;
import io.terminus.tsrm.md.spi.model.expert.po.SrmExpertMatCateMdPO;
import io.terminus.tsrm.md.spi.model.expert.po.SrmExpertMdPO;
import io.terminus.tsrm.md.spi.model.org.dto.OrgEmployeeMdDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * TODO: demo 测试
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TsrmExpertService {
    private final TsrmUserService tsrmUserService;

    private final SrmExpertMdConverter srmExpertMdConverter;

    private final SrmExpertMdRepo srmExpertMdRepo;

    private final IdGenerator idGenerator;

    private final SrmExpertBusiScopeMdConverter srmExpertBusiScopeMdConverter;

    private final SrmExpertBusiScopeMdRepo srmExpertBusiScopeMdRepo;

    private final SrmExpertMatCateMdConverter srmExpertMatCateMdConverter;

    private final SrmExpertMatCateMdRepo srmExpertMatCateMdRepo;

    @Transactional
    public SrmExpertMdDTO createExpert(SrmExpertMdDTO srmExpertMdDTO){
        if (null == srmExpertMdDTO.getEmployeeId()) {
            // 判断手机号是否已注册用户
            UserPagingParams queryParams = new UserPagingParams();
            queryParams.setMobile(srmExpertMdDTO.getContactPhone());
            List<User> users = tsrmUserService.queryUser(queryParams);
            if (null != users && users.size() > 0) {
                throw new BusinessException("MD.expert.phone.registered");
            }
            CompleteUserParams params = new CompleteUserParams();
            params.setPassword("Aa123456");
            params.setEmail(srmExpertMdDTO.getContactEmail());
            params.setUsername(srmExpertMdDTO.getContactPhone());
            params.setMobile(srmExpertMdDTO.getContactPhone());
            params.setNickname(srmExpertMdDTO.getExpertName());
            params.setRealname(srmExpertMdDTO.getExpertName());
            params.setStatus(true);
            User user = tsrmUserService.createUser(params);
            srmExpertMdDTO.setUser(user.getId());
            // 关联默认的外部专家角色, 暂时写死userId，发到不同环境时需要手动替换
            UserRoleRelationCreateParams createParams = new UserRoleRelationCreateParams();
            createParams.setUserId(user.getId());
            //根据key查询角色
            RoleByKeyFindParams findParams = new RoleByKeyFindParams();
            findParams.setKey("EXPERT");
            Role role = tsrmUserService.listRole(findParams);
            createParams.setRoleIds(new ArrayList<>(Arrays.asList(role.getId())));
            tsrmUserService.createRoleRelation(createParams);
        } else {
            // 校验员工是否已经被关联过了
            List<SrmExpertMdPO> expertList = srmExpertMdRepo.selectByEmployeeId(srmExpertMdDTO.getEmployeeId().getId());
            if (null != expertList && expertList.size() > 0) {
                throw new BusinessException("MD.expert.employee.already.linked");
            }
        }
        Long expertId = idGenerator.nextId(SrmExpertMdPO.class);
        srmExpertMdDTO.setId(expertId);
        srmExpertMdDTO.setStatus(SrmExpertMdStatusDict.ENABLED);
        srmExpertMdRepo.insert(srmExpertMdConverter.dto2Po(srmExpertMdDTO));
        if (null != srmExpertMdDTO.getBusiScope()) {
            for (SrmExpertBusiScopeMdDTO line : srmExpertMdDTO.getBusiScope()) {
                SrmExpertBusiScopeMdPO srmExpertBusiScopeMdPO = srmExpertBusiScopeMdConverter.dto2Po(line);
                srmExpertBusiScopeMdPO.setExpertId(expertId);
                srmExpertBusiScopeMdRepo.insert(srmExpertBusiScopeMdPO);
            }
        }
        if (null != srmExpertMdDTO.getMatCate()) {
            for (SrmExpertMatCateMdDTO line : srmExpertMdDTO.getMatCate()) {
                SrmExpertMatCateMdPO srmExpertMatCateMdPO = srmExpertMatCateMdConverter.dto2Po(line);
                srmExpertMatCateMdPO.setExpertId(expertId);
                srmExpertMatCateMdRepo.insert(srmExpertMatCateMdPO);
            }
        }

        return srmExpertMdDTO;
    }

    public void outerExpertUpdateWithUser(SrmExpertMdDTO srmExpertMdDTO){
        if (null == srmExpertMdDTO.getEmployeeId()) {
            CompleteUserParams params = new CompleteUserParams();
            params.setUsername(srmExpertMdDTO.getContactPhone());
            params.setEmail(srmExpertMdDTO.getContactEmail());
            params.setMobile(srmExpertMdDTO.getContactPhone());
            params.setNickname(srmExpertMdDTO.getExpertName());
            params.setRealname(srmExpertMdDTO.getExpertName());
            try {
                tsrmUserService.updateUser(srmExpertMdDTO.getUser(),params);
            } catch (BusinessException e) {
                if (e.getMessage().equals("用户名已被他人占用")) {
                    throw new BusinessException("MD.expert.phone.already.used");
                }
            }
        }
    }

    public void disableExpert(SrmExpertMdDTO request) {
        SrmExpertMdPO srmExpertMdPO = srmExpertMdRepo.selectById(request.getId());
        srmExpertMdPO.setStatus(SrmExpertMdStatusDict.DISABLED);
        srmExpertMdRepo.updateById(srmExpertMdPO);
        if (SrmExpertMdExpertTypeDict.OUTER.equals(srmExpertMdPO.getExpertType())) {
            User user = tsrmUserService.queryById(srmExpertMdPO.getUser());
            if (null != user) {
                tsrmUserService.disableUser(user.getId());
            }
        }
    }

    public void enableExpert(SrmExpertMdDTO request) {
        SrmExpertMdPO srmExpertMdPO = srmExpertMdRepo.selectById(request.getId());
        srmExpertMdPO.setStatus(SrmExpertMdStatusDict.ENABLED);
        srmExpertMdRepo.updateById(srmExpertMdPO);
        if (SrmExpertMdExpertTypeDict.OUTER.equals(srmExpertMdPO.getExpertType())) {
            User user = tsrmUserService.queryById(srmExpertMdPO.getUser());
            if (null != user) {
                tsrmUserService.enableUser(user.getId());
            }
        }
    }

    public void deleteExpert(SrmExpertMdDTO request) {
        SrmExpertMdPO srmExpertMdPO = srmExpertMdRepo.selectById(request.getId());
        srmExpertMdRepo.deleteById(srmExpertMdPO);
        if (SrmExpertMdExpertTypeDict.OUTER.equals(srmExpertMdPO.getExpertType())) {
            User user = tsrmUserService.queryById(srmExpertMdPO.getUser());
            if (null != user) {
                tsrmUserService.deleteUser(user.getId());
            }
        }
    }

}
