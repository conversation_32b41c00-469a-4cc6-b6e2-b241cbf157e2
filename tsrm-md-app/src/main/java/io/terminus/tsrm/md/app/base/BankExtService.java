package io.terminus.tsrm.md.app.base;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.md.infrastructure.repo.base.GenBankCfRepo;
import io.terminus.erp.md.infrastructure.repo.base.GenSubBankCfRepo;
import io.terminus.erp.md.spi.model.po.base.GenBankCfPO;
import io.terminus.erp.md.spi.model.po.base.GenSubBankCfPO;
import io.terminus.tsrm.md.spi.model.base.dto.BankSyncDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class BankExtService {

    private final GenSubBankCfRepo genSubBankCfRepo;

    private final GenBankCfRepo genBankCfRepo;


    @Transactional
    public Boolean bpmBankSync(BankSyncDTO bankSyncDTO) {
        //校验
        if (StringUtils.isEmpty(bankSyncDTO.getBankCode())) {
            throw new BusinessException("MD.bank.code.empty");
        }
        //查询对应的所属银行
        GenBankCfPO genBankCfPO = null;
        // bankType == bankName
        if (StringUtils.isNotEmpty(bankSyncDTO.getBankType())) {
            LambdaQueryWrapper<GenBankCfPO> queryGenBankCfPOByBankCode = new LambdaQueryWrapper<GenBankCfPO>()
                    .eq(GenBankCfPO::getBankName, bankSyncDTO.getBankType());
            genBankCfPO = genBankCfRepo.selectOne(queryGenBankCfPOByBankCode);
        }

        //通过code 查询目前是否有重复的支行
        LambdaQueryWrapper<GenSubBankCfPO> queryGenSubBankCfPOByBankCode = new LambdaQueryWrapper<GenSubBankCfPO>()
                .eq(GenSubBankCfPO::getSubBankCode, bankSyncDTO.getBankCode());
        GenSubBankCfPO genSubBankCfPO = genSubBankCfRepo.selectOne(queryGenSubBankCfPOByBankCode);
        if (genSubBankCfPO == null) {
            genSubBankCfPO = new GenSubBankCfPO();
            genSubBankCfPO.setSubBankCode(bankSyncDTO.getBankCode());
            genSubBankCfPO.setSubBankName(bankSyncDTO.getBankName());
            if (genBankCfPO != null) {
                genSubBankCfPO.setGenBankCfId(genBankCfPO.getId());
            }
            genSubBankCfRepo.insert(genSubBankCfPO);
        } else {
            genSubBankCfPO.setSubBankName(bankSyncDTO.getBankName());
            if (genBankCfPO != null) {
                genSubBankCfPO.setGenBankCfId(genBankCfPO.getId());
            }
            genSubBankCfRepo.updateById(genSubBankCfPO);
        }
        return Boolean.TRUE;
    }
}
