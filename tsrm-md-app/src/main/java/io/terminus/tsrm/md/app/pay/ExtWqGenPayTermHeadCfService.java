package io.terminus.tsrm.md.app.pay;

import io.terminus.common.api.exception.BusinessException;
import io.terminus.tsrm.md.infrastructure.repo.pay.ExtWqGenPayTermHeadCfRepo;
import io.terminus.tsrm.md.infrastructure.repo.pay.ExtWqGenPayTermItemCfRepo;
import io.terminus.tsrm.md.spi.convert.pay.ExtWqGenPayTermHeadCfConverter;
import io.terminus.tsrm.md.spi.convert.pay.ExtWqGenPayTermItemCfConverter;
import io.terminus.tsrm.md.spi.dict.pay.ExtWqGenPayTermItemCfPayTypeDict;
import io.terminus.tsrm.md.spi.model.pay.dto.ExtWqGenPayTermHeadCfDTO;
import io.terminus.tsrm.md.spi.model.pay.dto.ExtWqGenPayTermItemCfDTO;
import io.terminus.tsrm.md.spi.model.pay.po.ExtWqGenPayTermHeadCfPO;
import io.terminus.tsrm.md.spi.model.pay.po.ExtWqGenPayTermItemCfPO;
import io.terminus.tsrm.md.spi.msg.pay.GenPayTermHeadMsg;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 付款协议服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class ExtWqGenPayTermHeadCfService {

    private final ExtWqGenPayTermHeadCfConverter genPayTermHeadCfConverter;

    private final ExtWqGenPayTermHeadCfRepo genPayTermHeadCfRepo;

    private final ExtWqGenPayTermItemCfRepo genPayTermItemCfRepo;

    private final ExtWqGenPayTermItemCfConverter genPayTermItemCfConverter;

    @Transactional
    public void savePayTermHead (ExtWqGenPayTermHeadCfDTO payTermHeadCfDTO) {
        // 参数校验
        // 编码不能重复
        List<ExtWqGenPayTermHeadCfPO> extWqGenPayTermHeadCfPOList = new ArrayList<>();
        if (null == payTermHeadCfDTO.getId()) {
            extWqGenPayTermHeadCfPOList = genPayTermHeadCfRepo.selectByCode(payTermHeadCfDTO.getCode());
        } else {
            extWqGenPayTermHeadCfPOList = genPayTermHeadCfRepo.selectByCode(payTermHeadCfDTO.getCode(), payTermHeadCfDTO.getId());
        }
        if (CollectionUtils.isNotEmpty(extWqGenPayTermHeadCfPOList)) {
            throw new BusinessException(GenPayTermHeadMsg.CODE_MUST_UNIQUE);
        }
        List<ExtWqGenPayTermItemCfDTO> itemList = payTermHeadCfDTO.getPayTermItems();
        int prepaidNum = 0;
        int retentionMoneyNum = 0;
        List<Long> stageList = new ArrayList<>();
        for (ExtWqGenPayTermItemCfDTO payTermItemCfDTO: itemList) {
            // 分期值不能重复
            if (CollectionUtils.isEmpty(stageList)) {
                stageList.add(payTermItemCfDTO.getStage());
            } else {
                if (stageList.contains(payTermItemCfDTO.getStage())) {
                    throw new BusinessException(GenPayTermHeadMsg.STAGE_MUST_UNIQUE);
                }
                stageList.add(payTermItemCfDTO.getStage());
            }
            // 预付款、质保金类型的款项，只允许各存在一项
            if (ExtWqGenPayTermItemCfPayTypeDict.PREPAID.equals(payTermItemCfDTO.getPayType())) {
                if (prepaidNum == 1) {
                    throw new BusinessException(GenPayTermHeadMsg.PREPAID_CANNOT_GREATER_THAN_ONE);
                }
                prepaidNum ++;
            }
            if (ExtWqGenPayTermItemCfPayTypeDict.RETENTION_MONEY.equals(payTermItemCfDTO.getPayType())) {
                if (retentionMoneyNum == 1) {
                    throw new BusinessException(GenPayTermHeadMsg.RETENTION_MONEY_CANNOT_GREATER_THAN_ONE);
                }
                retentionMoneyNum ++;
            }
        }
        // 保存数据
        ExtWqGenPayTermHeadCfPO genPayTermHeadCfPO = genPayTermHeadCfConverter.dto2Po(payTermHeadCfDTO);
        List<ExtWqGenPayTermItemCfPO> extWqGenPayTermItemTrPOS = new ArrayList<>();
        if (null == genPayTermHeadCfPO.getId()) {
            genPayTermHeadCfRepo.insert(genPayTermHeadCfPO);
            for (ExtWqGenPayTermItemCfDTO payTermItemCfDTO: itemList) {
                ExtWqGenPayTermItemCfPO genPayTermItemCfPO = genPayTermItemCfConverter.dto2Po(payTermItemCfDTO);
                genPayTermItemCfPO.setExtWqGenPayTermHeadCfId(genPayTermHeadCfPO.getId());
                extWqGenPayTermItemTrPOS.add(genPayTermItemCfPO);
            }
            genPayTermItemCfRepo.insertBatch(extWqGenPayTermItemTrPOS);
        } else {
            genPayTermHeadCfRepo.updateById(genPayTermHeadCfPO);
            // 删除原有items
            List<ExtWqGenPayTermItemCfPO> extWqGenPayTermItems = genPayTermItemCfRepo.selectItemsByHeadId(genPayTermHeadCfPO.getId());
            List<Long> itemIds = extWqGenPayTermItems.stream().map(ExtWqGenPayTermItemCfPO::getId).distinct().collect(Collectors.toList());
            genPayTermItemCfRepo.deleteBatchIds(itemIds);
            // 保存传入的items
            itemList.forEach(line -> {
                line.setExtWqGenPayTermHeadCfId(genPayTermHeadCfPO.getId());
                line.setId(null);
                extWqGenPayTermItemTrPOS.add(genPayTermItemCfConverter.dto2Po(line));
            });
            genPayTermItemCfRepo.insertBatch(extWqGenPayTermItemTrPOS);
        }

    }

}
