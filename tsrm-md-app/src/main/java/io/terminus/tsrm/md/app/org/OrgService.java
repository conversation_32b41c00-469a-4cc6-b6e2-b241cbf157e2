package io.terminus.tsrm.md.app.org;

import io.terminus.common.api.model.Paging;
import io.terminus.erp.md.spi.util.MD;
import io.terminus.trantor.org.spi.model.dto.OrgStructMdDetailDTO;
import io.terminus.trantor.org.spi.model.po.OrgBusinessTypePO;
import io.terminus.trantor.org.spi.model.po.OrgRelationCfPO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO: demo 测试
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrgService {

    public Paging<OrgStructMdDetailDTO> pagingCompanyByDepartment(Long departmentId, int page, int pageSize) {
        if (null == departmentId) {
            return new Paging<>(0, Collections.emptyList());
        }
        OrgRelationCfPO orgRelationCfPO = new OrgRelationCfPO();
        orgRelationCfPO.setOrgHeadUnitId(departmentId);
        OrgBusinessTypePO orgBusinessTypePO = new OrgBusinessTypePO();
        orgBusinessTypePO.setCode("COM_ORG");
        List<OrgBusinessTypePO> businessTypePOS = MD.queryList(orgBusinessTypePO, OrgBusinessTypePO.class);
        if (!CollectionUtils.isEmpty(businessTypePOS)) {
            orgRelationCfPO.setOrgRelationUnitTypeId(businessTypePOS.get(0).getId());
            List<OrgRelationCfPO> orgRelationCfPOS = MD.queryList(orgRelationCfPO, OrgRelationCfPO.class);
            if (!CollectionUtils.isEmpty(orgRelationCfPOS)) {
                // 分页逻辑
                int total = orgRelationCfPOS.size();
                int start = (page - 1) * pageSize;
                int end = Math.min(start + pageSize, total);
                List<OrgRelationCfPO> pagedOrgRelationCfPOS = orgRelationCfPOS.subList(start, end);

                // 将 OrgRelationCfPO 转换为 OrgStructMdSaveDTO
                List<OrgStructMdDetailDTO> pagedOrgStructMdSaveDTOs = pagedOrgRelationCfPOS.stream()
                        .map(this::convertToOrgStructMdSaveDTO)
                        .collect(Collectors.toList());

                return new Paging<>(total, pagedOrgStructMdSaveDTOs);
            } else {
                OrgStructMdPO orgStructMdPO = MD.queryById(departmentId, OrgStructMdPO.class);
                Long parentId = orgStructMdPO.getOrgParentId();
                if (parentId != null) {
                    // 递归调用
                    return pagingCompanyByDepartment(parentId, page, pageSize);
                }
            }
        }
        return new Paging<>(0, Collections.emptyList());
    }

    private OrgStructMdDetailDTO convertToOrgStructMdSaveDTO(OrgRelationCfPO orgRelationCfPO) {
        // 实现转换逻辑
        OrgStructMdDetailDTO dto = new OrgStructMdDetailDTO();
        Long companyId = orgRelationCfPO.getOrgRelationUnitId();
        OrgStructMdPO orgStructMdPO = MD.queryById(companyId, OrgStructMdPO.class);
        // 设置 dto 的属性
        dto.setId(orgStructMdPO.getId());
        dto.setOrgCode(orgStructMdPO.getOrgCode());
        dto.setOrgName(orgStructMdPO.getOrgName());
        return dto;
    }


}
