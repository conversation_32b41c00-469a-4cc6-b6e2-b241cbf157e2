package io.terminus.tsrm.md.app.user;

import com.google.common.base.Throwables;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.iam.api.request.role.*;
import io.terminus.iam.api.request.user.CompleteUserParams;
import io.terminus.iam.api.request.user.UserPagingParams;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.role.RoleRelation;
import io.terminus.iam.api.response.user.User;
import io.terminus.iam.sdk.client.role.RoleClient;
import io.terminus.iam.sdk.client.role.RoleRelationClient;
import io.terminus.iam.sdk.client.user.UserManagementClient;
import io.terminus.trantor2.common.iam.IamClientFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TODO: demo 测试
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TsrmUserService {
    private final IamClientFactory iamClientFactory;


    public User queryById(Long userId){
        try {
            UserManagementClient client = iamClientFactory.getIamClient().users();
            return client.findById(userId).execute();
        } catch (Exception e){
            log.error("invoke user error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public List<RoleRelation> queryRoleRelationByUserId(Long userId){
        try {
            RoleRelationClient roleRelationClient = iamClientFactory.getIamClient().rolerelationClient();
            return roleRelationClient.findRoleRelationByUserId(userId).execute();
        } catch (Exception e){
            log.error("invoke query role relation error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public User createUser(CompleteUserParams params){
        try {
            UserManagementClient client = iamClientFactory.getIamClient().users();
            return client.create(params).execute();
        } catch (Exception e){
            log.error("invoke create user error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public List<User> queryUser(UserPagingParams params){
        try {
            UserManagementClient client = iamClientFactory.getIamClient().users();
            return client.findAll(params).execute();
        } catch (Exception e){
            log.error("invoke query user error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public void updateUser(Long userId,CompleteUserParams params){
        try {
            UserManagementClient client = iamClientFactory.getIamClient().users();
            client.updateById(userId,params).execute();
        } catch (Exception e){
            log.error("invoke update user error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public void enableUser(Long userId){
        try {
            UserManagementClient client = iamClientFactory.getIamClient().users();
            client.enabledById(userId).execute();
        } catch (Exception e){
            log.error("invoke enable user error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public void disableUser(Long userId){
        try {
            UserManagementClient client = iamClientFactory.getIamClient().users();
            client.disabledById(userId).execute();
        } catch (Exception e){
            log.error("invoke disable user error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public void resetPassword(CompleteUserParams params){
        try {
            UserManagementClient client = iamClientFactory.getIamClient().users();
            client.resetPasswordById(params.getId(),params).execute();
        } catch (Exception e){
            log.error("invoke reset user password error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    //注销用户
    public void deleteUser(Long userId){
        try {
            UserManagementClient client = iamClientFactory.getIamClient().users();
            client.destroyById(userId).execute();
        } catch (Exception e){
            log.error("invoke reset user password error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public void createRoleRelation(UserRoleRelationCreateParams params){
        try {
            RoleRelationClient roleRelationClient = iamClientFactory.getIamClient().rolerelationClient();
            roleRelationClient.createUserRoleRelation(params).execute();
        } catch (Exception e){
            log.error("invoke create role relation error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public void deleteRoleRelation(RoleRelationDeleteParams params){
        try {
            RoleRelationClient roleRelationClient = iamClientFactory.getIamClient().rolerelationClient();
            roleRelationClient.deleteRoleRelation(params).execute();
        } catch (Exception e){
            log.error("invoke delete role relation error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public List<RoleRelation> listRoleRelation(RoleRelationFindParams params){
        try {
            RoleRelationClient roleRelationClient = iamClientFactory.getIamClient().rolerelationClient();
            return roleRelationClient.listRole(params).execute();
        } catch (Exception e){
            log.error("invoke delete role relation error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

    public Role listRole(RoleByKeyFindParams params){
        try {
            RoleClient roleClient = iamClientFactory.getIamClient().roleClient();
            return roleClient.getRoleByKey(params).execute();
        } catch (Exception e){
            log.error("invoke delete role relation error!{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException(e.getMessage());
        }
    }

}
