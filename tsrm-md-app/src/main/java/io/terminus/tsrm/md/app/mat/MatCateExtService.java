package io.terminus.tsrm.md.app.mat;

import io.terminus.erp.md.infrastructure.repo.base.GenMatCateMdRepo;
import io.terminus.erp.md.spi.model.po.base.GenMatCateMdPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 物料分类
 *
 * <AUTHOR>
 * @date 2023/9/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MatCateExtService {

    private final GenMatCateMdRepo genMatCateMdRepo;


    /**
     * 查询物料分类全部上级类目
     * */
    public List<GenMatCateMdPO> queryMatCateParent(GenMatCateMdPO matCateDTO) {
        List<GenMatCateMdPO> totalMatCates = new ArrayList<>();
        GenMatCateMdPO childMatCate = genMatCateMdRepo.selectById(matCateDTO.getId());
        totalMatCates.add(childMatCate);

        recursionTotalCates(childMatCate,totalMatCates);
        return totalMatCates;
    }

    private void recursionTotalCates(GenMatCateMdPO childMatCate, List<GenMatCateMdPO> totalMatCates) {
        if (childMatCate.getMatCateParent() == null) {
            return;
        }
        GenMatCateMdPO parent = genMatCateMdRepo.selectById(childMatCate.getMatCateParent());
        totalMatCates.add(parent);
        recursionTotalCates(parent,totalMatCates);
    }


}
