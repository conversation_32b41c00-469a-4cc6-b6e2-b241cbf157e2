package io.terminus.tsrm.md.app.org;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.md.infrastructure.repo.org.OrgComOrgCfRepo;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import io.terminus.tsrm.md.infrastructure.repo.org.TsrmOrgStructMdRepo;
import io.terminus.tsrm.md.spi.model.sync.dto.OrgSyncDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2025/8/26 11:13
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrgOpenSyncService {

    private final TsrmOrgStructMdRepo tsrmOrgStructMdRepo;

    /**
     * 添加组织机构
     * 将OrgSyncDTO转换成OrgStructMdPO并落库
     *
     * @param orgSyncDTO 组织同步DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrg(OrgSyncDTO orgSyncDTO) {
        log.info("开始处理组织同步数据: {}", orgSyncDTO);

        // 参数校验
        validateOrgSyncDTO(orgSyncDTO);

        // 检查组织是否已存在
        OrgStructMdPO existingOrg = findOrgByUuid(orgSyncDTO.getOrganizationUuid());

        if (existingOrg != null) {
            // 更新现有组织
            updateExistingOrg(existingOrg, orgSyncDTO);
            log.info("更新组织成功, ID: {}, UUID: {}", existingOrg.getId(), orgSyncDTO.getOrganizationUuid());
        } else {
            // 创建新组织
            OrgStructMdPO newOrg = createNewOrg(orgSyncDTO);
            tsrmOrgStructMdRepo.insert(newOrg);
            log.info("创建组织成功, ID: {}, UUID: {}", newOrg.getId(), orgSyncDTO.getOrganizationUuid());
        }
    }

    /**
     * 参数校验
     */
    private void validateOrgSyncDTO(OrgSyncDTO orgSyncDTO) {
        if (orgSyncDTO == null) {
            throw new BusinessException("组织同步数据不能为空");
        }

        if (StringUtils.isBlank(orgSyncDTO.getOrganization())) {
            throw new BusinessException("组织名称不能为空");
        }

        if (StringUtils.isBlank(orgSyncDTO.getOrganizationUuid())) {
            throw new BusinessException("组织UUID不能为空");
        }

        // 如果不是根节点，父级UUID不能为空
        if (!Boolean.TRUE.equals(orgSyncDTO.getRootNode()) && StringUtils.isBlank(orgSyncDTO.getParentUuid())) {
            throw new BusinessException("非根节点的父级UUID不能为空");
        }
    }

    /**
     * 根据UUID查找组织
     */
    private OrgStructMdPO findOrgByUuid(String organizationUuid) {
        QueryWrapper<OrgStructMdPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("org_code", organizationUuid);
        return tsrmOrgStructMdRepo.selectOne(queryWrapper);
    }

    /**
     * 更新现有组织
     */
    private void updateExistingOrg(OrgStructMdPO existingOrg, OrgSyncDTO orgSyncDTO) {
        // 更新组织名称
        existingOrg.setOrgName(orgSyncDTO.getOrganization());

        // 更新父级组织ID
        if (!Boolean.TRUE.equals(orgSyncDTO.getRootNode()) && StringUtils.isNotBlank(orgSyncDTO.getParentUuid())) {
            Long parentId = findParentOrgId(orgSyncDTO.getParentUuid());
            existingOrg.setOrgParentId(parentId);
        }

        // 更新扩展字段信息
        updateExtendFields(existingOrg, orgSyncDTO);

        // 更新修改时间
        existingOrg.setUpdatedAt(LocalDateTime.now());

        tsrmOrgStructMdRepo.updateById(existingOrg);
    }

    /**
     * 创建新组织
     */
    private OrgStructMdPO createNewOrg(OrgSyncDTO orgSyncDTO) {
        OrgStructMdPO orgStructMdPO = new OrgStructMdPO();

        // 设置基本信息
        orgStructMdPO.setOrgCode(orgSyncDTO.getOrganizationUuid());
        orgStructMdPO.setOrgName(orgSyncDTO.getOrganization());
        orgStructMdPO.setOrgStatus("ENABLED");
        orgStructMdPO.setOrgEnableDate(LocalDateTime.now());

        // 设置父级组织ID
        if (!Boolean.TRUE.equals(orgSyncDTO.getRootNode()) && StringUtils.isNotBlank(orgSyncDTO.getParentUuid())) {
            Long parentId = findParentOrgId(orgSyncDTO.getParentUuid());
            orgStructMdPO.setOrgParentId(parentId);
        }

        // 设置扩展字段信息
        updateExtendFields(orgStructMdPO, orgSyncDTO);

        // 设置创建时间
        orgStructMdPO.setCreatedAt(LocalDateTime.now());
        orgStructMdPO.setUpdatedAt(LocalDateTime.now());

        return orgStructMdPO;
    }

    /**
     * 查找父级组织ID
     */
    private Long findParentOrgId(String parentUuid) {
        OrgStructMdPO parentOrg = findOrgByUuid(parentUuid);
        if (parentOrg == null) {
            throw new BusinessException("父级组织不存在，UUID: " + parentUuid);
        }
        return parentOrg.getId();
    }

    /**
     * 更新扩展字段信息
     */
    private void updateExtendFields(OrgStructMdPO orgStructMdPO, OrgSyncDTO orgSyncDTO) {
        List<OrgSyncDTO.ExtendFieldsDTO> extendFields = orgSyncDTO.getExtendFields();
        if (extendFields != null && !extendFields.isEmpty()) {
            // 取第一个扩展字段对象（通常只有一个）
            OrgSyncDTO.ExtendFieldsDTO extendField = extendFields.get(0);

            // 记录扩展字段信息到日志，便于后续扩展
            log.debug("处理扩展字段信息: 公司编码={}, 公司名称={}, 组织全路径={}",
                    extendField.getCompanycode(),
                    extendField.getCompanyname(),
                    extendField.getOrgfullpath());

            // 可以根据需要设置其他扩展字段
            // 例如：设置组织描述、组织类型等
            // 由于OrgStructMdPO的具体字段不确定，这里暂时只记录日志
        }
    }
}
