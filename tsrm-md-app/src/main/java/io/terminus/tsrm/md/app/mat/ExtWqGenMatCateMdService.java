package io.terminus.tsrm.md.app.mat;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.terminus.erp.md.infrastructure.repo.base.GenMatCateMdRepo;
import io.terminus.erp.md.spi.dict.GenMatCateMdStatusDict;
import io.terminus.erp.md.spi.model.po.base.GenMatCateMdPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ExtWqGenMatCateMdService {

    @Resource
    private GenMatCateMdRepo genMatCateMdRepo;

    public void disable(Long categoryId) {
        List<Long> updateCategoryIdList = new ArrayList<>();
        updateCategoryIdList.add(categoryId);
        LambdaQueryWrapper<GenMatCateMdPO> queryWrapper = new LambdaQueryWrapper<>();
        List<GenMatCateMdPO> genMatCateMdPOS;
        List<Long> categoryIdList = new ArrayList<>();
        categoryIdList.add(categoryId);
        do {
            queryWrapper.in(GenMatCateMdPO::getMatCateParent, categoryIdList);
            genMatCateMdPOS = genMatCateMdRepo.selectList(queryWrapper);
            categoryIdList = genMatCateMdPOS.stream().map(GenMatCateMdPO::getId).collect(Collectors.toList());
            updateCategoryIdList.addAll(categoryIdList);
        } while (CollectionUtil.isNotEmpty(genMatCateMdPOS));

        LambdaUpdateWrapper<GenMatCateMdPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GenMatCateMdPO::getId, updateCategoryIdList);
        updateWrapper.set(GenMatCateMdPO::getStatus, "UNENABLED");
        genMatCateMdRepo.update(null, updateWrapper);
    }
}
