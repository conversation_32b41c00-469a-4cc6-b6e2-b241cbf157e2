package io.terminus.tsrm.md.app.mat;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.md.infrastructure.repo.base.GenUomTypeCfRepo;
import io.terminus.erp.md.infrastructure.repo.mat.GenMatMdRepo;
import io.terminus.erp.md.infrastructure.repo.mat.GenMatTypeCfRepo;
import io.terminus.erp.md.infrastructure.repo.org.OrgComOrgCfRepo;
import io.terminus.erp.md.spi.model.po.base.GenUomTypeCfPO;
import io.terminus.erp.md.spi.model.po.mat.GenMatMdPO;
import io.terminus.erp.md.spi.model.po.mat.GenMatTypeCfPO;
import io.terminus.erp.md.spi.model.po.org.OrgComOrgCfPO;
import io.terminus.tsrm.md.infrastructure.repo.mat.ExtWqGenMatComOrgMdRepo;
import io.terminus.tsrm.md.spi.convert.mat.GenMatMdExtConverter;
import io.terminus.tsrm.md.spi.dict.org.MatOperatorDict;
import io.terminus.tsrm.md.spi.model.mat.dto.GenMatMdExtDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.MatDTO;
import io.terminus.tsrm.md.spi.model.mat.dto.MatSyncDTO;
import io.terminus.tsrm.md.spi.model.mat.po.ExtWqGenMatComOrgMdPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料 扩展 服务
 *
 * <AUTHOR>
 * @date 2023/9/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MatExtService {

    private final GenMatMdRepo genMatMdRepo;

    private final GenMatMdExtConverter genMatMdExtConverter;

    private final OrgComOrgCfRepo comOrgCfRepo;

    private final ExtWqGenMatComOrgMdRepo genMatComOrgMdRepo;

    private final GenMatTypeCfRepo genMatTypeCfRepo;

    private final GenUomTypeCfRepo genUomTypeCfRepo;

    public GenMatMdExtDTO queryMatExt(GenMatMdExtDTO matMdExtDTO) {
        // 用terp-md的repo查询物料表，ext字段都在extra这个map里面
        GenMatMdPO genMatMdPO = genMatMdRepo.selectById(matMdExtDTO.getId());
        // 自动生产的converter代码会把po中extra的map的字段转化到dto里面
        return genMatMdExtConverter.convert(genMatMdPO);
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean sync(List<MatSyncDTO> matSyncDTOList) {
        //校验
        if (CollectionUtils.isEmpty(matSyncDTOList)) {
            throw new BusinessException("MD.mat.sync.dto.list.empty");
        }
        // validateData
        for (MatSyncDTO matSyncDTO : matSyncDTOList) {
            validateFilterAndFillData(matSyncDTO);
        }

        //分组
        Map<String, List<MatSyncDTO>> matMap = matSyncDTOList.stream().collect(Collectors.groupingBy(MatSyncDTO::getOperator));
        //新增
        List<MatSyncDTO> addMatSyncDTOS = matMap.get(MatOperatorDict.ADD);
        if (CollectionUtils.isNotEmpty(addMatSyncDTOS)) {
            LambdaQueryWrapper<OrgComOrgCfPO> queryWrapper1 = new LambdaQueryWrapper<>();
            Set<String> comOrgCodes = addMatSyncDTOS.stream().map(o -> o.getMatDTO().getComOrgCode()).collect(Collectors.toSet());
            queryWrapper1.in(OrgComOrgCfPO::getCode, comOrgCodes);

            //查询组织代码
            List<OrgComOrgCfPO> orgComOrgCfPOS = comOrgCfRepo.selectList(queryWrapper1);
            Map<String, OrgComOrgCfPO> collect = orgComOrgCfPOS.stream().collect(Collectors.toMap(OrgComOrgCfPO::getCode, Function.identity()));
            for (MatSyncDTO addMatSyncDTO : addMatSyncDTOS) {
                //转换
//            GenMatMdPO genMatMdPO = genMatMdExtConverter.convertMatSync2PO(addMatSyncDTO.getMatDTO());
                GenMatMdPO genMatMdPO = convert(addMatSyncDTO.getMatDTO());
                genMatMdRepo.insert(genMatMdPO);
                ExtWqGenMatComOrgMdPO extWqGenMatComOrgMdPO = new ExtWqGenMatComOrgMdPO();
                OrgComOrgCfPO orDefault = collect.get(addMatSyncDTO.getMatDTO().getComOrgCode());
                extWqGenMatComOrgMdPO.setComOrg(orDefault.getId());
                extWqGenMatComOrgMdPO.setMaterial(genMatMdPO.getId());
                extWqGenMatComOrgMdPO.setMaterialCode(genMatMdPO.getMatCode());
                genMatComOrgMdRepo.insert(extWqGenMatComOrgMdPO);
            }
        }


        List<MatSyncDTO> editMatSyncDTOS = matMap.get(MatOperatorDict.EDIT);
        if (CollectionUtils.isNotEmpty(editMatSyncDTOS)) {
            List<String> codeList = editMatSyncDTOS.stream().map(o -> o.getMatDTO().getMatCode()).collect(Collectors.toList());
            LambdaQueryWrapper<GenMatMdPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(GenMatMdPO::getMatCode, codeList);
            List<GenMatMdPO> genMatMdPOS = genMatMdRepo.selectList(queryWrapper);
            Map<String, GenMatMdPO> genMatMdPOMap = genMatMdPOS.stream().collect(Collectors.toMap(GenMatMdPO::getMatCode, Function.identity()));
            for (MatSyncDTO editMatSyncDTO : editMatSyncDTOS) {
                GenMatMdPO genMatMdPO = convert(editMatSyncDTO.getMatDTO());
                GenMatMdPO genMatMdPO1 = genMatMdPOMap.get(editMatSyncDTO.getMatDTO().getMatCode());
                genMatMdPO.setId(genMatMdPO1.getId());
                genMatMdRepo.updateById(genMatMdPO);
            }
        }
        //TODO 禁用这个还没有场景
        List<MatSyncDTO> disableSyncDTOS = matMap.get(MatOperatorDict.DISABLE);

        return Boolean.TRUE;
    }

    private void validateFilterAndFillData(MatSyncDTO addMatSyncDTO) {
        if (Objects.isNull(addMatSyncDTO) || Objects.isNull(addMatSyncDTO.getMatDTO())) {
            throw new BusinessException("MD.mat.empty");
        }
        MatDTO matDTO = addMatSyncDTO.getMatDTO();
        if (StringUtils.isEmpty(matDTO.getMatCode())) {
            throw new BusinessException("MD.mat.code.empty");
        }
        if (StringUtils.isEmpty(matDTO.getGenMatType())) {
            throw new BusinessException("MD.mat.type.empty");
        }

        if (StringUtils.isEmpty(matDTO.getMainUnit())) {
            throw new BusinessException("MD.mat.main.unit.empty");
        }

    }


    private GenMatMdPO convert(MatDTO matDTO) {
        //基础数据转换
        GenMatMdPO genMatMdPO = genMatMdExtConverter.convertMatSync2PO(matDTO);
        //设置物料类型
        GenMatTypeCfPO genMatTypeCfPO = genMatTypeCfRepo.selectOne(new LambdaQueryWrapper<GenMatTypeCfPO>().eq(GenMatTypeCfPO::getMatTypeCode, matDTO.getGenMatType()));
        if (genMatTypeCfPO == null) {
            throw new BusinessException("MD.mat.type.not.exist", (Object)matDTO.getGenMatType());
        }
        //使用name
        GenUomTypeCfPO genUomTypeCfPO = genUomTypeCfRepo.selectOne(new LambdaQueryWrapper<GenUomTypeCfPO>().eq(GenUomTypeCfPO::getUomDesc, matDTO.getMainUnit()));
        if (genUomTypeCfPO == null) {
            throw new BusinessException("MD.mat.main.unit.not.exist", (Object)matDTO.getMainUnit());
        }
        genMatMdPO.setGenMatTypeCfId(genMatTypeCfPO.getId());
        genMatMdPO.setBaseUomId(genUomTypeCfPO.getId());
        return genMatMdPO;
    }


}
