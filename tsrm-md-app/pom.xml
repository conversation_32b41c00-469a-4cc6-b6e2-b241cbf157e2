<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tsrm-md</artifactId>
        <groupId>io.terminus.tsrm</groupId>
        <version>1.0.1.JASOLAR.DEV-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tsrm-md-app</artifactId>

    <dependencies>
        <dependency>
            <groupId>io.terminus.tsrm</groupId>
            <artifactId>tsrm-md-domain</artifactId>
            <version>${project.version}</version>
        </dependency>


        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-spring-boot-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-permission-runtime-sdk</artifactId>
        </dependency>
    </dependencies>
</project>
