<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tsrm-md</artifactId>
    <groupId>io.terminus.tsrm</groupId>
    <version>1.0.1.JASOLAR.DEV-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>tsrm-md</name>
    
    <parent>
        <groupId>io.terminus.tsrm</groupId>
        <artifactId>tsrm-parent</artifactId>
        <version>1.0.1.JASOLAR.DEV-SNAPSHOT</version>
    </parent>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <modules>
        <module>tsrm-md-adapter</module>
        <module>tsrm-md-app</module>
        <module>tsrm-md-domain</module>
        <module>tsrm-md-infrastructure</module>
        <module>tsrm-md-spi</module>
        <module>tsrm-md-starter</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.terminus.tsrm</groupId>
                <artifactId>tsrm-md-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.tsrm</groupId>
                <artifactId>tsrm-md-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.tsrm</groupId>
                <artifactId>tsrm-md-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.tsrm</groupId>
                <artifactId>tsrm-md-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.tsrm</groupId>
                <artifactId>tsrm-md-spi</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-runtime-sdk</artifactId>
                <version>${terminus.trantor2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel-core</artifactId>
                <version>3.3.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>io.terminus</groupId>
                <artifactId>terminus-code-generation</artifactId>
                <version>1.0.8-RELEASE</version>
                <configuration>
                    <!-- 开发环境元数据相关信息，其他环境请自行替换 -->
                    <httpInfo>
                        <iamUrl>console-iam-dev.app.terminus.io</iamUrl>
                        <consoleUrl>trantor2-back.app.terminus.io</consoleUrl>
                        <loginApiEndpoint>/iam/api/v1/user/login/account</loginApiEndpoint>
                        <metaApiEndpoint>/api/trantor/struct-node/related-by-alias</metaApiEndpoint>
                        <account>admin</account>
                        <password>Terminus2020@</password>
                        <teamId>54</teamId>
                        <appId>85</appId>
                    </httpInfo>
                    <!-- 基本信息配置 -->
                    <generateCodeInfo>
                        <!-- 待生成代码的表信息 -->
                        <modelInfoList>
                            <modelInfo>
                                <!-- 分组名： io.terminus.tsrm.md.spi.model.tc -->
                                <domain>tc</domain>
                                <modelCodeList>
                                    <value>xxx1</value>
                                    <value>xxx2</value>
                                </modelCodeList>
                            </modelInfo>
                        </modelInfoList>
                        <!-- 是否允许覆盖 -->
                        <allowOverwrite>true</allowOverwrite>
                        <!-- 是否允许 生成瞬时模型的关联模型 默认为false -->
                        <generateLinkObj>true</generateLinkObj>
                        <!-- 增加初始化DTO中link many List -->
                        <initOneToManyLink>false</initOneToManyLink>
                        <author>robot</author>
                        <!-- 生成代码的包名 -->
                        <tablePackageName>io.terminus.tsrm.md</tablePackageName>
                    </generateCodeInfo>
                    <groupInfoList>
                        <!-- 按照不同的模板生成不同的目录 进行分组 -->
                        <groupInfo>
                            <savePath>tsrm-md-spi</savePath>
                            <templateInfoList>
                                <templateInfo>
                                    <scope>MODEL</scope>
                                    <templateUrlList>
                                        <value>template/default/PO.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                                <templateInfo>
                                    <scope>TRANSIENT_MODEL</scope>
                                    <templateUrlList>
                                        <value>template/default/DTO.java.vm</value>
                                        <value>template/default/VO.java.vm</value>
                                        <value>template/default/SO.java.vm</value>
                                        <value>template/default/CreateREQ.java.vm</value>
                                        <value>template/default/PageREQ.java.vm</value>
                                        <value>template/default/UpdateREQ.java.vm</value>
                                        <value>template/default/Converter.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                                <templateInfo>
                                    <scope>DICT</scope>
                                    <templateUrlList>
                                        <value>template/default/Dict.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                            </templateInfoList>
                        </groupInfo>
                        <groupInfo>
                            <savePath>tsrm-md-infrastructure</savePath>
                            <templateInfoList>
                                <templateInfo>
                                    <scope>MODEL</scope>
                                    <templateUrlList>
                                        <value>template/default/Repo.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                            </templateInfoList>
                        </groupInfo>
                    </groupInfoList>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
