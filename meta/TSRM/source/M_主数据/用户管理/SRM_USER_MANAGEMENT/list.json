{"access": "Private", "key": "TSRM$SRM_USER_MANAGEMENT:list", "name": "list", "props": {"content": {"children": [{"children": [{"children": [], "key": "TSRM$SRM_USER_MANAGEMENT-J7Sw6ZntGcCBn5p9Xmg9A", "name": "Text", "props": {"copyable": true, "value": "用户管理"}, "type": "Widget"}], "key": "TSRM$SRM_USER_MANAGEMENT-tYv8xHYHyVFGwri_H8yg7", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "TSRM$SRM_USER_MANAGEMENT-BbMjE7BNMm9sYbiV_rcHz", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_USER_MANAGEMENT-CNH2GJ361nhBCg_e4AZFW", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_USER_MANAGEMENT-j2QK2qP6CG2GwCt7WM1ZO", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_USER_MANAGEMENT-50dvrWGnw7rpmEbmm5hhG", "name": "ActionsGroup", "props": {"params": []}, "type": "Layout"}], "key": "TSRM$SRM_USER_MANAGEMENT-ZKKRD-tnfPKdJ4ZP6XeHO", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "TSRM$SRM_USER_MANAGEMENT-TgjlX_O5sDMPFUWdPUgKh", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_USER_MANAGEMENT-mi4SjT3emytU4Cfyb2YUO", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_USER_MANAGEMENT-sTOnCzYp0yQgzdz_JhTry", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_USER_MANAGEMENT-WKgXneWLhdjYVQuYc9owq", "name": "ActionsGroup", "props": {"params": []}, "type": "Layout"}], "key": "TSRM$SRM_USER_MANAGEMENT-sMRogOkLdMQVlaBnPt4Pi", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs", "name": "UserList", "props": {"dataFrom": "USER_PAGING", "fields": [{"key": "username", "show": true, "title": "用户名"}, {"key": "email", "show": true, "title": "邮箱"}, {"key": "mobile", "show": true, "title": "手机号"}, {"key": "nickname", "show": true, "title": "昵称"}, {"key": "realname", "show": true, "title": "姓名"}, {"key": "status", "show": true, "title": "启用状态"}, {"key": "applicationName", "show": true, "title": "应用名称"}, {"key": "locked", "show": true, "title": "锁定状态"}], "filterFields": [{"key": "username", "show": true, "title": "用户名"}, {"key": "email", "show": true, "title": "邮箱"}, {"key": "mobile", "show": true, "title": "手机号"}, {"key": "nickname", "show": true, "title": "昵称"}, {"key": "realname", "show": false, "title": "姓名"}, {"key": "status", "show": false, "title": "启用状态"}, {"key": "signByApplication", "show": true, "title": "所属门户"}], "listPlain": true, "ownPortal": "all", "recordActions": [{"key": "globalAdd", "show": true, "title": "新增用户"}, {"key": "resetPassword", "show": true, "title": "重置密码"}, {"key": "displayAccount", "show": true, "title": "停用账号"}, {"key": "edit", "show": true, "title": "编辑"}, {"key": "destroy", "show": true, "title": "注销"}, {"key": "bindRole", "show": true, "title": "绑定角色"}, {"key": "unLockUser", "show": true, "title": "解锁用户"}], "userListBind": {"permissionKey": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs_userListBind_perm_ac"}, "userListCreate": {"permissionKey": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs_userListCreate_perm_ac"}, "userListDestroy": {"permissionKey": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs_userListDestroy_perm_ac"}, "userListDisabled": {"permissionKey": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs_userListDisabled_perm_ac"}, "userListEdit": {"permissionKey": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs_userListEdit_perm_ac"}, "userListEnabled": {"permissionKey": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs_userListEnabled_perm_ac"}, "userListResetPassword": {"permissionKey": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs_userListResetPassword_perm_ac"}}, "type": "Widget"}], "key": "TSRM$SRM_USER_MANAGEMENT-rEToBJ2sDks-ZhtkbJQyy", "name": "Page", "props": {"collectionService": {"createBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/save"}, "deleteBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/delete"}, "getCurrentSceneBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK"}}, "params": [], "showBack": true, "showFooter": true, "showHeader": true, "title": "用户管理"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "iam"]}, "i18nConfig": {"i18nKeySet": ["用户管理"], "i18nScanPaths": ["TSRM$SRM_USER_MANAGEMENT-J7Sw6ZntGcCBn5p9Xmg9A.props.value"]}, "key": "TSRM$SRM_USER_MANAGEMENT:list", "name": "list", "permissionKey": "TSRM$SRM_USER_MANAGEMENT:list_view_perm_ac_2f7495", "resources": [{"description": null, "key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs", "label": "用户列表", "path": [{"key": "TSRM$SRM_USER_MANAGEMENT-rEToBJ2sDks-ZhtkbJQyy", "label": "页面", "type": "Page"}], "relations": [{"key": "sys_common$API_TRANTOR_PORTAL_USER_PAGING_POST", "name": null, "props": null, "type": "Service"}, {"key": "sys_common$API_TRANTOR_PORTAL_USER_PAGING_DYNAMIC_PORTAL_POST", "name": null, "props": null, "type": "Service"}, {"key": "sys_common$API_TRANTOR_PORTAL_USER_GET_IAM_APP_LIST_GET", "name": null, "props": null, "type": "Service"}, {"key": "sys_common$API_TRANTOR_PORTAL_ROLE_RELATION_USER_GET", "name": null, "props": null, "type": "Service"}, {"key": "sys_common$API_TRANTOR_PORTAL_ROLE_PAGING_GET", "name": null, "props": null, "type": "Service"}, {"key": "sys_common$API_TRANTOR_PORTAL_USER_GET", "name": null, "props": null, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs/userListCreate", "label": "新建", "path": [{"key": "TSRM$SRM_USER_MANAGEMENT-rEToBJ2sDks-ZhtkbJQyy", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs", "label": "用户列表", "type": "UserList"}], "relations": [{"key": "sys_common$API_TRANTOR_PORTAL_USER_CREATE_POST", "name": null, "props": null, "type": "Service"}, {"key": "sys_common$API_TRANTOR_PORTAL_USER_GET_FRONT_END_CONFIG_GET", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs/userListEdit", "label": "编辑", "path": [{"key": "TSRM$SRM_USER_MANAGEMENT-rEToBJ2sDks-ZhtkbJQyy", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs", "label": "用户列表", "type": "UserList"}], "relations": [{"key": "sys_common$API_TRANTOR_PORTAL_USER_UPDATE_POST", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs/userListResetPassword", "label": "重置密码", "path": [{"key": "TSRM$SRM_USER_MANAGEMENT-rEToBJ2sDks-ZhtkbJQyy", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs", "label": "用户列表", "type": "UserList"}], "relations": [{"key": "sys_common$API_TRANTOR_PORTAL_USER_RESET_PASSWORD_POST", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs/userListDisabled", "label": "停用账号", "path": [{"key": "TSRM$SRM_USER_MANAGEMENT-rEToBJ2sDks-ZhtkbJQyy", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs", "label": "用户列表", "type": "UserList"}], "relations": [{"key": "sys_common$API_TRANTOR_PORTAL_USER_DISABLED_POST", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs/userListEnabled", "label": "启用账号", "path": [{"key": "TSRM$SRM_USER_MANAGEMENT-rEToBJ2sDks-ZhtkbJQyy", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs", "label": "用户列表", "type": "UserList"}], "relations": [{"key": "sys_common$API_TRANTOR_PORTAL_USER_ENABLED_POST", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs/userListDestroy", "label": "注销", "path": [{"key": "TSRM$SRM_USER_MANAGEMENT-rEToBJ2sDks-ZhtkbJQyy", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs", "label": "用户列表", "type": "UserList"}], "relations": [{"key": "sys_common$API_TRANTOR_PORTAL_USER_DESTROY_POST", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs/userListBind", "label": "绑定角色", "path": [{"key": "TSRM$SRM_USER_MANAGEMENT-rEToBJ2sDks-ZhtkbJQyy", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_USER_MANAGEMENT-NnblOgxjHrxnm4lXXrDLs", "label": "用户列表", "type": "UserList"}], "relations": [{"key": "sys_common$API_TRANTOR_PORTAL_ROLE_RELATION_USER_FLUSH_POST", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}], "title": "list", "type": "LIST"}, "type": "View"}