{"access": "Private", "description": "{}", "key": "TSRM$SRM_SEARCH_ROLES_SERVICE", "name": "查询TSRM采购门户下角色列表", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1igdqehsf1", "name": "开始", "nextNodeKey": "node_1igdqennj3", "props": {"globalVariable": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}}], "input": [{"elements": [{"elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "ConditionGroup", "id": null}, {"elements": [{"fieldAlias": "conditions", "fieldKey": "conditions", "fieldName": "条件对象", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$tsrm_emp_ro_temp_tr", "modelKey": "TSRM$tsrm_emp_ro_temp_tr", "modelName": "TSRM$tsrm_emp_ro_temp_tr"}}, {"fieldAlias": "logicOperator", "fieldKey": "logicOperator", "fieldName": "逻辑运算符", "fieldType": "Text", "id": null}], "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "ConditionItems", "id": null}, {"element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null}, {"fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null}, {"fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null}, {"fieldAlias": "keyword", "fieldKey": "keyword", "fieldName": "模糊搜索", "fieldType": "Text", "id": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "id": null}], "output": [{"elements": [{"fieldAlias": "count", "fieldKey": "count", "fieldName": "count", "fieldType": "Number", "id": null}, {"element": {"fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Text", "id": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1igdqennj3", "name": "调用扩展服务", "nextNodeKey": "node_1igdqg57s5", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": null, "implementation": "TSRM$TSRM_GET_USER_ENDPOINT_ACTION", "implementationName": "用户管理-查询当前用户所属的门户id", "newAction": true, "output": [{"elements": [{"fieldAlias": "success", "fieldKey": "success", "fieldName": "success", "fieldType": "Boolean", "id": null}, {"elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}, {"elements": [], "fieldAlias": "err", "fieldKey": "err", "fieldName": "err", "fieldType": "Object", "id": null}, {"elements": [], "fieldAlias": "info", "fieldKey": "info", "fieldName": "info", "fieldType": "Object", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "ActionResponse", "fieldType": "Object", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "data", "valueName": "data"}]}, "id": "1igdubr3s1", "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1igdqennj3", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_1igdqg57s5", "name": "调用编排服务", "nextNodeKey": "node_1igdqehsf2", "props": {"async": false, "inputMapping": [{"field": {"fieldAlias": "enabled", "fieldKey": "enabled", "fieldName": "enabled", "fieldType": "Boolean", "id": null}, "id": "1igdrimhe13", "value": {"fieldType": null, "funcExpression": "NULL()", "id": null, "type": "FuncValue"}}, {"field": {"fieldAlias": "endpointId", "fieldKey": "endpointId", "fieldName": "endpointId", "fieldType": "Number", "id": null}, "id": "1igdrimhe14", "value": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "data", "valueName": "data"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}}, {"field": {"fieldAlias": "isDefault", "fieldKey": "isDefault", "fieldName": "isDefault", "fieldType": "Boolean", "id": null}, "id": "1igdrimhe15", "value": {"fieldType": null, "funcExpression": "NULL()", "id": null, "type": "FuncValue"}}, {"field": {"fieldAlias": "key", "fieldKey": "key", "fieldName": "key", "fieldType": "Text", "id": null}, "id": "1igdrimhe16", "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "pageable", "valueName": "分页设置"}, {"fieldType": null, "valueKey": "conditionItems", "valueName": "简化版条件组"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$tsrm_emp_ro_temp_tr", "modelKey": "TSRM$tsrm_emp_ro_temp_tr", "modelName": "TSRM$tsrm_emp_ro_temp_tr"}, "valueKey": "conditions", "valueName": "条件对象"}, {"fieldType": null, "valueKey": "key", "valueName": "角色标识"}, {"fieldType": null, "valueKey": "value", "valueName": "值"}]}}, {"field": {"fieldAlias": "name", "fieldKey": "name", "fieldName": "name", "fieldType": "Text", "id": null}, "id": "1igdrimhe17", "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "pageable", "valueName": "分页设置"}, {"fieldType": null, "valueKey": "conditionItems", "valueName": "简化版条件组"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$tsrm_emp_ro_temp_tr", "modelKey": "TSRM$tsrm_emp_ro_temp_tr", "modelName": "TSRM$tsrm_emp_ro_temp_tr"}, "valueKey": "conditions", "valueName": "条件对象"}, {"fieldType": null, "valueKey": "name", "valueName": "角色名称"}, {"fieldType": null, "valueKey": "value", "valueName": "值"}]}}, {"field": {"fieldAlias": "no", "fieldKey": "no", "fieldName": "no", "fieldType": "Number", "id": null}, "id": "1igdrimhe18", "value": {"fieldType": null, "funcExpression": "NULL()", "id": null, "type": "FuncValue"}}, {"field": {"fieldAlias": "requestId", "fieldKey": "requestId", "fieldName": "requestId", "fieldType": "Text", "id": null}, "id": "1igdrimhe19", "value": {"fieldType": null, "funcExpression": "NULL()", "id": null, "type": "FuncValue"}}, {"field": {"fieldAlias": "search<PERSON>ey", "fieldKey": "search<PERSON>ey", "fieldName": "search<PERSON>ey", "fieldType": "Text", "id": null}, "id": "1igdrimhe20", "value": {"fieldType": null, "funcExpression": "NULL()", "id": null, "type": "FuncValue"}}, {"field": {"fieldAlias": "size", "fieldKey": "size", "fieldName": "size", "fieldType": "Number", "id": null}, "id": "1igdrimhe21", "value": {"fieldType": null, "funcExpression": "NULL()", "id": null, "type": "FuncValue"}}, {"field": {"fieldAlias": "skip<PERSON><PERSON>nt", "fieldKey": "skip<PERSON><PERSON>nt", "fieldName": "skip<PERSON><PERSON>nt", "fieldType": "Boolean", "id": null}, "id": "1igdrimhe22", "value": {"fieldType": null, "funcExpression": "NULL()", "id": null, "type": "FuncValue"}}, {"field": {"fieldAlias": "sort", "fieldKey": "sort", "fieldName": "sort", "fieldType": "Text", "id": null}, "id": "1igdrimhe23", "value": {"fieldType": null, "funcExpression": "NULL()", "id": null, "type": "FuncValue"}}], "output": [{"elements": [{"fieldAlias": "count", "fieldKey": "count", "fieldName": "count", "fieldType": "Number", "id": null}, {"element": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}, {"fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1igdroes724", "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1igdqg57s5", "valueName": "出参结构体"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "serviceKey": "sys_common$API_TRANTOR_PORTAL_ROLE_PAGING_GET", "serviceName": null, "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"desc": null, "id": null, "key": "node_1igdqehsf2", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"elements": [{"elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "ConditionGroup", "id": null}, {"elements": [{"fieldAlias": "conditions", "fieldKey": "conditions", "fieldName": "条件对象", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$tsrm_emp_ro_temp_tr", "modelKey": "TSRM$tsrm_emp_ro_temp_tr", "modelName": "TSRM$tsrm_emp_ro_temp_tr"}}, {"fieldAlias": "logicOperator", "fieldKey": "logicOperator", "fieldName": "逻辑运算符", "fieldType": "Text", "id": null}], "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "ConditionItems", "id": null}, {"element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null}, {"fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null}, {"fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null}, {"fieldAlias": "keyword", "fieldKey": "keyword", "fieldName": "模糊搜索", "fieldType": "Text", "id": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "id": null}], "key": "TSRM$SRM_SEARCH_ROLES_SERVICE", "name": "查询TSRM采购门户下角色列表", "output": [{"elements": [{"fieldAlias": "count", "fieldKey": "count", "fieldName": "count", "fieldType": "Number", "id": null}, {"element": {"fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Text", "id": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$SRM_SEARCH_ROLES_SERVICE_service_perm_ac_151dff", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}