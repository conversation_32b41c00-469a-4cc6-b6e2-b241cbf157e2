{"access": "Public", "description": "{}", "key": "TSRM$SRM_GET_EMPLOYEE_BY_CURRENT_USER", "name": "SRM_根据当前用户获取员工", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"id": null, "key": "node_1hk661jkq1", "name": "开始", "nextNodeKey": "node_1hk662v543", "props": {"desc": null, "globalVariable": null, "input": null, "name": null, "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"id": null, "key": "node_1hk662v543", "name": "查询数据", "nextNodeKey": "node_1hk665se44", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "xZ6et10q6EDF_ajfZ1nYZ", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "userId", "fieldName": "用户", "fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": null}, {"fieldKey": "id", "fieldName": "ID", "fieldType": null, "modelAlias": "TSRM$user", "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "userId", "valueName": "用户"}, {"fieldType": null, "modelAlias": "TSRM$user", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldPaths": [{"fieldKey": "SYS", "fieldName": "系统变量", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "CurrentUserId", "fieldName": "当前登录人id", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "SYS", "valueName": "系统变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "CurrentUserId", "valueName": "当前登录人id"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "9BMGSJZKYaw0F12PkBBgc", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "PPIhWrZeRvue_Z9S0J7WX", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "desensitized": true, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"id": null, "key": "node_1hk665se44", "name": "赋值", "nextNodeKey": "node_1hk661jkq2", "props": {"assignments": [{"field": {"constValue": null, "fieldPaths": [{"fieldKey": "OUTPUT", "fieldName": "服务出参", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "data", "fieldName": "data", "fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}}], "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "id": "1hk665tpt5", "operator": "EQ", "value": {"constValue": null, "fieldPaths": [{"fieldKey": "GLOBAL", "fieldName": "全局变量", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "NODE_OUTPUT_node_1hk662v543", "fieldName": "[查询数据]节点.output", "fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}}], "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hk662v543", "valueName": "[查询数据]节点.output"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "type": "AssignNode"}, {"id": null, "key": "node_1hk661jkq2", "name": "结束", "props": {"desc": null, "name": null, "type": "EndProperties"}, "type": "EndNode"}], "headNodeKeys": ["node_1hk661jkq1"], "id": null, "input": null, "key": "TSRM$SRM_GET_EMPLOYEE_BY_CURRENT_USER", "name": "SRM_根据当前用户获取员工", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "desc": null, "name": null, "permissionKey": "TSRM$SRM_GET_EMPLOYEE_BY_CURRENT_USER_perm_ac", "schedulerJob": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}