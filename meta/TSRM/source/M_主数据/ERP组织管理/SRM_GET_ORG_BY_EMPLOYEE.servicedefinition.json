{"access": "Public", "description": "{}", "key": "TSRM$SRM_GET_ORG_BY_EMPLOYEE", "name": "SRM_根据员工获取组织", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": "null", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1hk66eq1j11", "name": "开始", "nextNodeKey": "node_1hko872f05", "props": {"globalVariable": [{"fieldAlias": "empid", "fieldKey": "empid", "fieldName": "empID", "fieldType": "Number", "id": null}], "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}}, {"fieldAlias": "dimension_type", "fieldKey": "dimension_type", "fieldName": "dimensionType", "fieldType": "Text", "id": null}, {"fieldAlias": "business_type", "fieldKey": "business_type", "fieldName": "businessType", "fieldType": "Text", "id": null}], "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1hko872f05", "name": "赋值", "nextNodeKey": "node_1hk66fntq13", "props": {"assignments": [{"field": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "empid", "valueName": "empID"}]}, "id": "1hko873kc6", "operator": "EQ", "value": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}, {"desc": null, "id": null, "key": "node_1hk66fntq13", "name": "查询数据", "nextNodeKey": "node_1hk66h92514", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "BptAn5QLv5sQdRzDGqNMg", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "sys_common$org_employee_org_link_cf", "valueKey": "employeeId", "valueName": "员工"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "DG-ttlhFP-uSSQBkhq5I8", "key": null, "leftValue": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "sys_common$org_employee_org_link_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "valueKey": "orgUnitId", "valueName": "组织单元"}, {"fieldType": null, "modelAlias": "sys_common$org_struct_md", "valueKey": "orgBusinessTypeCodes", "valueName": "业务类型编码集合"}]}, "operator": "CONTAINS", "rightValue": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "business_type", "valueName": "businessType"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "Sa7WMZMSe4H9tJThHIKXR", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "-r8YrRLyu06HgSaACRZL1", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": null, "dataType": "MODEL", "desensitized": true, "dynamicCondition": null, "maximum": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "queryFields": [{"fieldKey": "isMainOrg"}, {"fieldKey": "identityId"}, {"fieldKey": "employeeId"}, {"fieldKey": "orgUnitId"}, {"fieldKey": "id"}, {"fieldKey": "created<PERSON>y"}, {"fieldKey": "updatedBy"}, {"fieldKey": "createdAt"}, {"fieldKey": "updatedAt"}, {"fieldKey": "version"}, {"fieldKey": "deleted"}, {"fieldKey": "originOrgId"}], "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_employee_org_link_cf", "queryFields": [{"fieldKey": "isMainOrg"}, {"fieldKey": "identityId", "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_identity_cf", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "employeeId", "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_employee_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "orgUnitId", "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_struct_md", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "orgBusinessTypeCode"}]}}, {"fieldKey": "id"}, {"fieldKey": "created<PERSON>y", "queryModelFields": {"allFields": false, "modelKey": "sys_common$user", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "updatedBy", "queryModelFields": {"allFields": false, "modelKey": "sys_common$user", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "createdAt"}, {"fieldKey": "updatedAt"}, {"fieldKey": "version"}, {"fieldKey": "deleted"}, {"fieldKey": "originOrgId"}]}, "relatedModel": {"modelAlias": "sys_common$org_employee_org_link_cf", "modelKey": "sys_common$org_employee_org_link_cf", "modelName": "员工组织关联表"}, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"conditionGroup": {"conditions": [], "id": "qNxPubGVEGsAIW7iZhE7o", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldKey": "identityId", "modelKey": "sys_common$org_identity_cf", "sortOrders": null, "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "employeeId", "modelKey": "sys_common$org_employee_md", "sortOrders": null, "subQueryRelatedModels": []}, {"conditionGroup": {"conditions": [], "id": "n6lfD4nX5rewuoUoA0iHu", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldKey": "orgUnitId", "modelKey": "sys_common$org_struct_md", "sortOrders": null, "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "created<PERSON>y", "modelKey": "sys_common$user", "sortOrders": null, "subQueryRelatedModels": []}], "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"desc": null, "id": null, "key": "node_1hk66h92514", "name": "赋值", "nextNodeKey": "node_1hk66eq1j12", "props": {"assignments": [{"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "valueKey": "data", "valueName": "data"}]}, "id": "1hk66ha8q15", "operator": "EQ", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_org_link_cf", "modelKey": "sys_common$org_employee_org_link_cf", "modelName": "员工组织关联表"}, "valueKey": "NODE_OUTPUT_node_1hk66fntq13", "valueName": "[查询数据]节点出参"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_org_link_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "valueKey": "orgUnitId", "valueName": "组织单元"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}, {"desc": null, "id": null, "key": "node_1hk66eq1j12", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}}, {"fieldAlias": "dimension_type", "fieldKey": "dimension_type", "fieldName": "dimensionType", "fieldType": "Text", "id": null}, {"fieldAlias": "business_type", "fieldKey": "business_type", "fieldName": "businessType", "fieldType": "Text", "id": null}], "key": "TSRM$SRM_GET_ORG_BY_EMPLOYEE", "name": "SRM_根据员工获取组织", "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$SRM_GET_ORG_BY_EMPLOYEE_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}