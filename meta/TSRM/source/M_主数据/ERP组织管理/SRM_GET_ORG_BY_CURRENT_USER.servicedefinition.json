{"access": "Public", "description": "{}", "key": "TSRM$SRM_GET_ORG_BY_CURRENT_USER", "name": "SRM_根据当前账号获取组织", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": "null", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1hk66eq1j11", "name": "开始", "nextNodeKey": "node_1hk66fntq13", "props": {"globalVariable": [], "input": [{"defaultValue": null, "description": null, "fieldAlias": "dimension_type", "fieldKey": "dimension_type", "fieldName": "dimensionType", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "business_type", "fieldKey": "business_type", "fieldName": "businessType", "fieldType": "Text", "id": null, "required": null}], "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "relation": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1hk66fntq13", "name": "查询数据", "nextNodeKey": "node_1hk66h92514", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "BptAn5QLv5sQdRzDGqNMg", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "sys_common$org_employee_org_link_cf", "valueKey": "employeeId", "valueName": "员工"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "valueKey": "userId", "valueName": "用户"}, {"fieldType": null, "modelAlias": "TSRM$user", "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "SYS", "valueName": "系统变量"}, {"fieldType": null, "valueKey": "CurrentUserId", "valueName": "当前登录人id"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "8wPxyrRNriJmh_FbbPZQJ", "key": null, "leftValue": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "sys_common$org_employee_org_link_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "valueKey": "orgUnitId", "valueName": "组织单元"}, {"fieldType": null, "modelAlias": "sys_common$org_struct_md", "valueKey": "orgBusinessTypeCodes", "valueName": "业务类型编码集合"}]}, "operator": "CONTAINS", "rightValue": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "business_type", "valueName": "businessType"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "Sa7WMZMSe4H9tJThHIKXR", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "-r8YrRLyu06HgSaACRZL1", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": null, "dataType": "MODEL", "desensitized": true, "maximum": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "queryFields": [{"fieldKey": "isMainOrg"}, {"fieldKey": "identityId"}, {"fieldKey": "employeeId"}, {"fieldKey": "orgUnitId"}, {"fieldKey": "id"}, {"fieldKey": "created<PERSON>y"}, {"fieldKey": "updatedBy"}, {"fieldKey": "createdAt"}, {"fieldKey": "updatedAt"}, {"fieldKey": "version"}, {"fieldKey": "deleted"}, {"fieldKey": "originOrgId"}], "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_employee_org_link_cf", "queryFields": [{"fieldKey": "isMainOrg"}, {"fieldKey": "identityId", "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_identity_cf", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "employeeId", "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_employee_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "orgUnitId", "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_struct_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "id"}, {"fieldKey": "created<PERSON>y", "queryModelFields": {"allFields": false, "modelKey": "sys_common$user", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "updatedBy", "queryModelFields": {"allFields": false, "modelKey": "sys_common$user", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "createdAt"}, {"fieldKey": "updatedAt"}, {"fieldKey": "version"}, {"fieldKey": "deleted"}, {"fieldKey": "originOrgId"}]}, "relatedModel": {"modelAlias": "sys_common$org_employee_org_link_cf", "modelKey": "sys_common$org_employee_org_link_cf", "modelName": "员工组织关联表"}, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"conditionGroup": null, "fieldKey": "identityId", "modelKey": "sys_common$org_identity_cf", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "employeeId", "modelKey": "sys_common$org_employee_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "orgUnitId", "modelKey": "sys_common$org_struct_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "created<PERSON>y", "modelKey": "sys_common$user", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "updatedBy", "modelKey": "sys_common$user", "subQueryRelatedModels": []}], "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"desc": null, "id": null, "key": "node_1hk66h92514", "name": "赋值", "nextNodeKey": "node_1hk66eq1j12", "props": {"assignments": [{"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "valueKey": "data", "valueName": "data"}]}, "id": "1hk66ha8q15", "operator": "EQ", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_org_link_cf", "modelKey": "sys_common$org_employee_org_link_cf", "modelName": "员工组织关联表"}, "valueKey": "NODE_OUTPUT_node_1hk66fntq13", "valueName": "[查询数据]节点出参"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_org_link_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "valueKey": "orgUnitId", "valueName": "组织单元"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}, {"desc": null, "id": null, "key": "node_1hk66eq1j12", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1hk66eq1j11"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "dimension_type", "fieldKey": "dimension_type", "fieldName": "dimensionType", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "business_type", "fieldKey": "business_type", "fieldName": "businessType", "fieldType": "Text", "id": null, "required": null}], "key": "TSRM$SRM_GET_ORG_BY_CURRENT_USER", "name": "SRM_根据当前账号获取组织", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "relation": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "TSRM$SRM_GET_ORG_BY_CURRENT_USER_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}