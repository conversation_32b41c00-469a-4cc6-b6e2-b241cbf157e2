{"access": "Private", "key": "TSRM$TSRM_PURCHASER_WORKBANCH:list", "name": "list", "props": {"content": {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-IndicatorCard", "name": "IndicatorCard", "props": {"indicatorList": [{"key": "gHw_RSddbl-VmTE19l8B5", "label": "采购待办"}, {"key": "qQrFSX0JGSDNwos_zi83A", "label": "需求待办"}, {"key": "aXnoaz6U-gB_jw-_W_KEz", "label": "询价待办"}], "title": "我的待办"}, "type": "Widget"}], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid-item-1-grid-item-1", "name": "GridItem", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-Approve", "name": "Approve", "props": {"allowFieldsServiceProps": {"queryAllowFieldsFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_INSTANCE_COUNT_GET", "type": "InvokeService"}}, "approvalShowList": [{"isChosen": true, "key": "WAITING", "label": "待我处理的"}, {"isChosen": true, "key": "HANDLED", "label": "已处理的"}, {"isChosen": true, "key": "START", "label": "我发起的"}], "listSceneKey": "sys_common$approval_task_center", "title": "审批中心"}, "type": "Widget"}], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid-item-1-grid-item-2", "name": "GridItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid-item-1-grid", "name": "Grid", "props": {"colNumber": 2, "colRatio": [6, 6], "customHeight": false, "gap": 8, "mode": "vertical", "type": "2"}, "type": "Layout"}], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid-item-1", "name": "GridItem", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_PURCHASER_WORKBANCH-08MSiucE8m9OwiZ7kCMlS", "name": "MyNotice", "props": {"WorkbenchMyNoticeContainerProps": {"fetchNoticeDetailFlow": {"serviceKey": "sys_common$API_NOTICE_STATION_QUERY_BY_ID_POST", "type": "InvokeService"}, "pagingFlow": {"serviceKey": "sys_common$API_NOTICE_STATION_PAGING_POST", "type": "InvokeService"}}, "WorkbenchMyNoticeTarget": {}, "isShowMenu": false, "isShowNotice": true, "pageSize": 10, "queryNoticeListFlow": {"serviceKey": "sys_common$API_NOTICE_STATION_PAGING_POST", "type": "InvokeService"}}, "type": "Widget"}], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid-item-2", "name": "GridItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid", "name": "Grid", "props": {"colNumber": 2, "colRatio": [7, 5], "customHeight": false, "gap": 8, "mode": "horizontal", "type": "7-5"}, "type": "Layout"}], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1", "name": "GridItem", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_PURCHASER_WORKBANCH-aiYStVrdGf2nCH7yNBCSg", "name": "NavigationPanel", "props": {"colsNumber": 8, "items": [{"key": "pesAGrKuiYBGdNFRUr2Zu", "label": "采购需求", "pageKey": "ERP_FIN$SETT_ITEM-list", "sceneKey": "TSRM$pla_admin_view"}, {"key": "33E9FJ8Yx-zTQ8MvoOJld", "label": "采购合同", "pageKey": "CON$PURCHASER_CON_LIST_VIEW-list", "sceneKey": "CON$PURCHASER_CON_LIST_VIEW"}, {"key": "ADd7R4etEbVJZVIT5QCVo", "label": "采购订单", "pageKey": "ERP_SCM$5FK5Ssd3Ov6z1uVTvum6n", "sceneKey": "ERP_SCM$TERP_MIGRATE_PO"}, {"key": "63pv85GGr9yUOTmVXD1pz", "label": "采购交货", "pageKey": "ERP_SCM$DEL_DN_PUR_VIEW-list", "sceneKey": "ERP_SCM$DEL_DN_PUR_VIEW"}, {"key": "WP-xWE5iVAOIysdhRiw7a", "label": "采购询价", "pageKey": "ERP_FIN$SETT_DOC-list", "sceneKey": "TSRM$SRM_TSRC_INQUIRY_MANAGER_VIEW"}, {"key": "N8ViITduAcPF-erOChceq", "label": "价格协议", "pageKey": "ERP_FIN$FIN_APM_230706-list", "sceneKey": "CON$srm_price_agreement"}, {"key": "bxHlPFWqmA-s521EACT5Q", "label": "采购发票", "pageKey": "ERP_FIN$FIN_TM_PI-list", "sceneKey": "ERP_FIN$FIN_TM_PI"}], "title": "应用中心"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_PURCHASER_WORKBANCH-EqcIZ6W4izVvqh_hRv_Ht", "name": "MultiText", "props": {"format": "html", "value": "   \n"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_PURCHASER_WORKBANCH-gEgLtdPLE_TRoxm70LaDL", "name": "DorsPage", "props": {"filterFields": [], "pageId": "clwydmxv7000c7knfpjl0cmjx", "workspace": "clv4xgzsn000052wpmu5zn9ba"}, "type": "Container"}], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-2", "name": "GridItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid", "name": "Grid", "props": {"colNumber": 2, "colRatio": [6, 6], "customHeight": false, "gap": 8, "mode": "vertical", "type": "custom"}, "type": "Layout"}], "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-page", "name": "View", "props": {"showHeader": false, "title": "PC工作台测试"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "dors", "terp", "service"]}, "i18nConfig": {"i18nKeySet": ["应用中心", "   \n"], "i18nScanPaths": ["TSRM$SRM_PURCHASER_WORKBANCH-aiYStVrdGf2nCH7yNBCSg.props.title", "TSRM$SRM_PURCHASER_WORKBANCH-EqcIZ6W4izVvqh_hRv_Ht.props.value"]}, "key": "TSRM$TSRM_PURCHASER_WORKBANCH:list", "permissionKey": "TSRM$TSRM_PURCHASER_WORKBANCH:list_perm_ac", "resources": [{"description": null, "key": "TSRM$SRM_PURCHASER_WORKBANCH-08MSiucE8m9OwiZ7kCMlS/WorkbenchMyNotice", "label": "组件调用服务", "path": [{"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-page", "label": "视图", "type": "View"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid", "label": "分栏", "type": "Grid"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1", "label": "分栏项", "type": "GridItem"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid", "label": "分栏", "type": "Grid"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid-item-2", "label": "分栏项", "type": "GridItem"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-08MSiucE8m9OwiZ7kCMlS", "label": "个人站内信", "type": "MyNotice"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-Approve", "label": "工作台审批", "path": [{"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-page", "label": "视图", "type": "View"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid", "label": "分栏", "type": "Grid"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1", "label": "分栏项", "type": "GridItem"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid", "label": "分栏", "type": "Grid"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid-item-1", "label": "分栏项", "type": "GridItem"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid-item-1-grid", "label": "分栏", "type": "Grid"}, {"key": "TSRM$SRM_PURCHASER_WORKBANCH-workbench-grid-item-1-grid-item-1-grid-item-2", "label": "分栏项", "type": "GridItem"}], "relations": [{"key": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_INSTANCE_COUNT_GET", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}