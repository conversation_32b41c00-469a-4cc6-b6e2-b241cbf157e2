{"access": "Private", "key": "TSRM$COMM_2B_TAX1:list", "name": "list", "props": {"containerSelect": {"TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail": [{"field": "taxCode", "selectFields": null}, {"field": "taxcate", "selectFields": null}, {"field": "tax", "selectFields": null}, {"field": "counId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "coun<PERSON><PERSON>", "selectFields": null}]}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}], "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form": [{"field": "version", "selectFields": null}, {"field": "taxCode", "selectFields": null}, {"field": "taxcate", "selectFields": null}, {"field": "tax", "selectFields": null}, {"field": "counId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "coun<PERSON><PERSON>", "selectFields": null}]}], "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf": [{"field": "taxCode", "selectFields": null}, {"field": "taxcate", "selectFields": null}, {"field": "tax", "selectFields": null}]}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "permissionKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list_perm_ac_z_0_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_tax_type_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ""}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "COMMON_2B$COMM_2B_TAX-TERP_MIGRATE$gen_tax_type_cf-multi-delete", "label": "批量删除", "permissionKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list_perm_ac_z_0_0_0_1_items_0"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-import", "name": "ImportButton", "props": {"addApprovalManageServiceProps": {"predictFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/gei/task/config/predict"}}, "deleteApprovalManageServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "downloadFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}, "downloadServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}}, "editApprovalManageServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "label": "导入", "permissionKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list_perm_ac_z_0_0_0_2", "predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}, "predictServiceProps": {"predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}}, "saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}, "saveSubServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}}}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"getUserInfoFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/portal/user/current"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "label": "导出", "permissionKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list_perm_ac_z_0_0_0_3", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "type": "Widget"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$COMM_2B_TAX1-aEUnSkcXZ1yYQWGZOp3V9", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-YGmq3YdXuBaK7preT8ITP", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxCode", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "税码", "name": "taxCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-TStT8rw6d5IyPubiD8uHL", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxcate", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "hidden": false, "label": "类型", "name": "taxcate", "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-KCuvX83tPz4FRNS6V3Qwd", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "tax", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入", "precision": 6}, "hidden": false, "label": "税率", "name": "tax", "type": "NUMBER", "width": 144}, "type": "Widget"}], "key": "TSRM$COMM_2B_TAX1-1JMpypGW8GGtb3LFRlq2J", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "taxCode", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "税码", "name": "taxCode", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "context$": "$context", "modelAlias": "ERP_GEN$gen_tax_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_tax_type_cf"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_tax_type_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showFilterFields": true, "showScope": "all", "tableCondition": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"税配置表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_tax_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "ERP_GEN$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list_perm_ac_z_0_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list_perm_ac_z_0_1_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list_perm_ac_z_0_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-taxCode", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxCode", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "税码", "name": "taxCode", "type": "TEXT"}}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-taxcate", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxcate", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "editable": false, "label": "类型", "name": "taxcate", "type": "SELECT"}}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-tax", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "tax", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入", "precision": 6}, "editable": false, "label": "税率", "name": "tax", "type": "NUMBER"}}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-counId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "counId", "label": "选择国家", "labelField": "counCode", "modelAlias": "ERP_GEN$gen_coun_type_cf", "parentModelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_coun_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "国家", "name": "counId", "type": "OBJECT"}}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-e5LCpyrR2ACht-Awo1737", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-TbxU7j2t4j2TXR3eZd_1J", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "context$": "$context", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "Container"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"税配置表\")) : \"新建税配置表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "9jwJ7HlQYIawQvJjisBeJ", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "ei4IC5IzrLgxiwvblkW7t", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-taxCode", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxCode", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "税码", "name": "taxCode", "rules": [{"message": "请输入税码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-taxcate", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxcate", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "类型", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "-Z45FtZq12GUKTc0_b_TJ", "operator": null, "valueRules": null}], "name": "taxcate", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-tax", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "tax", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "editComponentProps": {}, "editComponentType": "InputNumber", "hidden": false, "label": "税率", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": false, "required": true}, "key": "aCGZsi1xHMGXC2YdoMoqd", "operator": null, "valueRules": null}], "name": "tax", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "counId", "label": "选择国家", "labelField": "counCode", "modelAlias": "ERP_GEN$gen_coun_type_cf", "parentModelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_coun_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "counCode", "modelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "国家代码", "name": "counCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "coun<PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "名称", "name": "coun<PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["languageCode", "languageName"], "fieldAlias": "languageId", "label": "选择语言", "labelField": "languageName", "modelAlias": "ERP_GEN$gen_language_type_cf", "parentModelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "语言", "name": "languageId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "currId", "label": "选择币种", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "parentModelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "币种", "name": "currId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["timezoneCode", "timezoneDesc", "counId", "timezoneDistrict", "timezoneFormat", "isSummerTime", "summerTimeStart", "summerTimeEnd", "status"], "fieldAlias": "timezoneId", "label": "选择时区", "labelField": "timezoneDesc", "modelAlias": "ERP_GEN$gen_timezone_type_cf", "parentModelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "时区", "name": "timezoneId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "defaultRelv", "modelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "是否默认", "name": "defaultRelv", "required": false, "type": "BOOL", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "counCode", "modelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "国家代码", "name": "counCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "coun<PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "名称", "name": "coun<PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["languageCode", "languageName"], "fieldAlias": "languageId", "label": "选择语言", "labelField": "languageName", "modelAlias": "ERP_GEN$gen_language_type_cf", "parentModelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "语言", "name": "languageId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "currId", "label": "选择币种", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "parentModelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "币种", "name": "currId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["timezoneCode", "timezoneDesc", "counId", "timezoneDistrict", "timezoneFormat", "isSummerTime", "summerTimeStart", "summerTimeEnd", "status"], "fieldAlias": "timezoneId", "label": "选择时区", "labelField": "timezoneDesc", "modelAlias": "ERP_GEN$gen_timezone_type_cf", "parentModelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "时区", "name": "timezoneId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "defaultRelv", "modelAlias": "ERP_GEN$gen_coun_type_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "是否默认", "name": "defaultRelv", "required": false, "type": "BOOL", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_coun_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId", "context$": "$context", "modelAlias": "ERP_GEN$gen_tax_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_tax_type_cf"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "counCode", "modelAlias": "ERP_GEN$gen_coun_type_cf"}, "editComponentType": "RelationSelect", "hidden": false, "label": "国家", "name": "counId", "rules": [], "type": "OBJECT"}, "type": "Widget"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "modelAlias": "ERP_GEN$gen_tax_type_cf", "params$": "{ id: route.recordId }", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "modelAlias": "ERP_GEN$gen_tax_type_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "ERP_GEN$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list_perm_ac_z_0_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_tax_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form"}, "type": "action"}}], "service": "ERP_GEN$SYS_MasterData_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list_perm_ac_z_0_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "税"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["请输入版本号", "时区", "名称", "保存", "选择币种", "请输入ID", "用户名", "ID", "创建人", "请输入更新时间", "保存成功!", "新建税配置表", "是否默认", "语言", "逻辑删除标识", "请选择", "版本号", "更新时间", "选择国家", "编辑", "税率", "选择创建人", "请输入逻辑删除标识", "税码", "确认删除吗？", "请输入税码", "复制", "选择更新人", "表格", "系统信息", "新建", "删除成功", "批量删除", "请输入创建时间", "选择语言", "删除", "国家", "税配置表", "批量操作", "主体信息", "币种", "更新人", "税配置表详情", "请输入", "取消", "创建时间", "国家代码", "选择时区", "类型"], "i18nScanPaths": ["TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.0.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-taxcate.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.3.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-deleted.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.4.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-tax.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-taxcate.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-new.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.2.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.5.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.5.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs.props.items.0.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-createdBy.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-version.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-aEUnSkcXZ1yYQWGZOp3V9.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedAt.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-taxCode.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-taxcate.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-updatedAt.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.3.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedBy.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdAt.props.rules.0.message", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.4.componentProps.label", "TSRM$COMM_2B_TAX1-aEUnSkcXZ1yYQWGZOp3V9.props.label", "TSRM$COMM_2B_TAX1-TStT8rw6d5IyPubiD8uHL.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-action-cancel.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-taxcate.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-updatedBy.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.3.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs.props.items.1.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-taxCode.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdAt.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf.props.filterFields.0.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-taxCode.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-YGmq3YdXuBaK7preT8ITP.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.2.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.2.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedAt.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-copy.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-updatedBy.props.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-tax.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.1.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-tax.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.5.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-id.props.rules.0.message", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.4.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdBy.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.1.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-tax.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-edit.props.label", "TSRM$COMM_2B_TAX1-KCuvX83tPz4FRNS6V3Qwd.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.2.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-version.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.3.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "@exp:TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-page-title.props.title", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch.props.items.0.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-deleted.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.3.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.1.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.4.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-version.props.rules.0.message", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdBy.props.editComponentProps.fields.0.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch.props.items.0.actionConfig.endLogicOtherConfig.1.message", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdBy.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-deleted.props.rules.0.message", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedAt.props.rules.0.message", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-action-save.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.3.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.4.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-createdAt.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedBy.props.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-id.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.0.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdBy.props.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.5.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedBy.props.editComponentProps.fields.0.label", "TSRM$COMM_2B_TAX1-YGmq3YdXuBaK7preT8ITP.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedBy.props.label", "TSRM$COMM_2B_TAX1-TStT8rw6d5IyPubiD8uHL.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdAt.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-createdBy.props.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-delete.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-id.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-taxCode.props.rules.0.message", "@exp:TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page-title.props.title", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.2.componentProps.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.filterFields.4.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "TSRM$COMM_2B_TAX1-KCuvX83tPz4FRNS6V3Qwd.props.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.2.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs.props.items.0.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf.props.filterFields.0.componentProps.placeholder", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId.props.editComponentProps.fields.1.label", "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-taxCode.props.label"]}, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list", "permissionKey": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list_perm_ac", "resources": [{"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "label": "表格", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-new", "label": "新建", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch/items/COMMON_2B$COMM_2B_TAX-TERP_MIGRATE$gen_tax_type_cf-multi-delete", "label": "批量删除", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "ERP_GEN$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-import", "label": "导入", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-sub-model", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/template/download", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/config/predict", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}, {"key": "/api/trantor/workflow/v2/{param0}", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-export", "label": "导出", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/trantor/portal/user/current", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-aEUnSkcXZ1yYQWGZOp3V9", "label": "ID", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_TAX1-1JMpypGW8GGtb3LFRlq2J", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-YGmq3YdXuBaK7preT8ITP", "label": "税码", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_TAX1-1JMpypGW8GGtb3LFRlq2J", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-TStT8rw6d5IyPubiD8uHL", "label": "类型", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_TAX1-1JMpypGW8GGtb3LFRlq2J", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-KCuvX83tPz4FRNS6V3Qwd", "label": "税率", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-list-TERP_MIGRATE$gen_tax_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_TAX1-1JMpypGW8GGtb3LFRlq2J", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "label": "详情", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-action-cancel", "label": "取消", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-action-save", "label": "保存", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$SYS_MasterData_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-delete", "label": "删除", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-copy", "label": "复制", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions-edit", "label": "编辑", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-id", "label": "ID", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdBy", "label": "创建人", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedBy", "label": "更新人", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-createdAt", "label": "创建时间", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-updatedAt", "label": "更新时间", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-version", "label": "版本号", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-taxCode", "label": "税码", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-taxcate", "label": "类型", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-tax", "label": "税率", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-field-counId", "label": "国家", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-vmZmumbIENvMxtLnWmaf1", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-editView-TERP_MIGRATE$gen_tax_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_language_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_language_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_timezone_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_timezone_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-taxCode", "label": "税码", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-e5LCpyrR2ACht-Awo1737", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-taxcate", "label": "类型", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-e5LCpyrR2ACht-Awo1737", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-tax", "label": "税率", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-e5LCpyrR2ACht-Awo1737", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-field-counId", "label": "国家", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-e5LCpyrR2ACht-Awo1737", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-TbxU7j2t4j2TXR3eZd_1J", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-TbxU7j2t4j2TXR3eZd_1J", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-TbxU7j2t4j2TXR3eZd_1J", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-TERP_MIGRATE$gen_tax_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_TAX1-TbxU7j2t4j2TXR3eZd_1J", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_TAX1-COMMON_2B$COMM_2B_TAX-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}