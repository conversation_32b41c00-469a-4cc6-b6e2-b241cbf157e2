{"access": "Private", "key": "TSRM$COMM_2B_CURRENCY_TYPE:list", "name": "list", "props": {"containerSelect": {"COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail": [{"field": "currCode", "selectFields": null}, {"field": "currName", "selectFields": null}, {"field": "symbol", "selectFields": null}, {"field": "remark", "selectFields": null}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}], "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form": [{"field": "version", "selectFields": null}, {"field": "currCode", "selectFields": null}, {"field": "currName", "selectFields": null}, {"field": "currIsoName", "selectFields": null}, {"field": "decimalPlace", "selectFields": null}, {"field": "symbol", "selectFields": null}, {"field": "remark", "selectFields": null}], "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf": [{"field": "currCode", "selectFields": null}, {"field": "currName", "selectFields": null}]}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "label": "新建", "permissionKey": "TSRM$COMM_2B_CURRENCY_TYPE-list_perm_ac_z_0_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_curr_type_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ""}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-TERP_MIGRATE$gen_curr_type_cf-multi-delete", "label": "批量删除", "permissionKey": "TSRM$COMM_2B_CURRENCY_TYPE-list_perm_ac_z_0_0_0_1_items_0"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-import", "name": "ImportButton", "props": {"addApprovalManageServiceProps": {"predictFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/gei/task/config/predict"}}, "deleteApprovalManageServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "downloadFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}, "downloadServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}}, "editApprovalManageServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "label": "导入", "permissionKey": "TSRM$COMM_2B_CURRENCY_TYPE-list_perm_ac_z_0_0_0_2", "predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}, "predictServiceProps": {"predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}}, "saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}, "saveSubServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}}}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"getUserInfoFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/portal/user/current"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "fields": [{"access": null, "alias": "id", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "id", "name": "ID", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "created<PERSON>y", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "created_by", "name": "创建人", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "ERP_GEN$gen_curr_type_cf", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "updatedBy", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "updated_by", "name": "更新人", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "ERP_GEN$gen_curr_type_cf", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "createdAt", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "created_at", "name": "创建时间", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "DATE", "intLength": null, "isSystemField": true, "length": null, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "updatedAt", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "updated_at", "name": "更新时间", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "DATE", "intLength": null, "isSystemField": true, "length": null, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "version", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "version", "name": "版本号", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "deleted", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "deleted", "name": "逻辑删除标识", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": false, "defaultValue": 0, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "currCode", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "curr_code", "name": "币别编码", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "curr_code", "comment": "币别编码", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "TEXT", "intLength": null, "isSystemField": false, "length": 32, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "currName", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "curr_name", "name": "名称", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "curr_name", "comment": "名称", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "TEXT", "intLength": null, "isSystemField": false, "length": 32, "relationMeta": null, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "currIsoName", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "curr_iso_name", "name": "ISO名称", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "curr_iso_name", "comment": "ISO名称", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "TEXT", "intLength": null, "isSystemField": false, "length": 32, "relationMeta": null, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "decimalPlace", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "decimal_place", "name": "小数位", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "decimal_place", "comment": "小数位", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "NUMBER", "intLength": 8, "isSystemField": false, "length": null, "relationMeta": null, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "symbol", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "symbol", "name": "币种符号", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "symbol", "comment": "币种符号", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "TEXT", "intLength": null, "isSystemField": false, "length": 16, "relationMeta": null, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "remark", "appId": 922, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "remark", "name": "备注", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "remark", "comment": "备注", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "TEXT", "intLength": null, "isSystemField": false, "length": 256, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 572, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}], "flow": {}, "label": "导出", "modelAlias": "ERP_GEN$gen_curr_type_cf", "permissionKey": "TSRM$COMM_2B_CURRENCY_TYPE-list_perm_ac_z_0_0_0_3", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "5wYykxIXur54EM1txggLH", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "6G5Grnh2xXfTc8EJh_ckC", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "currCode", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "币别编码", "name": "currCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "4APnIWtReR8jq9EzWumIK", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "币种名称", "name": "currName", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "4zVOJEyi1kOxj_isQUPjL", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "currCode", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "币别编码", "name": "currCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "名称", "name": "currName", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf", "context$": "$context", "modelAlias": "ERP_GEN$gen_curr_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_curr_type_cf"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_curr_type_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showFilterFields": true, "showScope": "all", "tableCondition": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"币种配置表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_curr_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "ERP_GEN$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_CURRENCY_TYPE-list_perm_ac_z_0_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_CURRENCY_TYPE-list_perm_ac_z_0_1_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_CURRENCY_TYPE-list_perm_ac_z_0_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-currCode", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "currCode", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "editable": false, "label": "币别编码", "name": "currCode", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-currName", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "editable": false, "label": "名称", "name": "currName", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-symbol", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "symbol", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "editable": false, "label": "币种符号", "name": "symbol", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-remark", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "remark", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "displayComponentProps": {"alinkConfig": {"enable": false}}, "displayComponentType": "Text", "editComponentType": "InputText", "editable": false, "label": "备注", "name": "remark", "type": "TEXT"}}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_CURRENCY_TYPE--iQKmA6gtxdHVuArT3JXR", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_CURRENCY_TYPE-govBqi7wSORTmDyx7Skbk", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "context$": "$context", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"币种配置表\")) : \"新建币种配置表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "vW9Yj2s7AL_bDEKFuJHIn", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "CXgTZik9JrAOtES14f5a8", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currCode", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "currCode", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "币别编码", "name": "currCode", "rules": [{"message": "请输入币别编码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currName", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "币种名称", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": false, "required": true}, "key": "gQKuzyunf6iH28lEMwemm", "operator": null, "valueRules": null}], "name": "currName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currIsoName", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "currIsoName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "ISO名称", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "VkYCCAIBnlfynYp6rZEWO", "operator": null, "valueRules": null}], "name": "currIsoName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-decimalPlace", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "decimalPlace", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": false, "label": "小数位", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "tJMdZOabu9mo_Rt8gdFA-", "operator": null, "valueRules": null}], "name": "decimalPlace", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-symbol", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "symbol", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "币种符号", "name": "symbol", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-remark", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "remark", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "displayComponentProps": {"alinkConfig": {"enable": false}}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "备注", "name": "remark", "rules": [{"message": "请输入备注", "required": true}], "type": "TEXT"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "modelAlias": "ERP_GEN$gen_curr_type_cf", "params$": "{ id: route.recordId }", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "modelAlias": "ERP_GEN$gen_curr_type_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "ERP_GEN$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_CURRENCY_TYPE-list_perm_ac_z_0_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_curr_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form"}, "type": "action"}}], "service": "ERP_GEN$SYS_MasterData_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_CURRENCY_TYPE-list_perm_ac_z_0_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "币种"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["复制", "选择更新人", "表格", "系统信息", "新建", "删除成功", "请输入版本号", "名称", "币别编码", "小数位", "备注", "批量删除", "请输入创建时间", "删除", "保存", "币种配置表详情", "请输入ID", "批量操作", "用户名", "ID", "创建人", "请输入更新时间", "保存成功!", "ISO名称", "新建币种配置表", "币种名称", "逻辑删除标识", "币种配置表", "主体信息", "请选择", "版本号", "更新时间", "币种符号", "更新人", "编辑", "请输入", "选择创建人", "请输入备注", "取消", "创建时间", "请输入币别编码", "请输入逻辑删除标识", "确认删除吗？"], "i18nScanPaths": ["COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-decimalPlace.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdAt.props.rules.0.message", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedBy.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-edit.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf.props.filterFields.1.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.8.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-updatedBy.props.componentProps.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf.props.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currIsoName.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.9.name", "5wYykxIXur54EM1txggLH.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.6.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-deleted.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-updatedBy.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-symbol.props.label", "4APnIWtReR8jq9EzWumIK.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-version.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currName.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch.props.items.0.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-copy.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdBy.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch.props.items.0.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedAt.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-id.props.rules.0.message", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currCode.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.12.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currCode.props.rules.0.message", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-id.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currName.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-decimalPlace.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-version.props.rules.0.message", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-action-save.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.1.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-currCode.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.4.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-symbol.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.7.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-delete.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-deleted.props.componentProps.placeholder", "4APnIWtReR8jq9EzWumIK.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-symbol.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.10.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdAt.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-remark.props.rules.0.message", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-action-cancel.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-createdBy.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-updatedAt.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.2.name", "6G5Grnh2xXfTc8EJh_ckC.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.3.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.11.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-remark.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.0.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf.props.filterFields.0.componentProps.placeholder", "6G5Grnh2xXfTc8EJh_ckC.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currIsoName.props.label", "5wYykxIXur54EM1txggLH.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf.props.filterFields.0.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-id.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-deleted.props.rules.0.message", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-createdAt.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-currCode.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-symbol.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export.props.fields.5.name", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedBy.props.componentProps.label", "@exp:COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page-title.props.title", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-remark.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs.props.items.1.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedAt.props.rules.0.message", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-remark.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currCode.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-currName.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-remark.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-currName.props.componentProps.placeholder", "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-new.props.label", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs.props.items.0.label", "@exp:COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-page-title.props.title", "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-version.props.componentProps.placeholder"]}, "key": "TSRM$COMM_2B_CURRENCY_TYPE-list", "permissionKey": "TSRM$COMM_2B_CURRENCY_TYPE-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-new", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch/items/COMMON_2B$COMM_2B_CURRENCY_TYPE-TERP_MIGRATE$gen_curr_type_cf-multi-delete", "label": "批量删除", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "ERP_GEN$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-import", "label": "导入", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-sub-model", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/template/download", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/config/predict", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}, {"key": "/api/trantor/workflow/v2/{param0}", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-export", "label": "导出", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/trantor/portal/user/current", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "5wYykxIXur54EM1txggLH", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf", "label": "表格", "type": "Table"}, {"key": "4zVOJEyi1kOxj_isQUPjL", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "6G5Grnh2xXfTc8EJh_ckC", "label": "币别编码", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf", "label": "表格", "type": "Table"}, {"key": "4zVOJEyi1kOxj_isQUPjL", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "4APnIWtReR8jq9EzWumIK", "label": "币种名称", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-list-TERP_MIGRATE$gen_curr_type_cf", "label": "表格", "type": "Table"}, {"key": "4zVOJEyi1kOxj_isQUPjL", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$SYS_MasterData_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-version", "label": "版本号", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currCode", "label": "币别编码", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currName", "label": "币种名称", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-currIsoName", "label": "ISO名称", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-decimalPlace", "label": "小数位", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-symbol", "label": "币种符号", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-field-remark", "label": "备注", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-_2U0o0-KJOg6EX-CYDlu7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-editView-TERP_MIGRATE$gen_curr_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-currCode", "label": "币别编码", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE--iQKmA6gtxdHVuArT3JXR", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-currName", "label": "名称", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE--iQKmA6gtxdHVuArT3JXR", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-symbol", "label": "币种符号", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE--iQKmA6gtxdHVuArT3JXR", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-field-remark", "label": "备注", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE--iQKmA6gtxdHVuArT3JXR", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-govBqi7wSORTmDyx7Skbk", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-govBqi7wSORTmDyx7Skbk", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-govBqi7wSORTmDyx7Skbk", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-TERP_MIGRATE$gen_curr_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_CURRENCY_TYPE-govBqi7wSORTmDyx7Skbk", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_CURRENCY_TYPE-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}