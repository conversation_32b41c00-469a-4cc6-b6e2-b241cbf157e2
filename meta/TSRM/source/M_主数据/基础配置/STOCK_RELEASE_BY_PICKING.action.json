{"access": "Private", "key": "TSRM$STOCK_RELEASE_BY_PICKING", "name": "领料释放库存", "props": {"bean": "StockAction", "desc": null, "groovyScript": null, "languageType": "Java", "method": "stockReleaseByPicking", "order": 10, "requestType": "io.terminus.tsrm.md.spi.model.stock.dto.InvRecordTrDTO", "responseType": "io.terminus.tsrm.md.spi.model.stock.dto.InvRecordTrDTO", "returnModel": null, "status": "enabled"}, "type": "Action"}