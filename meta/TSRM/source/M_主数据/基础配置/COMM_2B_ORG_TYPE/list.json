{"access": "Private", "key": "TSRM$COMM_2B_ORG_TYPE:list", "name": "list", "props": {"containerSelect": {"TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail": [{"field": "parentLimitBizTypeList", "selectFields": []}], "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form": [{"field": "parentLimitBizTypeList", "selectFields": []}], "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf": []}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "permissionKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list_perm_ac_z_0_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "fields": [{"alias": "id", "appId": 35339, "ext": false, "key": "id", "name": "ID", "props": {"autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "required": true, "unique": true}, "teamId": 1, "type": "DataStructField"}, {"alias": "created<PERSON>y", "appId": 35339, "ext": false, "key": "created_by", "name": "创建人", "props": {"autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "TERP_MIGRATE$org_biz_type_cf", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 1, "type": "DataStructField"}, {"alias": "updatedBy", "appId": 35339, "ext": false, "key": "updated_by", "name": "更新人", "props": {"autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "TERP_MIGRATE$org_biz_type_cf", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 1, "type": "DataStructField"}, {"alias": "createdAt", "appId": 35339, "ext": false, "key": "created_at", "name": "创建时间", "props": {"autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 1, "type": "DataStructField"}, {"alias": "updatedAt", "appId": 35339, "ext": false, "key": "updated_at", "name": "更新时间", "props": {"autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 1, "type": "DataStructField"}, {"alias": "version", "appId": 35339, "ext": false, "key": "version", "name": "版本号", "props": {"autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "required": true, "unique": true}, "teamId": 1, "type": "DataStructField"}, {"alias": "deleted", "appId": 35339, "ext": false, "key": "deleted", "name": "逻辑删除标识", "props": {"autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "required": true, "unique": true}, "teamId": 1, "type": "DataStructField"}, {"alias": "name", "appId": 35339, "ext": false, "key": "name", "name": "业务类型名称", "props": {"autoGenerated": false, "columnName": "name", "comment": "业务类型名称", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": true, "unique": false}, "teamId": 1, "type": "DataStructField"}, {"alias": "<PERSON><PERSON><PERSON>", "appId": 35339, "ext": false, "key": "model_key", "name": "模型key", "props": {"autoGenerated": false, "columnName": "model_key", "comment": "模型key", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": true, "unique": false}, "teamId": 1, "type": "DataStructField"}, {"alias": "parentLimitBizTypeList", "appId": 35339, "ext": false, "key": "parent_limit_biz_type_list", "name": "允许的下级类型", "props": {"autoGenerated": false, "columnName": "parent_limit_biz_type_list", "comment": "允许的下级类型", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "TERP_MIGRATE$org_biz_type_cf", "currentModelFieldAlias": "parentLimitBizTypeList", "linkModelAlias": "TERP_MIGRATE$org_biz_type_limit_link_cf", "linkModelFieldAlias": "bizTypeId", "relationKey": null, "relationModelAlias": "TERP_MIGRATE$org_biz_type_limit_link_cf", "relationModelKey": "TERP_MIGRATE$org_biz_type_limit_link_cf", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}, "teamId": 1, "type": "DataStructField"}, {"alias": "code", "appId": 35339, "ext": false, "key": "code", "name": "编码", "props": {"autoGenerated": false, "columnName": "code", "comment": "编码", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 32, "required": false, "unique": false}, "teamId": 1, "type": "DataStructField"}], "flow": {}, "getAliasFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor/struct-node/find-by-alias"}, "label": "导出", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "permissionKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list_perm_ac_z_0_0_0_1", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "type": "Widget"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-XfWDU6dOssbCVPGYYMXYi", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-ZVDht05ZwxEA0QHesRGn-", "name": "Field", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "编码", "name": "code", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-Yo0lOS2o6u2U0Z3-Uz73Z", "name": "Field", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "业务类型名称", "name": "name", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-dJYht_ac83oowkPKv-5rD", "name": "Field", "props": {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "模型key", "name": "<PERSON><PERSON><PERSON>", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "TSRM$COMM_2B_ORG_TYPE-_IXrnPyzI7UNZv2nHAiuy", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "业务类型名称", "name": "name", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "模型key", "name": "<PERSON><PERSON><PERSON>", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "编码", "name": "code", "type": "TEXT", "width": 146}], "flow": {"containerKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_biz_type_cf"}}], "serviceKey": "TERP_MIGRATE$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showScope": "all", "tableCondition": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"组织业务类型配置表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_biz_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TERP_MIGRATE$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list_perm_ac_z_0_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list_perm_ac_z_0_1_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list_perm_ac_z_0_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detail-TERP_MIGRATE$org_biz_type_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-code", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-name", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "editable": false, "label": "业务类型名称", "name": "name", "type": "TEXT"}}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-modelKey", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "editable": false, "label": "模型key", "name": "<PERSON><PERSON><PERSON>", "type": "TEXT"}}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-TS2pJy9Fkncu1q4ahNkGp", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-list-batch-actions", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-list-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-78QgKj_YE_IujebGlCxM6", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_biz_type_limit_link_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-6P2p21DKchtS2ypPGf-7X", "name": "Field", "props": {"componentProps": {"columns": ["name", "<PERSON><PERSON><PERSON>", "code"], "fieldAlias": "childBizTypeId", "label": "选择下级单元类型", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "parentModelAlias": "TERP_MIGRATE$org_biz_type_limit_link_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "hidden": false, "label": "下级单元类型", "name": "childBizTypeId", "type": "OBJECT"}, "type": "Widget"}], "key": "TSRM$COMM_2B_ORG_TYPE-Tjw5jhgKaAtLFosxf6hZD", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-list", "name": "Table", "props": {"enableSolution": false, "fieldName": "parentLimitBizTypeList", "flow": {"context$": "$context", "name": "parentLimitBizTypeList", "type": "RelationData"}, "label": "表格", "mode": "normal", "modelAlias": "TERP_MIGRATE$org_biz_type_limit_link_cf", "subTableEnabled": false}, "type": "Container"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-customDetailField", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "parentLimitBizTypeList"}, "type": "Meta"}], "key": "TSRM$COMM_2B_ORG_TYPE-hx7q0vra-26ZG63utmx9d", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-_khakpj1O9e_sX6fSwXzr", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "允许的下级类型"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "serviceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "Container"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"组织业务类型配置表\")) : \"新建组织业务类型配置表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "ZpfqYnAFxFJa9bYPizKbX", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "OH49WiaTKUhDYgBL5R7oK", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-code", "name": "FormField", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "c-G1LhrP0dSpw_4-PVpKG", "valueRules": null}], "name": "code", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-name", "name": "FormField", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "业务类型名称", "name": "name", "rules": [{"message": "请输入业务类型名称", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-modelKey", "name": "FormField", "props": {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "模型key", "name": "<PERSON><PERSON><PERSON>", "rules": [{"message": "请输入模型key", "required": true}], "type": "TEXT"}, "type": "Widget"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform-batchActions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "buttonType": "default", "confirmOn": "off", "disabled$": "mode === \"design\" ? undefined : $context.selectedKeys?.length === 0", "label": "删除", "onClick$": "() => $context.batchRemove?.($context.selectedKeys || [])", "permissionKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list_perm_ac_z_0_1_2_2_0_1_0_0_0_0", "type": "primary"}, "type": "Widget"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform-batchActions", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform-action-deleteLine", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "SystemAction", "name": "remove"}]}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list_perm_ac_z_0_1_2_2_0_1_0_0_1_0", "type": "text"}, "type": "Widget"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform-reordActions", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-ViWeHIfOmFiN6f1GGQyPP", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_biz_type_limit_link_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "ID必填", "required": true}], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv", "name": "Field", "props": {"componentProps": {"fieldAlias": "childBizTypeId", "label": "选择下级单元类型", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "parentModelAlias": "TERP_MIGRATE$org_biz_type_limit_link_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "业务类型名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "模型key", "name": "<PERSON><PERSON><PERSON>", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "编码", "name": "code", "required": false, "type": "TEXT", "width": 120}], "labelField": "name", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "下级单元类型", "name": "childBizTypeId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}], "key": "TSRM$COMM_2B_ORG_TYPE-waab8RZInpNzeBWHrwCto", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform", "name": "TableForm", "props": {"fieldName": "parentLimitBizTypeList", "fields": [], "hideDefaultDelete": true, "label": "表格表单", "modelAlias": "TERP_MIGRATE$org_biz_type_limit_link_cf", "subTableEnabled": false}}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-form-field-parentLimitBizTypeList", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "parentLimitBizTypeList"}, "type": "Meta"}], "key": "TSRM$COMM_2B_ORG_TYPE-A_i5xoP3YayNKlc8Y1uu9", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "允许的下级类型"}], "underline": true}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "params$": "{ id: route.recordId }", "serviceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "modelAlias": "TERP_MIGRATE$org_biz_type_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "TERP_MIGRATE$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list_perm_ac_z_0_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_biz_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form"}, "type": "action"}}], "service": "TERP_MIGRATE$SYS_MasterData_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list_perm_ac_z_0_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "组织业务类型"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["复制", "选择更新人", "表格", "系统信息", "新建", "删除成功", "请输入版本号", "请输入业务类型名称", "模型key", "请输入创建时间", "删除", "ID必填", "下级单元类型", "业务类型名称", "保存", "请输入ID", "组织业务类型配置表", "表格表单", "用户名", "ID", "创建人", "请输入更新时间", "保存成功!", "新建组织业务类型配置表", "逻辑删除标识", "编码", "主体信息", "请输入模型key", "请选择", "版本号", "组织业务类型配置表详情", "更新时间", "更新人", "允许的下级类型", "编辑", "请输入", "选择创建人", "选择下级单元类型", "取消", "创建时间", "请输入逻辑删除标识", "确认删除吗？"], "i18nScanPaths": ["TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs.props.items.1.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-code.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs.props.items.0.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-list.props.label", "TSRM$COMM_2B_ORG_TYPE-78QgKj_YE_IujebGlCxM6.props.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.9.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.7.name", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-modelKey.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-modelKey.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-name.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdBy.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.6.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-createdAt.props.label", "TSRM$COMM_2B_ORG_TYPE-XfWDU6dOssbCVPGYYMXYi.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-code.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-delete.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.6.name", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-modelKey.props.rules.0.message", "TSRM$COMM_2B_ORG_TYPE-78QgKj_YE_IujebGlCxM6.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.9.name", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.3.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.0.label", "TSRM$COMM_2B_ORG_TYPE-Yo0lOS2o6u2U0Z3-Uz73Z.props.label", "TSRM$COMM_2B_ORG_TYPE-dJYht_ac83oowkPKv-5rD.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs.props.items.2.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf.props.filterFields.1.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdBy.props.editComponentProps.fields.0.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.2.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-createdBy.props.componentProps.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf.props.filterFields.0.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedBy.props.editComponentProps.fields.0.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.4.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.5.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-copy.props.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.9.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedBy.props.componentProps.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.1.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs.props.items.1.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedBy.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-modelKey.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform-action-deleteLine.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.8.name", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.8.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdAt.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-name.props.rules.0.message", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-updatedAt.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.10.name", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.2.name", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdAt.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.5.name", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-deleted.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-action-save.props.label", "TSRM$COMM_2B_ORG_TYPE-ViWeHIfOmFiN6f1GGQyPP.props.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.1.componentProps.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.1.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf.props.filterFields.0.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.1.name", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-Yo0lOS2o6u2U0Z3-Uz73Z.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-name.props.label", "TSRM$COMM_2B_ORG_TYPE-ZVDht05ZwxEA0QHesRGn-.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedAt.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-modelKey.props.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.7.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-name.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.4.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.0.name", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdAt.props.rules.0.message", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-deleted.props.rules.0.message", "TSRM$COMM_2B_ORG_TYPE-XfWDU6dOssbCVPGYYMXYi.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedAt.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedBy.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-6P2p21DKchtS2ypPGf-7X.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-new.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-updatedBy.props.componentProps.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform-batchActions-delete.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs.props.items.0.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.3.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-deleted.props.label", "TSRM$COMM_2B_ORG_TYPE-ViWeHIfOmFiN6f1GGQyPP.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-id.props.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.8.componentProps.placeholder", "@exp:TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-page-title.props.title", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-action-cancel.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-id.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.componentProps.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-edit.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-id.props.rules.0.message", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-createdBy.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf.props.filterFields.2.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-code.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-version.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.4.name", "@exp:TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page-title.props.title", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform.props.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.6.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-ViWeHIfOmFiN6f1GGQyPP.props.rules.0.message", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdBy.props.componentProps.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-name.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export.props.fields.3.name", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-code.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-ZVDht05ZwxEA0QHesRGn-.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-version.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf.props.filterFields.2.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-updatedBy.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdBy.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-dJYht_ac83oowkPKv-5rD.props.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedAt.props.rules.0.message", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.2.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.7.componentProps.placeholder", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf.props.filterFields.1.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.5.label", "TSRM$COMM_2B_ORG_TYPE-6P2p21DKchtS2ypPGf-7X.props.componentProps.label", "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv.props.editComponentProps.fields.2.componentProps.label", "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-version.props.rules.0.message", "TSRM$COMM_2B_ORG_TYPE-6P2p21DKchtS2ypPGf-7X.props.label"]}, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list", "permissionKey": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list_perm_ac", "resources": [{"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf", "label": "表格", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-new", "label": "新建", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-export", "label": "导出", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/trantor/struct-node/find-by-alias", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-logs", "label": "日志", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf-toolbar-actions", "label": "按钮组", "type": "ToolbarActions"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-XfWDU6dOssbCVPGYYMXYi", "label": "ID", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_ORG_TYPE-_IXrnPyzI7UNZv2nHAiuy", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-ZVDht05ZwxEA0QHesRGn-", "label": "编码", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_ORG_TYPE-_IXrnPyzI7UNZv2nHAiuy", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-Yo0lOS2o6u2U0Z3-Uz73Z", "label": "业务类型名称", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_ORG_TYPE-_IXrnPyzI7UNZv2nHAiuy", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-dJYht_ac83oowkPKv-5rD", "label": "模型key", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-list-TERP_MIGRATE$org_biz_type_cf", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_ORG_TYPE-_IXrnPyzI7UNZv2nHAiuy", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-action-cancel", "label": "取消", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-action-save", "label": "保存", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_MasterData_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-delete", "label": "删除", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-copy", "label": "复制", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions-edit", "label": "编辑", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detail-TERP_MIGRATE$org_biz_type_cf-logs", "label": "日志", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-id", "label": "ID", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdBy", "label": "创建人", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedBy", "label": "更新人", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-createdAt", "label": "创建时间", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-updatedAt", "label": "更新时间", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-version", "label": "版本号", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-code", "label": "编码", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-name", "label": "业务类型名称", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-field-modelKey", "label": "模型key", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-D85n48CUyaPZLT_Qpq3iX", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform", "label": "表格表单", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-A_i5xoP3YayNKlc8Y1uu9", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-form-field-parentLimitBizTypeList", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-code", "label": "编码", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-TS2pJy9Fkncu1q4ahNkGp", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-name", "label": "业务类型名称", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-TS2pJy9Fkncu1q4ahNkGp", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-field-modelKey", "label": "模型key", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-TS2pJy9Fkncu1q4ahNkGp", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-list", "label": "表格", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-hx7q0vra-26ZG63utmx9d", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-_khakpj1O9e_sX6fSwXzr", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-_khakpj1O9e_sX6fSwXzr", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-_khakpj1O9e_sX6fSwXzr", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-_khakpj1O9e_sX6fSwXzr", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform-batchActions-delete", "label": "删除", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-A_i5xoP3YayNKlc8Y1uu9", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-form-field-parentLimitBizTypeList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform-batchActions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform-action-deleteLine", "label": "删除", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-A_i5xoP3YayNKlc8Y1uu9", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-form-field-parentLimitBizTypeList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform-reordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-ViWeHIfOmFiN6f1GGQyPP", "label": "ID", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-A_i5xoP3YayNKlc8Y1uu9", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-form-field-parentLimitBizTypeList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$COMM_2B_ORG_TYPE-waab8RZInpNzeBWHrwCto", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-IztgbmrqU-YEgZp_FTbjv", "label": "下级单元类型", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-A_i5xoP3YayNKlc8Y1uu9", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-form-field-parentLimitBizTypeList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-editView-TERP_MIGRATE$org_biz_type_limit_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$COMM_2B_ORG_TYPE-waab8RZInpNzeBWHrwCto", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-78QgKj_YE_IujebGlCxM6", "label": "ID", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-hx7q0vra-26ZG63utmx9d", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-list", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_ORG_TYPE-Tjw5jhgKaAtLFosxf6hZD", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$COMM_2B_ORG_TYPE-6P2p21DKchtS2ypPGf-7X", "label": "下级单元类型", "path": [{"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-view", "label": "视图", "type": "View"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORG_TYPE-hx7q0vra-26ZG63utmx9d", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$COMM_2B_ORG_TYPE-COMMON_2B$COMM_2B_ORG_TYPE-detailView-TERP_MIGRATE$org_biz_type_limit_link_cf-list", "label": "表格", "type": "Table"}, {"key": "TSRM$COMM_2B_ORG_TYPE-Tjw5jhgKaAtLFosxf6hZD", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_biz_type_cf"}, "type": "SystemService"}], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}