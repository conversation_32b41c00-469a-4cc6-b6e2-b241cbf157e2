{"access": "Private", "key": "TSRM$TESTAction", "name": "保存协议合同完整性检查", "props": {"bean": "PurchaserTestAction$$EnhancerBySpringCGLIB$$5ed5d51b", "desc": null, "groovyScript": null, "languageType": "Java", "method": "test", "order": 10, "requestType": "io.terminus.erp.eq.purchase.api.dto.TestDTO", "responseType": "io.terminus.erp.eq.purchase.api.dto.TestDTO", "returnModel": null, "status": "enabled"}, "type": "Action"}