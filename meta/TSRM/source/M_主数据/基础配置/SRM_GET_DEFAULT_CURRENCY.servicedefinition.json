{"access": "Public", "description": "{}", "key": "TSRM$SRM_GET_DEFAULT_CURRENCY", "name": "SRM_获取默认货币", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"id": null, "key": "node_1hkqfgqcm8", "name": "开始", "nextNodeKey": "node_1hkqfh67g10", "props": {"desc": null, "globalVariable": null, "input": null, "name": null, "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_curr_type_cf", "modelKey": "ERP_GEN$gen_curr_type_cf", "modelName": null}, "relation": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"id": null, "key": "node_1hkqfh67g10", "name": "查询数据", "nextNodeKey": "node_1hkqfko7j11", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "271hb1HYhMQjizg8to7hH", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "currCode", "fieldName": "币别编码", "fieldType": null, "modelAlias": "ERP_GEN$gen_curr_type_cf", "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "ERP_GEN$gen_curr_type_cf", "relatedModel": null, "valueKey": "currCode", "valueName": "币别编码"}]}, "operator": "EQ", "rightValue": {"constValue": "CNY", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "6aHsP6lsDUbC-UcXlcfdm", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "SdFb_iwjgs0b4-70e288t", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "desensitized": true, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_curr_type_cf", "modelKey": "ERP_GEN$gen_curr_type_cf", "modelName": "币种配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"id": null, "key": "node_1hkqfko7j11", "name": "赋值", "nextNodeKey": "node_1hkqfgqcm9", "props": {"assignments": [{"field": {"constValue": null, "fieldPaths": [{"fieldKey": "OUTPUT", "fieldName": "服务出参", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "data", "fieldName": "data", "fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_curr_type_cf", "modelKey": "ERP_GEN$gen_curr_type_cf", "modelName": null}}], "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_curr_type_cf", "modelKey": "ERP_GEN$gen_curr_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "id": "1hkqfkpen12", "operator": "EQ", "value": {"constValue": null, "fieldPaths": [{"fieldKey": "GLOBAL", "fieldName": "全局变量", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "NODE_OUTPUT_node_1hkqfh67g10", "fieldName": "[查询数据]节点.output", "fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_curr_type_cf", "modelKey": "ERP_GEN$gen_curr_type_cf", "modelName": "币种配置表"}}], "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_curr_type_cf", "modelKey": "ERP_GEN$gen_curr_type_cf", "modelName": "币种配置表"}, "valueKey": "NODE_OUTPUT_node_1hkqfh67g10", "valueName": "[查询数据]节点.output"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "type": "AssignNode"}, {"id": null, "key": "node_1hkqfgqcm9", "name": "结束", "props": {"desc": null, "name": null, "type": "EndProperties"}, "type": "EndNode"}], "headNodeKeys": ["node_1hkqfgqcm8"], "id": null, "input": null, "key": "TSRM$SRM_GET_DEFAULT_CURRENCY", "name": "SRM_获取默认货币", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_curr_type_cf", "modelKey": "ERP_GEN$gen_curr_type_cf", "modelName": null}, "relation": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "desc": null, "name": null, "permissionKey": "TSRM$SRM_GET_DEFAULT_CURRENCY_perm_ac", "schedulerJob": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}