{"access": "Private", "key": "TSRM$COMM_2B_SUB_BANK:list", "name": "list", "props": {"containerSelect": {"COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail": [{"field": "subBankCode", "selectFields": null}, {"field": "subBankName", "selectFields": null}, {"field": "genBankCfId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "bankName", "selectFields": null}]}, {"field": "genAddrTypeCfId", "selectFields": null}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}], "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form": [{"field": "version", "selectFields": null}, {"field": "subBankCode", "selectFields": null}, {"field": "subBankName", "selectFields": null}, {"field": "genBankCfId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "bankName", "selectFields": null}]}, {"field": "genAddrTypeCfId", "selectFields": null}], "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf": [{"field": "subBankCode", "selectFields": null}, {"field": "subBankName", "selectFields": null}]}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "permissionKey": "TSRM$COMM_2B_SUB_BANK-list_perm_ac_z_0_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_sub_bank_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf"}, {"action": "PageJump", "target": "list"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "COMMON_2B$COMM_2B_SUB_BANK-TERP_MIGRATE$gen_sub_bank_cf-multi-delete", "label": "批量删除", "permissionKey": "TSRM$COMM_2B_SUB_BANK-list_perm_ac_z_0_0_0_1_items_0"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-import", "name": "ImportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "downloadFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}, "downloadServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}}, "isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "label": "导入", "permissionKey": "TSRM$COMM_2B_SUB_BANK-list_perm_ac_z_0_0_0_2", "predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}, "predictServiceProps": {"predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}}, "saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}, "saveSubServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}}}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"getUserInfoFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/portal/user/current"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "label": "导出", "permissionKey": "TSRM$COMM_2B_SUB_BANK-list_perm_ac_z_0_0_0_3", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "jraXrJFp2_7lSaZbu4bKh", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "uCIjqhb8i1rBWElek0PAt", "name": "Field", "props": {"componentProps": {"fieldAlias": "subBankCode", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "hidden": false, "label": "银行支行编码", "name": "subBankCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "jCckGIQtFXfBWl7RQPjeJ", "name": "Field", "props": {"componentProps": {"fieldAlias": "subBankName", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "hidden": false, "label": "银行支行名称", "name": "subBankName", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "qzlYXKwHmvdzIMR1cbBba", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "subBankCode", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "hidden": false, "label": "银行支行编码", "name": "subBankCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "subBankName", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "hidden": false, "label": "银行支行名称", "name": "subBankName", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "context$": "$context", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_sub_bank_cf"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showFilterFields": true, "showScope": "all", "tableCondition": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"银行支行表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_sub_bank_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "ERP_GEN$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_SUB_BANK-list_perm_ac_z_0_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_SUB_BANK-list_perm_ac_z_0_1_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_SUB_BANK-list_perm_ac_z_0_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detail-TERP_MIGRATE$gen_sub_bank_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankCode", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "subBankCode", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "editable": false, "label": "银行支行编码", "name": "subBankCode", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankName", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "subBankName", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "editable": false, "label": "银行支行名称", "name": "subBankName", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "genBankCfId", "label": "选择所属银行", "labelField": "bankCode", "modelAlias": "ERP_GEN$gen_bank_cf", "parentModelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": ["bankName"], "modelAlias": "ERP_GEN$gen_bank_cf"}, "displayComponentType": "RelationShow", "editable": false, "label": "所属银行", "name": "genBankCfId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "genAddrTypeCfId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "地址", "name": "genAddrTypeCfId", "type": "OBJECT"}}], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_SUB_BANK-rrflPA2fs8lEFT2KNCM3w", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_SUB_BANK-LhX1tGSaYExZlFUvWiYV0", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "context$": "$context", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_sub_bank_cf"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"银行支行表\")) : \"新建银行支行表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "1JqRXPiMt5MAUrnASEWux", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "MZC3IDR6A4an9WJ6PkXqE", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankCode", "name": "FormField", "props": {"componentProps": {"fieldAlias": "subBankCode", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "银行支行编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "IX-uIHj5lelbo6Sw9CYqS", "valueRules": null}], "name": "subBankCode", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "subBankName", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "银行支行名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "Q5gp5jxrXFyaxaQLEUCdC", "valueRules": null}], "name": "subBankName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "genBankCfId", "label": "选择所属银行", "labelField": "bankCode", "modelAlias": "ERP_GEN$gen_bank_cf", "parentModelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": ["bankName"], "modelAlias": "ERP_GEN$gen_bank_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "bankCode", "modelAlias": "ERP_GEN$gen_bank_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "银行编码", "name": "bankCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "bankName", "modelAlias": "ERP_GEN$gen_bank_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "银行名称", "name": "bankName", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "bankMneCode", "modelAlias": "ERP_GEN$gen_bank_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "银行助记码", "name": "bankMneCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "bankSwiftCode", "modelAlias": "ERP_GEN$gen_bank_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "银行国际代码", "name": "bankSwiftCode", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": ["bankName"], "modelAlias": "ERP_GEN$gen_bank_cf", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "所属银行", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "w-g3Q1JNiH3BlJeZFWIp8", "valueRules": null}], "name": "genBankCfId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "genAddrTypeCfId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "ERP_GEN$gen_sub_bank_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "addrCode", "modelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "地址库编码", "name": "addrCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "地址库名称", "name": "addrName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "postCode", "modelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "邮政编码", "name": "postCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["counCode", "coun<PERSON><PERSON>", "languageId", "currId", "timezoneId", "defaultRelv"], "fieldAlias": "counId", "label": "选择国家", "labelField": "counCode", "modelAlias": "ERP_GEN$gen_coun_type_cf", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "isRelationColumn": true, "label": "国家", "name": "counId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请选择"}, "hidden": false, "isRelationColumn": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"columns": ["addrCode", "addrName", "postCode", "counId", "<PERSON><PERSON><PERSON><PERSON>", "addrParentId", "plnSoaRegconsMdId"], "fieldAlias": "addrParentId", "label": "选择父节点", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "isRelationColumn": true, "label": "父节点", "name": "addrParentId", "required": false, "type": "SELFRELATION", "width": 120}, {"componentProps": {"columns": ["productCate", "productCode", "supplyCode", "addrAccCode", "supplyMethod", "supplyType", "areaId"], "fieldAlias": "plnSoaRegconsMdId", "label": "选择plnSoaRegconsMdId", "labelField": "productCate", "modelAlias": "ERP_PLN$pln_soa_regcons_md", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PLN$SYS_FindDataByIdService", "searchServiceKey": "ERP_PLN$SYS_PagingDataService"}}, "hidden": false, "isRelationColumn": true, "label": "plnSoaRegconsMdId", "name": "plnSoaRegconsMdId", "required": false, "type": "OBJECT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "地址", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "uRKjMPkjOel0CLFVOC_cs", "valueRules": null}], "name": "genAddrTypeCfId", "rules": [], "type": "OBJECT"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "params$": "{ id: route.recordId }", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "ERP_GEN$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "ERP_GEN$gen_sub_bank_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_SUB_BANK-list_perm_ac_z_0_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_sub_bank_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form"}, "type": "action"}}], "service": "ERP_GEN$SYS_MasterData_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_SUB_BANK-list_perm_ac_z_0_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_SUB_BANK-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_SUB_BANK-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "银行支行"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["银行国际代码", "请输入版本号", "银行编码", "是否叶子节点", "选择plnSoaRegconsMdId", "保存", "父节点", "请输入ID", "银行支行表", "用户名", "地址", "ID", "创建人", "请输入更新时间", "保存成功!", "所属银行", "逻辑删除标识", "请选择", "版本号", "选择所属银行", "更新时间", "选择国家", "银行支行表详情", "编辑", "选择创建人", "银行助记码", "请输入逻辑删除标识", "确认删除吗？", "复制", "选择更新人", "银行支行编码", "表格", "系统信息", "新建", "删除成功", "plnSoaRegconsMdId", "选择父节点", "批量删除", "请输入创建时间", "删除", "地址库编码", "国家", "批量操作", "银行名称", "新建银行支行表", "主体信息", "选择地址", "邮政编码", "银行支行名称", "更新人", "请输入", "取消", "创建时间", "地址库名称"], "i18nScanPaths": ["COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf.props.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedBy.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankName.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-new.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf.props.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-createdAt.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.3.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-id.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-version.props.componentProps.placeholder", "uCIjqhb8i1rBWElek0PAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.componentProps.placeholder", "@exp:COMMON_2B$COMM_2B_SUB_BANK-detailView-page-title.props.title", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.4.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.6.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-delete.props.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdBy.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs.props.items.1.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-action-save.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "jraXrJFp2_7lSaZbu4bKh.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "jCckGIQtFXfBWl7RQPjeJ.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-action-cancel.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.editComponentProps.fields.3.label", "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankName.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-deleted.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-deleted.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankName.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankCode.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedBy.props.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-updatedBy.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdAt.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankCode.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.3.label", "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-id.props.label", "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf.props.filterFields.0.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankCode.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedAt.props.rules.0.message", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.6.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch.props.items.0.actionConfig.endLogicOtherConfig.2.message", "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-copy.props.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "jCckGIQtFXfBWl7RQPjeJ.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf.props.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-id.props.rules.0.message", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.5.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-createdBy.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedAt.props.label", "@exp:COMMON_2B$COMM_2B_SUB_BANK-editView-page-title.props.title", "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankName.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch.props.items.0.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.4.componentProps.placeholder", "jraXrJFp2_7lSaZbu4bKh.props.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-edit.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.5.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-version.props.rules.0.message", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-deleted.props.rules.0.message", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.5.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.6.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-updatedBy.props.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdAt.props.rules.0.message", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-version.props.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId.props.componentProps.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId.props.label", "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-updatedAt.props.label", "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf.props.filterFields.1.label", "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankCode.props.componentProps.placeholder", "uCIjqhb8i1rBWElek0PAt.props.label"]}, "key": "TSRM$COMM_2B_SUB_BANK-list", "permissionKey": "TSRM$COMM_2B_SUB_BANK-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_sub_bank_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-new", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch/items/COMMON_2B$COMM_2B_SUB_BANK-TERP_MIGRATE$gen_sub_bank_cf-multi-delete", "label": "批量删除", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "ERP_GEN$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_sub_bank_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-import", "label": "导入", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-sub-model", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/template/download", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-export", "label": "导出", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/trantor/portal/user/current", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf-toolbar-actions", "label": "按钮组", "type": "ToolbarActions"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "jraXrJFp2_7lSaZbu4bKh", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "label": "表格", "type": "Table"}, {"key": "qzlYXKwHmvdzIMR1cbBba", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "uCIjqhb8i1rBWElek0PAt", "label": "银行支行编码", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "label": "表格", "type": "Table"}, {"key": "qzlYXKwHmvdzIMR1cbBba", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "jCckGIQtFXfBWl7RQPjeJ", "label": "银行支行名称", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-list-TERP_MIGRATE$gen_sub_bank_cf", "label": "表格", "type": "Table"}, {"key": "qzlYXKwHmvdzIMR1cbBba", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_sub_bank_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_sub_bank_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_sub_bank_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$SYS_MasterData_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_sub_bank_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_sub_bank_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detail-TERP_MIGRATE$gen_sub_bank_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-version", "label": "版本号", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankCode", "label": "银行支行编码", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankName", "label": "银行支行名称", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId", "label": "所属银行", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_bank_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_bank_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId", "label": "地址", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-m2_-ej143fczhukGwaUj4", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-editView-TERP_MIGRATE$gen_sub_bank_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_PLN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_soa_regcons_md"}, "type": "SystemService"}, {"key": "ERP_PLN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_soa_regcons_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankCode", "label": "银行支行编码", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-rrflPA2fs8lEFT2KNCM3w", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-subBankName", "label": "银行支行名称", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-rrflPA2fs8lEFT2KNCM3w", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-genBankCfId", "label": "所属银行", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-rrflPA2fs8lEFT2KNCM3w", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_bank_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-field-genAddrTypeCfId", "label": "地址", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-rrflPA2fs8lEFT2KNCM3w", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-LhX1tGSaYExZlFUvWiYV0", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-LhX1tGSaYExZlFUvWiYV0", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-LhX1tGSaYExZlFUvWiYV0", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_SUB_BANK-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-TERP_MIGRATE$gen_sub_bank_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_SUB_BANK-LhX1tGSaYExZlFUvWiYV0", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_SUB_BANK-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}