{"access": "Private", "key": "TSRM$COMM_2B_UOM_CONVERTOR:edit", "name": "edit", "props": {"containerSelect": {"COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf": [{"field": "version", "selectFields": null}, {"field": "unitId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "baseUnitFactor", "selectFields": null}, {"field": "symbol", "selectFields": null}, {"field": "targetUnitId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "targetUnitFactor", "selectFields": null}]}, "content": {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑单位转换数据\" : \"创建单位转换数据\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "UGtYO_sucQLaeecc8eQ-x", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "GQxkkCyqe7FQyNQ9ub36R", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "unitId", "label": "选择基础单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "uomCode", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "计量单位编码", "name": "uomCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "计量单位名称", "name": "uomDesc", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "uomType", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请选择"}, "hidden": false, "isRelationColumn": true, "label": "计量单位类型编码", "name": "uomType", "required": true, "type": "SELECT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "uomCode", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "计量单位编码", "name": "uomCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "计量单位名称", "name": "uomDesc", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "基础单位", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "vudLfM3VA45B2R2wuMhug", "valueRules": null}], "name": "unitId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-baseUnitFactor", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "baseUnitFactor", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": false, "initialValue": null, "label": "基本单位系数", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "z_eOMNbS81d9WYCP4PZjZ", "valueRules": null}], "name": "baseUnitFactor", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-symbol", "name": "FormField", "props": {"componentProps": {"defaultValue": "=", "fieldAlias": "symbol", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": "=", "label": "运算符号", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "KRakX8Euv-NZiQY-UyCm7", "valueRules": null}], "name": "symbol", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "targetUnitId", "label": "选择目标单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "uomCode", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "计量单位编码", "name": "uomCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "计量单位名称", "name": "uomDesc", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "uomType", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请选择"}, "hidden": false, "isRelationColumn": true, "label": "计量单位类型编码", "name": "uomType", "required": true, "type": "SELECT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "uomCode", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "计量单位编码", "name": "uomCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "计量单位名称", "name": "uomDesc", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "目标单位", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "BlFXeUVDlstjY_oudgych", "valueRules": null}], "name": "targetUnitId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitFactor", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "targetUnitFactor", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": false, "initialValue": null, "label": "目标单位系数", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "AxpmEMYWuy7EaD-Ou6cTJ", "valueRules": null}], "name": "targetUnitFactor", "rules": [], "type": "NUMBER"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "name": "FormGroupItem", "props": {"showSplit": true, "title": false}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "params$": "{ id: route.recordId }", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "ERP_GEN$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "modelAlias": "ERP_GEN$gen_uom_formula_type_cf"}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-edit_perm_ac_z_2_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_uom_formula_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf"}, "type": "action"}}], "service": "ERP_GEN$SYS_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "保存成功！"}, {"action": "OpenView", "openViewConfig": {"page": {"key": "TSRM$COMM_2B_UOM_CONVERTOR-list", "name": "list", "type": "View"}, "type": "NewPage"}}, {"action": "RefreshTab", "target": "previous"}, {"action": "Close", "target": ["ROOT"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-edit_perm_ac_z_2_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "i18nConfig": {"i18nKeySet": ["选择更新人", "目标单位系数", "请输入版本号", "计量单位名称", "请输入创建时间", "选择基础单位", "编辑单位转换数据", "保存", "目标单位", "请输入ID", "基础单位", "选择目标单位", "用户名", "ID", "创建人", "请输入更新时间", "逻辑删除标识", "计量单位类型编码", "运算符号", "请选择", "版本号", "基本单位系数", "更新时间", "更新人", "请输入", "选择创建人", "取消", "创建时间", "创建单位转换数据", "请输入逻辑删除标识", "计量单位编码", "保存成功！"], "i18nScanPaths": ["COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.componentProps.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-deleted.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedBy.props.componentProps.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedAt.props.rules.0.message", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-id.props.rules.0.message", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.editComponentProps.filterFields.1.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.editComponentProps.filterFields.1.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitFactor.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-version.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-baseUnitFactor.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-symbol.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-baseUnitFactor.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-id.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-deleted.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-version.props.rules.0.message", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdAt.props.rules.0.message", "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-footer-cancel.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-id.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-footer-save.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.editComponentProps.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-symbol.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.editComponentProps.fields.2.label", "@exp:COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-title.props.title", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdBy.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.componentProps.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-version.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdAt.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-footer-save.props.actionConfig.endLogicOtherConfig.0.message", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitFactor.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedAt.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedBy.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-deleted.props.rules.0.message", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId.props.editComponentProps.filterFields.0.componentProps.placeholder"]}, "key": "TSRM$COMM_2B_UOM_CONVERTOR-edit", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-edit_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_formula_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_formula_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-footer-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-footer-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [{"key": "ERP_GEN$SYS_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_formula_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-version", "label": "版本号", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-deleted", "label": "逻辑删除标识", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-unitId", "label": "基础单位", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-baseUnitFactor", "label": "基本单位系数", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-symbol", "label": "运算符号", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitId", "label": "目标单位", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf-for-widget-targetUnitFactor", "label": "目标单位系数", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-total-config-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "title": "edit", "type": "FORM"}, "type": "View"}