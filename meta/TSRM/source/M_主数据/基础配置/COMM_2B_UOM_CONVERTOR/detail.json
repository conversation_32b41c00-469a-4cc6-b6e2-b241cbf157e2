{"access": "Private", "key": "TSRM$COMM_2B_UOM_CONVERTOR:detail", "name": "detail", "props": {"containerSelect": {"COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail": [{"field": "unitId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "baseUnitFactor", "selectFields": null}, {"field": "symbol", "selectFields": null}, {"field": "targetUnitId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "targetUnitFactor", "selectFields": null}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}]}, "content": {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "单位转换数据详情", "useExpression": false}, "type": "Meta"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-header-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { copyId: route.recordId } })"}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-detail_perm_ac_z_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-header-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-detail_perm_ac_z_1_1", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-TERP_MIGRATE$gen_uom_formula_type_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-unitId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "unitId", "label": "选择基础单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "displayComponentType": "RelationShow", "editable": false, "label": "基础单位", "name": "unitId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-baseUnitFactor", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "baseUnitFactor", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入", "precision": null}, "editable": false, "label": "基本单位系数", "name": "baseUnitFactor", "type": "NUMBER"}}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-symbol", "name": "DetailField", "props": {"componentProps": {"defaultValue": "=", "fieldAlias": "symbol", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "运算符号", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false}, "key": "ynwcZITM1IS_uygJ_0x0Z", "valueRules": null}], "name": "symbol", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-targetUnitId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "targetUnitId", "label": "选择目标单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "displayComponentType": "RelationShow", "editable": false, "label": "目标单位", "name": "targetUnitId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-targetUnitFactor", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "targetUnitFactor", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入", "precision": null}, "editable": false, "label": "目标单位系数", "name": "targetUnitFactor", "type": "NUMBER"}}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout", "name": "DetailGroupItem", "props": {"title": ""}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail", "name": "DetailGroupItem", "props": {"title": ""}, "type": "Layout"}], "key": "TSRM$COMM_2B_UOM_CONVERTOR-IkVmenBYgyGzlb9orMJWR", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2-Tabs", "name": "Tabs", "props": {"items": [{"label": "系统信息"}]}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2", "name": "DetailGroupItem", "props": {"showSplit": true, "title": ""}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_uom_formula_type_cf"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["复制", "目标单位系数", "选择更新人", "详情组标题", "系统信息", "运算符号", "请选择", "基本单位系数", "选择基础单位", "更新时间", "单位转换数据详情", "目标单位", "更新人", "编辑", "基础单位", "请输入", "选择创建人", "选择目标单位", "创建时间", "创建人"], "i18nScanPaths": ["COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout.props.title", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-symbol.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-targetUnitFactor.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2.props.title", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-unitId.props.componentProps.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-targetUnitFactor.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-symbol.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail.props.title", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-baseUnitFactor.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-unitId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-unitId.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-header-edit.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt.props.label", "@exp:COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-title.props.title", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-baseUnitFactor.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-header-copy.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2-Tabs.props.items.0.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy.props.componentProps.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-targetUnitId.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-targetUnitId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-targetUnitId.props.componentProps.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt.props.componentProps.placeholder"]}, "key": "TSRM$COMM_2B_UOM_CONVERTOR-detail", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-detail_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_formula_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-header-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-header-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-TERP_MIGRATE$gen_uom_formula_type_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-unitId", "label": "基础单位", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-baseUnitFactor", "label": "基本单位系数", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-symbol", "label": "运算符号", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-targetUnitId", "label": "目标单位", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-For-DetailField-targetUnitFactor", "label": "目标单位系数", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2-Tabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_UOM_CONVERTOR-IkVmenBYgyGzlb9orMJWR", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2-Tabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_UOM_CONVERTOR-IkVmenBYgyGzlb9orMJWR", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2-Tabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_UOM_CONVERTOR-IkVmenBYgyGzlb9orMJWR", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detailView-detail-Layout-2-Tabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_UOM_CONVERTOR-IkVmenBYgyGzlb9orMJWR", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-detail-detail-group-item-3-tabs-detail", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "detail", "type": "DETAIL"}, "type": "View"}