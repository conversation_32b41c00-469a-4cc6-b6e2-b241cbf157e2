{"access": "Private", "key": "TSRM$COMM_2B_UOM_CONVERTOR:list", "name": "list", "props": {"conditionGroups": {"K7QkKnRPcZFYQ5LrLWf4C": {"conditions": [], "id": "K7QkKnRPcZFYQ5LrLWf4C", "logicOperator": "OR", "type": "ConditionGroup"}}, "containerSelect": {"COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf": [{"field": "unitId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "baseUnitFactor", "selectFields": null}, {"field": "targetUnitId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "targetUnitFactor", "selectFields": null}]}, "content": {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "label": "新建", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-list_perm_ac_z_2_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否删除选中单据？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_uom_formula_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}}], "service": "ERP_GEN$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ""}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "disabled$": "mode === \"design\" ? false : $context.selectedKeys?.length === 0", "isMultiple": true, "label": "删除", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-list_perm_ac_z_2_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1-button-3", "name": "ExportButton", "props": {"label": "导出", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-list_perm_ac_z_2_0_2", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "show"}]}, "buttonType": "default", "confirmOn": "off", "label": "查看", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-list_perm_ac_z_2_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-list_perm_ac_z_2_1_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-3", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_uom_formula_type_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "record?.id", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_GEN$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf"]}, {"action": "Message", "level": "success", "message": "删除成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-list_perm_ac_z_2_1_2", "type": "default"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list-TERP_MIGRATE$gen_uom_formula_type_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-toolbar-actions-1", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-EXsrnn9u6ZY58nLoMnbmt", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep", "name": "Field", "props": {"componentProps": {"fieldAlias": "unitId", "label": "选择基础单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "uomType", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请选择"}, "editComponentProps": {}, "editComponentType": "Select", "label": "计量单位类型编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "JCNCSAyIGqH3d38YDQYp8", "trigger": "auto", "valueRules": null}], "name": "uomType", "type": "SELECT"}, {"componentProps": {"fieldAlias": "uomCode", "parentModelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "计量单位编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "EeURhV7qfjAkH6p6LaMQd", "trigger": "auto", "valueRules": null}], "name": "uomCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "uomDesc", "parentModelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "计量单位名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "MBTNysgAypLt-AMBS_Sbt", "trigger": "auto", "valueRules": null}], "name": "uomDesc", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "基础单位", "name": "unitId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-GJRetyecJHk3tpvIlHxsa", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "baseUnitFactor", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "label": "基本单位系数", "name": "baseUnitFactor", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5", "name": "Field", "props": {"componentProps": {"fieldAlias": "targetUnitId", "label": "选择目标单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "uomType", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请选择"}, "editComponentProps": {}, "editComponentType": "Select", "label": "计量单位类型编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "Qqs-pnIJOZjwpW6GmRBfn", "trigger": "auto", "valueRules": null}], "name": "uomType", "type": "SELECT"}, {"componentProps": {"fieldAlias": "uomCode", "parentModelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "计量单位编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "Jz7Dug1sX6mY7RmQXIe_c", "trigger": "auto", "valueRules": null}], "name": "uomCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "uomDesc", "parentModelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "计量单位名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "uTIYeYu7QWqEgCjH9IML3", "trigger": "auto", "valueRules": null}], "name": "uomDesc", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "目标单位", "name": "targetUnitId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-aVBaYpNhGIE-ZeVbiFpBC", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "targetUnitFactor", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "label": "目标单位系数", "name": "targetUnitFactor", "type": "NUMBER", "width": 144}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-p7NhOoC8nlrCoWNCymrni", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "editComponentProps": {"fields": [], "labelField": []}, "editComponentType": "InputText", "filterType": "fuzzy", "hidden": false, "label": "基础计量单位名称", "name": "unitId.uomDesc", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "editComponentProps": {"fields": [], "labelField": []}, "editComponentType": "InputText", "filterType": "fuzzy", "hidden": false, "label": "目标计量单位名称", "name": "targetUnitId.uomDesc", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "context$": "$context", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_uom_formula_type_cf"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "normal", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "showConfigure": false, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [], "id": "K7QkKnRPcZFYQ5LrLWf4C", "logicOperator": "OR", "type": "ConditionGroup"}, "toolbar": {"search": false}}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "name": "Page", "props": {"params": [], "showFooter": false, "showHeader": false}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["目标单位系数", "表格", "新建", "删除成功", "计量单位类型编码", "请选择", "基本单位系数", "目标计量单位名称", "查看", "是否删除选中单据？", "计量单位名称", "选择基础单位", "删除", "基础计量单位名称", "删除成功!", "目标单位", "编辑", "基础单位", "请输入", "选择目标单位", "ID", "计量单位编码", "确认删除吗？"], "i18nScanPaths": ["COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-1.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-aVBaYpNhGIE-ZeVbiFpBC.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-2.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-3.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep.props.componentProps.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1-button-2.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf.props.filterFields.1.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-GJRetyecJHk3tpvIlHxsa.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5.props.componentProps.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf.props.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-3.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1-button-2.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1-button-1.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-aVBaYpNhGIE-ZeVbiFpBC.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1-button-2.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf.props.filterFields.0.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-EXsrnn9u6ZY58nLoMnbmt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-EXsrnn9u6ZY58nLoMnbmt.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-GJRetyecJHk3tpvIlHxsa.props.label", "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf.props.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-3.props.actionConfig.beforeLogicConfig.0.text"]}, "key": "TSRM$COMM_2B_UOM_CONVERTOR-list", "permissionKey": "TSRM$COMM_2B_UOM_CONVERTOR-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_formula_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1-button-1", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1-button-2", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "ERP_GEN$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_formula_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1-button-3", "label": "导出", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-1", "label": "查看", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-2", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1-button-3", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "ERP_GEN$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_formula_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list-TERP_MIGRATE$gen_uom_formula_type_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-toolbar-actions-1", "label": "按钮组", "type": "ToolbarActions"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-EXsrnn9u6ZY58nLoMnbmt", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-p7NhOoC8nlrCoWNCymrni", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-CspaK7LHGIiJ2z6V53Mep", "label": "基础单位", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-p7NhOoC8nlrCoWNCymrni", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-GJRetyecJHk3tpvIlHxsa", "label": "基本单位系数", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-p7NhOoC8nlrCoWNCymrni", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-nyY9-5ww23l5orfN4gTC5", "label": "目标单位", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-p7NhOoC8nlrCoWNCymrni", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-aVBaYpNhGIE-ZeVbiFpBC", "label": "目标单位系数", "path": [{"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-table-container-TERP_MIGRATE$gen_uom_formula_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_UOM_CONVERTOR-p7NhOoC8nlrCoWNCymrni", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}