{"access": "Private", "description": "{}", "key": "TSRM$ORG_USER_ADD_SERVICE", "name": "ORG_添加用户服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "TSRM$user", "name": "用户"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$ORG_USER_ADD_perm_ac", "relations": [{"actionType": "Action", "code": "TSRM$WQ_ORG_USER_ADD_ACTION", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": null, "sourceCode": null}], "returnModel": {"children": null, "desc": null, "key": "TSRM$user", "name": "用户"}, "returnModelArrayWhether": false, "states": []}, "isDeleted": null, "isEnabled": true, "modelKey": "TSRM$user", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_30b630120a", "name": "开始", "props": {"globalVariable": [{"elements": null, "fieldAlias": "TSRM_WQ_ORG_USER_ADD_ACTION", "fieldKey": "TSRM_WQ_ORG_USER_ADD_ACTION", "fieldName": "[员工管理-新增action]节点出参", "fieldType": "Object", "id": null}], "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "用户"}}], "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "用户"}}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_380cf0a38a", "name": "员工管理-新增action", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(TSRM$user)request", "implementation": "TSRM$WQ_ORG_USER_ADD_ACTION", "implementationName": "员工管理-新增action", "newAction": true, "output": [{"elements": [{"fieldAlias": "success", "fieldKey": "success", "fieldName": "success", "fieldType": "Boolean", "id": null}, {"elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}, {"elements": [], "fieldAlias": "err", "fieldKey": "err", "fieldName": "err", "fieldType": "Object", "id": null}, {"elements": [], "fieldAlias": "info", "fieldKey": "info", "fieldName": "info", "fieldType": "Object", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "ActionResponse", "fieldType": "Object", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_380cf0a38a", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}, {"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "TSRM_WQ_ORG_USER_ADD_ACTION", "valueName": "[员工管理-新增action]节点出参"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_380cf0a38a", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_de00251270", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "用户"}}], "key": "TSRM$ORG_USER_ADD_SERVICE", "name": "ORG_添加用户服务", "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "用户"}}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$ORG_USER_ADD_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}