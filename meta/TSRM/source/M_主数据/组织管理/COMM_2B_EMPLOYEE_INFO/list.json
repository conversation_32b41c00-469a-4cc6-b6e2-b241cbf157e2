{"access": "Private", "key": "TSRM$COMM_2B_EMPLOYEE_INFO:list", "name": "list", "props": {"containerSelect": {"CMYG3uqmSlGpbMVLJrKu3": [{"field": "id", "selectFields": null}, {"field": "extWqPurOrg", "selectFields": []}], "VDQSKDhEG0MRS8o7PspIu": [{"field": "userId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "code", "selectFields": null}, {"field": "name", "selectFields": null}, {"field": "type", "selectFields": null}, {"field": "email", "selectFields": null}, {"field": "mobile", "selectFields": null}, {"field": "status", "selectFields": null}]}, "content": {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "个人信息", "useExpression": false}, "type": "Meta"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "Lm00zG3xzUrRn06oUzi9p", "name": "修改密码 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"action": {"selector": "userId.id", "target": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail"}, "name": "id", "type": "action"}], "type": "Modal"}}, "buttonType": "default", "confirmOn": "off", "label": "修改密码", "permissionKey": "TSRM$COMM_2B_EMPLOYEE_INFO-list_perm_ac_z_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "U5lGmZc05kFa4tkranBHs", "name": "修改个人信息 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"action": {"selector": "id", "target": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail"}, "expression": "route.recordId", "name": "id", "type": "action"}], "type": "Modal"}}, "buttonType": "default", "confirmOn": "off", "label": "修改个人信息", "permissionKey": "TSRM$COMM_2B_EMPLOYEE_INFO-list_perm_ac_z_1_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "9PDi3449oUJULFZ4Kui3m", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editable": false, "label": "ID", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false}, "key": "uez7t-2pEe6VsHlB8gbpg", "valueRules": null}], "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-code", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "员工编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-name", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "姓名", "name": "name", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-type", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "员工类型", "name": "type", "type": "SELECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-mobile", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "手机", "name": "mobile", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-email", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "邮箱", "name": "email", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "displayComponentType": "RelationShow", "editable": false, "label": "地址", "name": "addressId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressDetail", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "详细地址", "name": "addressDetail", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-entryAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "entryAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "label": "入职日期", "name": "entryAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-resignationAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "resignationAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "label": "离职日期", "name": "resignationAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-status", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "状态", "name": "status", "type": "SELECT"}}], "key": "A45jI71YowHwN4V0LyYvT", "name": "DetailGroupItem", "props": {"title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-userId", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "nickname", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "昵称", "name": "userId.nickname", "required": false, "type": "TEXT", "width": 120}}, {"children": [], "key": "ZVrD-LfB_crTVkBL0xpMH", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "username", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "用户名", "name": "userId.username", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Zg6ypb2jsziTOpSMdeVop", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "用户邮箱", "name": "userId.email", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "3Z8EXPsqTmE7zfvG5B37d", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "用户手机", "name": "userId.mobile", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "cn4e74lxANutwHKp0oD-a", "name": "DetailGroupItem", "props": {"title": "员工账号信息"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "jISZdvIaZMLhWm0M7DMC3", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$COMM_2B_EMPLOYEE_INFO-BpTJgduuW7HZKHUI68b01", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [], "key": "znBzesXPAsqqSstn1A6_z", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "mDNcnAHu-V8tK5qsPbTNi", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "gFJP-a7Q_xbSsdnXGAV5w", "name": "Field", "props": {"componentProps": {"fieldAlias": "admOrg", "label": "选择行政组织", "labelField": "code", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "name", "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "OwViybZq5byZ0XWGCeEMa", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "行政组织", "modelAlias": "sys_common$org_struct_md", "name": "admOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "jQXxEmCP6HG9jMY4NYegn", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "orgType", "status"], "fieldAlias": "orgRole", "label": "选择组织角色", "labelField": "code", "modelAlias": "TB2B$ext_wq_org_role_cf", "parentModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TB2B$SYS_FindDataByIdService", "searchServiceKey": "TB2B$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "name", "modelAlias": "TB2B$ext_wq_org_role_cf"}, "displayComponentType": "RelationShow", "hidden": false, "initialValue": null, "label": "组织角色", "modelAlias": "TB2B$ext_wq_org_role_cf", "name": "orgRole", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "W0SoupspT58ceR7Wj9TF2", "name": "Field", "props": {"componentProps": {"fieldAlias": "invOrg", "label": "选择需求单位", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "z2V1Ux3zXsb2e5aZvmshP", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "库存组织", "name": "invOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "_2Rhg51FwbG8_daSuvSGQ", "name": "Field", "props": {"componentProps": {"fieldAlias": "comInfo", "label": "选择所属公司", "labelField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "parentModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "name", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "公司名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "uWeWlto5OrX-QX7aT6HaN", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "所属公司", "name": "comInfo", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ROmQRrcf2ic8951aWR_V8", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "leader", "modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "是否领导", "name": "leader", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}], "key": "s6sK8V18SXtXP7CrV1yv_", "name": "Fields", "props": {}, "type": "Meta"}], "key": "Nv_xp0aUNzyyYMyum8ACY", "name": "Table", "props": {"enableSolution": false, "fieldName": "extWqAdmOrg", "flow": {"context$": "$context", "name": "extWqAdmOrg", "type": "RelationData"}, "label": "表格", "mode": "simple", "modelAlias": "TB2B$ext_wq_employee_adm_org_cf"}, "type": "Container"}], "key": "tX1sS1pyYVoW3LDXUQ15P", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "label": "自定义详情字段"}, "type": "Meta"}], "key": "XvYp2hD-6hF1vhOCUslFr", "name": "DetailGroupItem", "props": {"title": "行政组织"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "3J1BNN5XQOqLk3SW3xWZl", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$COMM_2B_EMPLOYEE_INFO-auttvpR5YMTBVCFlWc5Uc", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [], "key": "y0k6NfwFx-JZ_CQZ6dOXo", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "zpJMCEXoOnL5GZameLZkx", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "HlFh0bJJNe2KonHYRcqLZ", "name": "Field", "props": {"componentProps": {"fieldAlias": "purOrg", "label": "选择采购组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_employee_pur_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "-UGVQFJOYGt4C39UTboFx", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "采购组织", "name": "purOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE_INFO-poA81n_Ba4DAiIfmAJF_8", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "label": "组织编码", "name": "purOrg.code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "X0cCTgioSDfHmOOMHPQJT", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TamMu9lcXFWSpxCfjeWOi", "name": "Table", "props": {"enableSolution": false, "fieldName": "extWqPurOrg", "flow": {"context$": "$context", "name": "extWqPurOrg", "type": "RelationData"}, "label": "表格", "mode": "simple", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf"}, "type": "Container"}], "key": "iUxUeNnWGtMtUquDOADvh", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "label": "自定义详情字段"}, "type": "Meta"}], "key": "jeltjNBA2-ADZ1lcYiTIu", "name": "DetailGroupItem", "props": {"title": "采购组织"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "name": "DetailGroupItem", "props": {"title": ""}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "name": "Detail", "props": {"flow": {"context$": "$context", "params": [], "serviceKey": "ERP_GEN$ORG_GET_EMPLOYEE_BY_CURRENT_USER_ALL", "type": "InvokeService"}, "modelAlias": "sys_common$org_employee_md"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "jbL7ZKQFYQIOKhTYFumGC", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentType": "InputNumber", "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "kcfvQodO_NDAfxh5GuIK8", "name": "FormField", "props": {"componentProps": {"fieldAlias": "userId", "label": "选择用户", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "nickname", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "label": "昵称", "name": "nickname", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "username", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "label": "用户名", "name": "username", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "email", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "label": "用户邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mobile", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "label": "用户手机", "name": "mobile", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "TSRM$user", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$user"}}], "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "username", "modelAlias": "TSRM$user", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "label": "用户", "lookup": [{"fieldRules": {"hidden": true, "readOnly": true, "required": false}, "key": "hv6s3tQ1O2dcx5SpFId6I", "valueRules": null}], "name": "userId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "OEYWUWBc58T7YMGmtxnkz", "name": "FormField", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "员工编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "j9vbPIJHwH_k3TbWVFs0f", "valueRules": null}], "name": "code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "dyqNRmcs5zRdTi8ZNkUp4", "name": "FormField", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "姓名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "HEKgbsvQdrjoR7rDAvHW7", "valueRules": null}], "name": "name", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "h2nvDh9z89Fl-7v1sFu7J", "name": "FormField", "props": {"componentProps": {"fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "员工类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "I0isnc8rnoIS0ZN26wa5D", "valueRules": null}], "name": "type", "required": true, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "VLyGRySH8l8IRuz98B1rI", "name": "FormField", "props": {"componentProps": {"fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "邮箱", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "Qow3VI0IfYhMgspfUzKvi", "valueRules": null}], "name": "email", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "4NiVMVRLxlD7BImsNnxSF", "name": "FormField", "props": {"componentProps": {"fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "手机", "name": "mobile", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "HTcNZZiAGaAsBxGWo48TX", "name": "FormField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "状态", "lookup": [{"fieldRules": {"hidden": true, "readOnly": true, "required": false}, "key": "-NnS769Tj294n4djYqFSo", "valueRules": null}], "name": "status", "required": true, "type": "SELECT", "width": 120}, "type": "Widget"}], "displayName": "修改个人信息", "key": "VDQSKDhEG0MRS8o7PspIu", "name": "FormGroup", "props": {"flow": {"containerKey": "VDQSKDhEG0MRS8o7PspIu", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "params?.id", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}], "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_employee_md"}, "type": "Container"}], "key": "t-Ohd0Q9pmyzKt5pzyg5k", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "oGXXsrXiNk_LNG20ubtgN", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["U5lGmZc05kFa4tkranBHs"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_EMPLOYEE_INFO-list_perm_ac_z_3_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "7Cj6WAnJMxImdv9nMRGJz", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [], "service": "TSRM$ORG_EMPLOYEE_PERSONAL_INFORMATION_EDIT_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["U5lGmZc05kFa4tkranBHs"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail"}, {"action": "Message", "message": "操作成功！"}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_EMPLOYEE_INFO-list_perm_ac_z_3_1_1", "type": "primary"}, "type": "Widget"}], "key": "K7rbRUZjmUKwE7SbZtiha", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "U5lGmZc05kFa4tkranBHs", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"width": 560}, "params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "title": "修改个人信息"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "ov_rBzFu_SZfYWmlO3JTd", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentType": "InputNumber", "label": "ID", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "IuVUAkSBIyt7uJpWsxNvV", "valueRules": null}], "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [{"children": [{"children": [], "key": "EX2Hn3CK8PeFE2srgSOsD", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "SA5oGcAf7Xxf3oI4cOZeO", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "2ZZLVYVt2xcZKMURR50j8", "name": "Field", "props": {"componentProps": {"fieldAlias": "purOrg", "label": "选择采购组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_employee_pur_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId", "extWqNccCode", "extWqSapCodeWq", "extWqNcCodeWq", "extWqK3CodeWq", "extWqComType", "extAbbr", "extEstBank", "extEstName", "extBankAcct", "genWcHeadId"], "fieldAlias": "comOrgCode", "label": "选择所属公司组织", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "hidden": false, "label": "所属公司组织", "name": "comOrgCode", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["languageCode", "languageName"], "fieldAlias": "languageId", "label": "选择语言", "labelField": "languageName", "modelAlias": "ERP_GEN$gen_language_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "语言", "name": "languageId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "area", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "区号", "name": "area", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "tele", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "电话/分机号", "name": "tele", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "fax", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "传真/分机号", "name": "fax", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mail", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "邮箱", "name": "mail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "phone", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "手机", "name": "phone", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["addrCode", "addrName", "postCode", "counId", "<PERSON><PERSON><PERSON><PERSON>", "addrParentId", "plnSlsOrgTypeCfId", "plnSlsOrgTypeCfTestId", "plnSoaRegconsMdId"], "fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "地址", "name": "addressId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "addressDetail", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "街道", "name": "addressDetail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["timezoneCode", "timezoneDesc", "counId", "timezoneDistrict", "timezoneFormat", "isSummerTime", "summerTimeStart", "summerTimeEnd", "status"], "fieldAlias": "timezoneId", "label": "选择时区", "labelField": "timezoneDesc", "modelAlias": "ERP_GEN$gen_timezone_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "时区", "name": "timezoneId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "postcode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "邮编", "name": "postcode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "coordinate", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "经纬度坐标", "name": "coordinate", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "vendCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "公司间开票的供应商编号", "name": "vendCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "custCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "公司间开票的客户编号", "name": "custCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "vendCode", "status", "purOrgId", "slsOrgId", "slsCustInfoId", "purVendInfoId", "orgSlsDcId", "extBelongType", "testbomHeader1Id", "extWqNccCode", "extWqNcCode", "extWqSapCode", "extWqK3Code", "extWqComInfo", "extWqErpType", "extWqErpPurOrgCode", "extWqErpPurOrgName"], "fieldAlias": "invOrgId", "label": "选择库存组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "库存组织", "name": "invOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["vendPurCode", "vendPurDimension", "typeName", "remark", "status", "partnerProcedureRef", "attachmentProcedureRef", "isInvestigation", "isApprove"], "fieldAlias": "extWqVendWarehouse", "label": "选择供应商库配置", "labelField": "typeName", "modelAlias": "ERP_GEN$gen_vend_pur_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "供应商库配置", "name": "extWqVendWarehouse", "required": false, "type": "OBJECT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId", "extWqNccCode", "extWqSapCodeWq", "extWqNcCodeWq", "extWqK3CodeWq", "extWqComType", "extAbbr", "extEstBank", "extEstName", "extBankAcct", "genWcHeadId"], "fieldAlias": "comOrgCode", "label": "选择所属公司组织", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "hidden": true, "label": "所属公司组织", "name": "comOrgCode", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["languageCode", "languageName"], "fieldAlias": "languageId", "label": "选择语言", "labelField": "languageName", "modelAlias": "ERP_GEN$gen_language_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "语言", "name": "languageId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "area", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "区号", "name": "area", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "tele", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "电话/分机号", "name": "tele", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "fax", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "传真/分机号", "name": "fax", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mail", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "邮箱", "name": "mail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "phone", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "手机", "name": "phone", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["addrCode", "addrName", "postCode", "counId", "<PERSON><PERSON><PERSON><PERSON>", "addrParentId", "plnSlsOrgTypeCfId", "plnSlsOrgTypeCfTestId", "plnSoaRegconsMdId"], "fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "地址", "name": "addressId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "addressDetail", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "街道", "name": "addressDetail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["timezoneCode", "timezoneDesc", "counId", "timezoneDistrict", "timezoneFormat", "isSummerTime", "summerTimeStart", "summerTimeEnd", "status"], "fieldAlias": "timezoneId", "label": "选择时区", "labelField": "timezoneDesc", "modelAlias": "ERP_GEN$gen_timezone_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "时区", "name": "timezoneId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "postcode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "邮编", "name": "postcode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "coordinate", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "经纬度坐标", "name": "coordinate", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "vendCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "公司间开票的供应商编号", "name": "vendCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "custCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "公司间开票的客户编号", "name": "custCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "vendCode", "status", "purOrgId", "slsOrgId", "slsCustInfoId", "purVendInfoId", "orgSlsDcId", "extBelongType", "testbomHeader1Id", "extWqNccCode", "extWqNcCode", "extWqSapCode", "extWqK3Code", "extWqComInfo", "extWqErpType", "extWqErpPurOrgCode", "extWqErpPurOrgName"], "fieldAlias": "invOrgId", "label": "选择库存组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "库存组织", "name": "invOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["vendPurCode", "vendPurDimension", "typeName", "remark", "status", "partnerProcedureRef", "attachmentProcedureRef", "isInvestigation", "isApprove"], "fieldAlias": "extWqVendWarehouse", "label": "选择供应商库配置", "labelField": "typeName", "modelAlias": "ERP_GEN$gen_vend_pur_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "供应商库配置", "name": "extWqVendWarehouse", "required": false, "type": "OBJECT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "采购组织", "lookup": [{"fieldRules": {"required": true}, "key": "6l2KxH-W-hC_fMTInjEet", "valueRules": null}], "name": "purOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "DVz64eBEO0nsQZKsKu29e", "name": "Field", "props": {"componentProps": {"fieldAlias": "erpSystem", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf", "placeholder": "请选择"}, "hidden": false, "label": "ERP系统", "lookup": [{"fieldRules": {"required": true}, "key": "sqOqLIdYojflnmTONIZYr", "valueRules": null}], "name": "erpSystem", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "o5iFOKww0BDTKzDScJb1Y", "name": "Field", "props": {"componentProps": {"fieldAlias": "erpSystemAccout", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf", "placeholder": "请输入"}, "hidden": false, "label": "ERP系统账号", "lookup": [{"fieldRules": {"required": true}, "key": "wXETih4hzdLYhwi9XStXa", "valueRules": null}], "name": "erpSystemAccout", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "gorly3E9XbTVsM9f0Hf79", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ScVfNbwCNR7orD-lNMifU", "name": "TableForm", "props": {"fieldName": "extWqPurOrg", "fields": [], "label": "表格表单", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf"}, "type": "Widget"}], "key": "MbY8QAUUf2ZTS4e47tnm1", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "displayName": "维护账号映射关系", "key": "CMYG3uqmSlGpbMVLJrKu3", "name": "FormGroup", "props": {"flow": {"containerKey": "CMYG3uqmSlGpbMVLJrKu3", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "params?.id", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}], "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_employee_md"}, "type": "Container"}], "key": "6kYlgjsFxBA9VJwTAOs--", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "Nl18u4gOpLIrCkDpA6GeP", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["a3u0rVck-jhURLwMWnNy7"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_EMPLOYEE_INFO-list_perm_ac_z_4_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "j3YuQm_eQVc2A7ZirhUPT", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"elements": [{"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER"}, {"fieldAlias": "created<PERSON>y", "fieldName": "创建人", "fieldType": "OBJECT"}, {"fieldAlias": "updatedBy", "fieldName": "更新人", "fieldType": "OBJECT"}, {"fieldAlias": "createdAt", "fieldName": "创建时间", "fieldType": "DATE"}, {"fieldAlias": "updatedAt", "fieldName": "更新时间", "fieldType": "DATE"}, {"fieldAlias": "version", "fieldName": "版本号", "fieldType": "NUMBER"}, {"fieldAlias": "deleted", "fieldName": "逻辑删除标识", "fieldType": "NUMBER"}, {"fieldAlias": "userId", "fieldName": "用户", "fieldType": "OBJECT"}, {"fieldAlias": "code", "fieldName": "员工编码", "fieldType": "TEXT"}, {"fieldAlias": "name", "fieldName": "姓名", "fieldType": "TEXT"}, {"fieldAlias": "status", "fieldName": "状态", "fieldType": "ENUM"}, {"fieldAlias": "type", "fieldName": "员工类型", "fieldType": "ENUM"}, {"fieldAlias": "email", "fieldName": "邮箱", "fieldType": "TEXT"}, {"fieldAlias": "mobile", "fieldName": "手机", "fieldType": "TEXT"}, {"fieldAlias": "addressId", "fieldName": "地址", "fieldType": "OBJECT"}, {"fieldAlias": "addressDetail", "fieldName": "详细地址", "fieldType": "TEXT"}, {"fieldAlias": "entryAt", "fieldName": "入职日期", "fieldType": "DATE"}, {"fieldAlias": "resignationAt", "fieldName": "离职日期", "fieldType": "DATE"}, {"element": {"fieldAlias": "element", "fieldName": "element", "fieldType": "Model", "modelAlias": "TERP_MIGRATE$org_employee_org_link_cf"}, "fieldAlias": "employeeOrgLinkList", "fieldName": "组织单元", "fieldType": "Array"}, {"element": {"fieldAlias": "element", "fieldName": "element", "fieldType": "Model", "modelAlias": "TERP_MIGRATE$org_employee_role_link_cf"}, "fieldAlias": "employeeRoleLinkList", "fieldName": "角色", "fieldType": "Array"}, {"element": {"fieldAlias": "element", "fieldName": "element", "fieldType": "Model", "modelAlias": "TB2B$ext_wq_employee_adm_org_cf"}, "fieldAlias": "extWqAdmOrg", "fieldName": "行政组织", "fieldType": "Array"}, {"element": {"fieldAlias": "element", "fieldName": "element", "fieldType": "Model", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf"}, "fieldAlias": "extWqPurOrg", "fieldName": "采购组织", "fieldType": "Array"}, {"fieldAlias": "extWqSignature", "fieldName": "签名图", "fieldType": "ATTACHMENT"}, {"fieldAlias": "extWqResignationFlag", "fieldName": "离职标记", "fieldType": "BOOL"}], "fieldAlias": "data", "fieldName": "data", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "valueConfig": {"action": {"selector": "", "target": "CMYG3uqmSlGpbMVLJrKu3"}, "type": "action"}}], "service": "TSRM$ORG_EMPLOYEE_MD_PUR_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["a3u0rVck-jhURLwMWnNy7"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail"}, {"action": "Message", "message": "操作成功！"}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_EMPLOYEE_INFO-list_perm_ac_z_4_1_1", "type": "primary"}, "type": "Widget"}], "key": "PQwdyGhFyPajLtprnRQlF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "a3u0rVck-jhURLwMWnNy7", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"width": 560}, "params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "title": "维护账号映射关系"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "lu0qDyj6B0sI9noP5FwAD", "name": "FormField", "props": {"componentProps": {"fieldAlias": "userId", "modelAlias": "TSRM$ext_wq_employee_user_temp", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentType": "InputNumber", "hidden": false, "label": "用户id", "lookup": [{"action": "get", "fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "SyHsE9KpDdFIcWemv2tUj", "operator": "FIELD", "valueRules": {"scope": "", "type": "FIELD", "val": "userId", "value": "userId", "valueType": "expression"}}], "name": "userId", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "gu1TOKaNh6yqdJbnSbsuT", "name": "FormField", "props": {"componentProps": {"fieldAlias": "originalPwd", "modelAlias": "TSRM$ext_wq_employee_user_temp", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "原始密码", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": true}, "key": "T1I1ZRPPiJpws0bcMH1Nb", "valueRules": null}], "name": "originalPwd", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ex_xYiWK0Ie66z3kbh3HD", "name": "FormField", "props": {"componentProps": {"fieldAlias": "userPwd", "modelAlias": "TSRM$ext_wq_employee_user_temp", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "新密码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "oW3-NdJmTlZ8LTYtDrxGl", "valueRules": null}], "name": "userPwd", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "displayName": "修改密码", "key": "PIBrtGjT8-lv_8ZjqUGZo", "name": "FormGroup", "props": {"flow": {}, "modelAlias": "TSRM$ext_wq_employee_user_temp"}, "type": "Container"}], "key": "-NYAKqOKYD2-rZs7LKe6h", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "J3cxqFnw15yCyi9IcKhSb", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["Lm00zG3xzUrRn06oUzi9p"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_EMPLOYEE_INFO-list_perm_ac_z_5_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "OgA83PYVM1MqHQ1WC06nS", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "员工用户临时模型", "fieldType": "Model", "modelAlias": "TSRM$ext_wq_employee_user_temp", "valueConfig": {"action": {"selector": "", "target": "PIBrtGjT8-lv_8ZjqUGZo"}, "type": "action"}}], "service": "TSRM$ORG_EMPLOYEE_PERSONAL_INFORMATION_PASSWORD_EDIT"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "保存成功"}, {"action": "Close", "target": ["Lm00zG3xzUrRn06oUzi9p"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_EMPLOYEE_INFO-list_perm_ac_z_5_1_1", "type": "primary"}, "type": "Widget"}], "key": "8wQpR9IlJXAJUcukTSI8Q", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "Lm00zG3xzUrRn06oUzi9p", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"width": 560}, "params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "title": "修改密码"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": false, "showFooter": false, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "i18nConfig": {"i18nKeySet": ["用户", "选择库存组织", "状态", "电话/分机号", "时区", "库存组织", "是否领导", "公司间开票的客户编号", "保存", "姓名", "修改个人信息", "选择需求单位", "地址", "用户名", "修改密码", "ID", "创建人", "维护账号映射关系", "传真/分机号", "用户手机", "新密码", "语言", "自定义详情字段", "逻辑删除标识", "所属公司", "选择采购组织", "请选择", "公司间开票的供应商编号", "版本号", "基本信息", "更新时间", "采购组织", "组织角色", "供应商库配置", "行政组织", "选择创建人", "选择组织角色", "ERP系统账号", "邮编", "选择所属公司", "所属公司组织", "选择行政组织", "员工编码", "组织名称", "选择更新人", "手机", "表格", "ERP系统", "操作成功！", "公司名称", "个人信息", "街道", "选择语言", "详细地址", "入职日期", "离职日期", "邮箱", "用户邮箱", "昵称", "用户id", "原始密码", "表格表单", "保存成功", "选择供应商库配置", "员工账号信息", "选择所属公司组织", "详情组标题", "组织编码", "选择地址", "更新人", "选择用户", "请输入", "区号", "取消", "创建时间", "经纬度坐标", "员工类型", "选择时区"], "i18nScanPaths": ["COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-userId.props.componentProps.placeholder", "gu1TOKaNh6yqdJbnSbsuT.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.12.label", "U5lGmZc05kFa4tkranBHs.props.title", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.22.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.16.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.25.label", "ex_xYiWK0Ie66z3kbh3HD.props.componentProps.placeholder", "Nv_xp0aUNzyyYMyum8ACY.props.label", "_2Rhg51FwbG8_daSuvSGQ.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.6.label", "DVz64eBEO0nsQZKsKu29e.props.componentProps.placeholder", "gFJP-a7Q_xbSsdnXGAV5w.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressDetail.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.10.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.0.label", "kcfvQodO_NDAfxh5GuIK8.props.editComponentProps.fields.3.componentProps.placeholder", "_2Rhg51FwbG8_daSuvSGQ.props.label", "jbL7ZKQFYQIOKhTYFumGC.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.6.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-mobile.props.label", "kcfvQodO_NDAfxh5GuIK8.props.editComponentProps.fields.2.label", "XvYp2hD-6hF1vhOCUslFr.props.title", "VLyGRySH8l8IRuz98B1rI.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.18.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.7.componentProps.placeholder", "Lm00zG3xzUrRn06oUzi9p.props.title", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.15.componentProps.placeholder", "ZVrD-LfB_crTVkBL0xpMH.props.componentProps.placeholder", "kcfvQodO_NDAfxh5GuIK8.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.2.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.16.label", "DVz64eBEO0nsQZKsKu29e.props.label", "SA5oGcAf7Xxf3oI4cOZeO.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.17.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.3.componentProps.placeholder", "lu0qDyj6B0sI9noP5FwAD.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.24.label", "a3u0rVck-jhURLwMWnNy7.props.title", "2ZZLVYVt2xcZKMURR50j8.props.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.18.componentProps.placeholder", "gFJP-a7Q_xbSsdnXGAV5w.props.editComponentProps.fields.0.label", "kcfvQodO_NDAfxh5GuIK8.props.editComponentProps.fields.3.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-entryAt.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.5.label", "4NiVMVRLxlD7BImsNnxSF.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-resignationAt.props.label", "ROmQRrcf2ic8951aWR_V8.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE_INFO-poA81n_Ba4DAiIfmAJF_8.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE_INFO-poA81n_Ba4DAiIfmAJF_8.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.5.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.4.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.15.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.18.label", "_2Rhg51FwbG8_daSuvSGQ.props.componentProps.label", "HlFh0bJJNe2KonHYRcqLZ.props.editComponentProps.fields.0.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.25.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.14.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.18.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.3.componentProps.placeholder", "W0SoupspT58ceR7Wj9TF2.props.componentProps.label", "iUxUeNnWGtMtUquDOADvh.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.11.label", "HlFh0bJJNe2KonHYRcqLZ.props.editComponentProps.fields.0.componentProps.placeholder", "HlFh0bJJNe2KonHYRcqLZ.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.19.componentProps.placeholder", "mDNcnAHu-V8tK5qsPbTNi.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.6.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.11.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.19.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-status.props.componentProps.placeholder", "Zg6ypb2jsziTOpSMdeVop.props.componentProps.placeholder", "9PDi3449oUJULFZ4Kui3m.props.componentProps.placeholder", "kcfvQodO_NDAfxh5GuIK8.props.editComponentProps.fields.0.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.12.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.14.label", "TamMu9lcXFWSpxCfjeWOi.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.20.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-status.props.label", "OEYWUWBc58T7YMGmtxnkz.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.23.componentProps.placeholder", "gFJP-a7Q_xbSsdnXGAV5w.props.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.0.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.9.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.4.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.9.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.2.componentProps.label", "W0SoupspT58ceR7Wj9TF2.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.13.componentProps.placeholder", "ex_xYiWK0Ie66z3kbh3HD.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.10.componentProps.placeholder", "ROmQRrcf2ic8951aWR_V8.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.6.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-userId.props.label", "lu0qDyj6B0sI9noP5FwAD.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.21.componentProps.placeholder", "j3YuQm_eQVc2A7ZirhUPT.props.actionConfig.endLogicOtherConfig.2.message", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.10.componentProps.placeholder", "_2Rhg51FwbG8_daSuvSGQ.props.editComponentProps.fields.0.componentProps.placeholder", "jQXxEmCP6HG9jMY4NYegn.props.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.1.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.11.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.9.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.8.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-code.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.16.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.23.componentProps.placeholder", "dyqNRmcs5zRdTi8ZNkUp4.props.componentProps.placeholder", "W0SoupspT58ceR7Wj9TF2.props.editComponentProps.fields.0.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.12.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.12.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.18.componentProps.label", "kcfvQodO_NDAfxh5GuIK8.props.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-mobile.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.7.label", "kcfvQodO_NDAfxh5GuIK8.props.editComponentProps.fields.2.componentProps.placeholder", "ov_rBzFu_SZfYWmlO3JTd.props.label", "OgA83PYVM1MqHQ1WC06nS.props.label", "HlFh0bJJNe2KonHYRcqLZ.props.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.2.label", "oGXXsrXiNk_LNG20ubtgN.props.label", "Nl18u4gOpLIrCkDpA6GeP.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.22.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.11.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.22.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.25.componentProps.label", "gu1TOKaNh6yqdJbnSbsuT.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.18.label", "OEYWUWBc58T7YMGmtxnkz.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.8.componentProps.placeholder", "jbL7ZKQFYQIOKhTYFumGC.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.15.label", "OgA83PYVM1MqHQ1WC06nS.props.actionConfig.endLogicOtherConfig.0.message", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.5.componentProps.placeholder", "gFJP-a7Q_xbSsdnXGAV5w.props.editComponentProps.fields.0.componentProps.placeholder", "3Z8EXPsqTmE7zfvG5B37d.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.3.label", "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header-copy.props.label", "VLyGRySH8l8IRuz98B1rI.props.componentProps.placeholder", "kcfvQodO_NDAfxh5GuIK8.props.editComponentProps.fields.1.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.16.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.13.componentProps.placeholder", "W0SoupspT58ceR7Wj9TF2.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.10.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.2.label", "Zg6ypb2jsziTOpSMdeVop.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.25.label", "jeltjNBA2-ADZ1lcYiTIu.props.title", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressId.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.24.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.21.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-entryAt.props.label", "HTcNZZiAGaAsBxGWo48TX.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.13.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.24.componentProps.label", "cn4e74lxANutwHKp0oD-a.props.title", "h2nvDh9z89Fl-7v1sFu7J.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.24.componentProps.placeholder", "j3YuQm_eQVc2A7ZirhUPT.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressId.props.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-name.props.label", "ZVrD-LfB_crTVkBL0xpMH.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.19.componentProps.placeholder", "_2Rhg51FwbG8_daSuvSGQ.props.editComponentProps.fields.0.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-resignationAt.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.4.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.25.componentProps.placeholder", "jQXxEmCP6HG9jMY4NYegn.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.1.componentProps.placeholder", "ov_rBzFu_SZfYWmlO3JTd.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.10.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.2.componentProps.label", "W0SoupspT58ceR7Wj9TF2.props.editComponentProps.fields.0.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.9.componentProps.placeholder", "kcfvQodO_NDAfxh5GuIK8.props.label", "@exp:COMMON_2B$COMM_2B_EMPLOYEE-detail-page-title.props.title", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.9.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.23.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.8.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.20.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.20.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-type.props.label", "2ZZLVYVt2xcZKMURR50j8.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header-edit.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.25.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout.props.title", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.1.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.15.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.7.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.3.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.21.label", "4NiVMVRLxlD7BImsNnxSF.props.componentProps.placeholder", "jQXxEmCP6HG9jMY4NYegn.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.24.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-name.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.14.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressId.props.componentProps.placeholder", "7Cj6WAnJMxImdv9nMRGJz.props.actionConfig.endLogicOtherConfig.2.message", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.0.label", "3Z8EXPsqTmE7zfvG5B37d.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.24.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-email.props.componentProps.placeholder", "o5iFOKww0BDTKzDScJb1Y.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-email.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.7.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressDetail.props.label", "SA5oGcAf7Xxf3oI4cOZeO.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.13.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.19.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.10.componentProps.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.1.componentProps.label", "ScVfNbwCNR7orD-lNMifU.props.label", "o5iFOKww0BDTKzDScJb1Y.props.componentProps.placeholder", "7Cj6WAnJMxImdv9nMRGJz.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.16.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.21.label", "A45jI71YowHwN4V0LyYvT.props.title", "zpJMCEXoOnL5GZameLZkx.props.componentProps.placeholder", "HTcNZZiAGaAsBxGWo48TX.props.componentProps.placeholder", "h2nvDh9z89Fl-7v1sFu7J.props.componentProps.placeholder", "kcfvQodO_NDAfxh5GuIK8.props.editComponentProps.fields.0.label", "9PDi3449oUJULFZ4Kui3m.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.8.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.14.label", "HlFh0bJJNe2KonHYRcqLZ.props.componentProps.placeholder", "J3cxqFnw15yCyi9IcKhSb.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.20.label", "mDNcnAHu-V8tK5qsPbTNi.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.4.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.22.label", "dyqNRmcs5zRdTi8ZNkUp4.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.1.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.17.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.17.label", "gFJP-a7Q_xbSsdnXGAV5w.props.label", "kcfvQodO_NDAfxh5GuIK8.props.editComponentProps.fields.1.label", "tX1sS1pyYVoW3LDXUQ15P.props.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.5.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.0.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.9.label", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.fields.17.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.16.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.23.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-code.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-type.props.componentProps.placeholder", "2ZZLVYVt2xcZKMURR50j8.props.editComponentProps.filterFields.1.label", "zpJMCEXoOnL5GZameLZkx.props.label"]}, "key": "TSRM$COMM_2B_EMPLOYEE_INFO-list", "permissionKey": "TSRM$COMM_2B_EMPLOYEE_INFO-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$ORG_GET_EMPLOYEE_BY_CURRENT_USER_ALL", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header-copy", "label": "修改密码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header-edit", "label": "修改个人信息", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "VDQSKDhEG0MRS8o7PspIu", "label": "修改个人信息", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "t-Ohd0Q9pmyzKt5pzyg5k", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "oGXXsrXiNk_LNG20ubtgN", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "K7rbRUZjmUKwE7SbZtiha", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "7Cj6WAnJMxImdv9nMRGJz", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "K7rbRUZjmUKwE7SbZtiha", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "TSRM$ORG_EMPLOYEE_PERSONAL_INFORMATION_EDIT_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "CMYG3uqmSlGpbMVLJrKu3", "label": "维护账号映射关系", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "a3u0rVck-jhURLwMWnNy7", "label": "维护账号映射关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "6kYlgjsFxBA9VJwTAOs--", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "Nl18u4gOpLIrCkDpA6GeP", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "a3u0rVck-jhURLwMWnNy7", "label": "维护账号映射关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "PQwdyGhFyPajLtprnRQlF", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "j3YuQm_eQVc2A7ZirhUPT", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "a3u0rVck-jhURLwMWnNy7", "label": "维护账号映射关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "PQwdyGhFyPajLtprnRQlF", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "TSRM$ORG_EMPLOYEE_MD_PUR_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "PIBrtGjT8-lv_8ZjqUGZo", "label": "修改密码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "Lm00zG3xzUrRn06oUzi9p", "label": "修改密码", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "-NYAKqOKYD2-rZs7LKe6h", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [], "type": "Container"}, {"description": null, "key": "J3cxqFnw15yCyi9IcKhSb", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "Lm00zG3xzUrRn06oUzi9p", "label": "修改密码", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "8wQpR9IlJXAJUcukTSI8Q", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "OgA83PYVM1MqHQ1WC06nS", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "Lm00zG3xzUrRn06oUzi9p", "label": "修改密码", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "8wQpR9IlJXAJUcukTSI8Q", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "TSRM$ORG_EMPLOYEE_PERSONAL_INFORMATION_PASSWORD_EDIT", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "9PDi3449oUJULFZ4Kui3m", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-code", "label": "员工编码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-name", "label": "姓名", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-type", "label": "员工类型", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-mobile", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-email", "label": "邮箱", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressId", "label": "地址", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressDetail", "label": "详细地址", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-entryAt", "label": "入职日期", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-resignationAt", "label": "离职日期", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-status", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-userId", "label": "昵称", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "cn4e74lxANutwHKp0oD-a", "label": "员工账号信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ZVrD-LfB_crTVkBL0xpMH", "label": "用户名", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "cn4e74lxANutwHKp0oD-a", "label": "员工账号信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "Zg6ypb2jsziTOpSMdeVop", "label": "用户邮箱", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "cn4e74lxANutwHKp0oD-a", "label": "员工账号信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "3Z8EXPsqTmE7zfvG5B37d", "label": "用户手机", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "cn4e74lxANutwHKp0oD-a", "label": "员工账号信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "jbL7ZKQFYQIOKhTYFumGC", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "t-Ohd0Q9pmyzKt5pzyg5k", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "VDQSKDhEG0MRS8o7PspIu", "label": "修改个人信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "kcfvQodO_NDAfxh5GuIK8", "label": "用户", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "t-Ohd0Q9pmyzKt5pzyg5k", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "VDQSKDhEG0MRS8o7PspIu", "label": "修改个人信息", "type": "FormGroup"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "OEYWUWBc58T7YMGmtxnkz", "label": "员工编码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "t-Ohd0Q9pmyzKt5pzyg5k", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "VDQSKDhEG0MRS8o7PspIu", "label": "修改个人信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "dyqNRmcs5zRdTi8ZNkUp4", "label": "姓名", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "t-Ohd0Q9pmyzKt5pzyg5k", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "VDQSKDhEG0MRS8o7PspIu", "label": "修改个人信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "h2nvDh9z89Fl-7v1sFu7J", "label": "员工类型", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "t-Ohd0Q9pmyzKt5pzyg5k", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "VDQSKDhEG0MRS8o7PspIu", "label": "修改个人信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "VLyGRySH8l8IRuz98B1rI", "label": "邮箱", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "t-Ohd0Q9pmyzKt5pzyg5k", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "VDQSKDhEG0MRS8o7PspIu", "label": "修改个人信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "4NiVMVRLxlD7BImsNnxSF", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "t-Ohd0Q9pmyzKt5pzyg5k", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "VDQSKDhEG0MRS8o7PspIu", "label": "修改个人信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "HTcNZZiAGaAsBxGWo48TX", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "U5lGmZc05kFa4tkranBHs", "label": "修改个人信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "t-Ohd0Q9pmyzKt5pzyg5k", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "VDQSKDhEG0MRS8o7PspIu", "label": "修改个人信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "ov_rBzFu_SZfYWmlO3JTd", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "a3u0rVck-jhURLwMWnNy7", "label": "维护账号映射关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "6kYlgjsFxBA9VJwTAOs--", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "CMYG3uqmSlGpbMVLJrKu3", "label": "维护账号映射关系", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "lu0qDyj6B0sI9noP5FwAD", "label": "用户id", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "Lm00zG3xzUrRn06oUzi9p", "label": "修改密码", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "-NYAKqOKYD2-rZs7LKe6h", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "PIBrtGjT8-lv_8ZjqUGZo", "label": "修改密码", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "gu1TOKaNh6yqdJbnSbsuT", "label": "原始密码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "Lm00zG3xzUrRn06oUzi9p", "label": "修改密码", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "-NYAKqOKYD2-rZs7LKe6h", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "PIBrtGjT8-lv_8ZjqUGZo", "label": "修改密码", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "ex_xYiWK0Ie66z3kbh3HD", "label": "新密码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "Lm00zG3xzUrRn06oUzi9p", "label": "修改密码", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "-NYAKqOKYD2-rZs7LKe6h", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "PIBrtGjT8-lv_8ZjqUGZo", "label": "修改密码", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TamMu9lcXFWSpxCfjeWOi", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "jeltjNBA2-ADZ1lcYiTIu", "label": "采购组织", "type": "DetailGroupItem"}, {"key": "iUxUeNnWGtMtUquDOADvh", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "ScVfNbwCNR7orD-lNMifU", "label": "表格表单", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "a3u0rVck-jhURLwMWnNy7", "label": "维护账号映射关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "6kYlgjsFxBA9VJwTAOs--", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "CMYG3uqmSlGpbMVLJrKu3", "label": "维护账号映射关系", "type": "FormGroup"}, {"key": "MbY8QAUUf2ZTS4e47tnm1", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "mDNcnAHu-V8tK5qsPbTNi", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "gFJP-a7Q_xbSsdnXGAV5w", "label": "行政组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "jQXxEmCP6HG9jMY4NYegn", "label": "组织角色", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TB2B$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_org_role_cf"}, "type": "SystemService"}, {"key": "TB2B$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_org_role_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "W0SoupspT58ceR7Wj9TF2", "label": "库存组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "_2Rhg51FwbG8_daSuvSGQ", "label": "所属公司", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ROmQRrcf2ic8951aWR_V8", "label": "是否领导", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "zpJMCEXoOnL5GZameLZkx", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "jeltjNBA2-ADZ1lcYiTIu", "label": "采购组织", "type": "DetailGroupItem"}, {"key": "iUxUeNnWGtMtUquDOADvh", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TamMu9lcXFWSpxCfjeWOi", "label": "表格", "type": "Table"}, {"key": "X0cCTgioSDfHmOOMHPQJT", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "HlFh0bJJNe2KonHYRcqLZ", "label": "采购组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "jeltjNBA2-ADZ1lcYiTIu", "label": "采购组织", "type": "DetailGroupItem"}, {"key": "iUxUeNnWGtMtUquDOADvh", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TamMu9lcXFWSpxCfjeWOi", "label": "表格", "type": "Table"}, {"key": "X0cCTgioSDfHmOOMHPQJT", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE_INFO-poA81n_Ba4DAiIfmAJF_8", "label": "组织编码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "jeltjNBA2-ADZ1lcYiTIu", "label": "采购组织", "type": "DetailGroupItem"}, {"key": "iUxUeNnWGtMtUquDOADvh", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TamMu9lcXFWSpxCfjeWOi", "label": "表格", "type": "Table"}, {"key": "X0cCTgioSDfHmOOMHPQJT", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "SA5oGcAf7Xxf3oI4cOZeO", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "a3u0rVck-jhURLwMWnNy7", "label": "维护账号映射关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "6kYlgjsFxBA9VJwTAOs--", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "CMYG3uqmSlGpbMVLJrKu3", "label": "维护账号映射关系", "type": "FormGroup"}, {"key": "MbY8QAUUf2ZTS4e47tnm1", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ScVfNbwCNR7orD-lNMifU", "label": "表格表单", "type": "TableForm"}, {"key": "gorly3E9XbTVsM9f0Hf79", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "2ZZLVYVt2xcZKMURR50j8", "label": "采购组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "a3u0rVck-jhURLwMWnNy7", "label": "维护账号映射关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "6kYlgjsFxBA9VJwTAOs--", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "CMYG3uqmSlGpbMVLJrKu3", "label": "维护账号映射关系", "type": "FormGroup"}, {"key": "MbY8QAUUf2ZTS4e47tnm1", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ScVfNbwCNR7orD-lNMifU", "label": "表格表单", "type": "TableForm"}, {"key": "gorly3E9XbTVsM9f0Hf79", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_language_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_language_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_timezone_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_timezone_type_cf"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_vend_pur_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_vend_pur_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "DVz64eBEO0nsQZKsKu29e", "label": "ERP系统", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "a3u0rVck-jhURLwMWnNy7", "label": "维护账号映射关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "6kYlgjsFxBA9VJwTAOs--", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "CMYG3uqmSlGpbMVLJrKu3", "label": "维护账号映射关系", "type": "FormGroup"}, {"key": "MbY8QAUUf2ZTS4e47tnm1", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ScVfNbwCNR7orD-lNMifU", "label": "表格表单", "type": "TableForm"}, {"key": "gorly3E9XbTVsM9f0Hf79", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "o5iFOKww0BDTKzDScJb1Y", "label": "ERP系统账号", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "a3u0rVck-jhURLwMWnNy7", "label": "维护账号映射关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "6kYlgjsFxBA9VJwTAOs--", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "CMYG3uqmSlGpbMVLJrKu3", "label": "维护账号映射关系", "type": "FormGroup"}, {"key": "MbY8QAUUf2ZTS4e47tnm1", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ScVfNbwCNR7orD-lNMifU", "label": "表格表单", "type": "TableForm"}, {"key": "gorly3E9XbTVsM9f0Hf79", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}