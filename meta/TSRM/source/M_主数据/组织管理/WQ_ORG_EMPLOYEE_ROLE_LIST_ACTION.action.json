{"access": "Private", "key": "TSRM$WQ_ORG_EMPLOYEE_ROLE_LIST_ACTION", "name": "员工管理-个人信息-获取用户角色列表", "props": {"bean": "ExtWqOrgEmployeeMdAction", "desc": null, "groovyScript": null, "languageType": "Java", "method": "roleList", "order": 8, "requestType": "io.terminus.tsrm.md.spi.model.org.dto.ExtWqEmployeeUserTempDTO", "responseType": "java.util.List<io.terminus.tsrm.md.spi.model.org.dto.RoleDTO>", "returnModel": {"children": null, "desc": null, "key": "TERP_MIGRATE$org_employee_role_link_cf", "name": "员工角色关联表"}, "status": "enabled"}, "type": "Action"}