{"access": "Private", "description": "{}", "key": "TSRM$SRM_SAVE_EMPLOYEE_WITH_ROLE", "name": "保存员工同时绑定角色", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1ifu6623q6", "name": "开始", "nextNodeKey": "node_1ifu67d8s9", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}}], "output": null, "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1ifu67d8s9", "name": "调用业务事件", "nextNodeKey": "node_1ifuanpvm36", "props": {"conditionGroup": null, "convert": null, "express": "REQUEST.(sys_common$org_employee_md)request", "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "serviceKey": "ERP_GEN$ORG_EMPLOYEE_SAVE_EVENT_SERVICE", "serviceName": null, "transactionPropagation": "NOT_SUPPORTED", "type": "CallEventServiceProperties"}, "type": "CallEventServiceNode"}, {"desc": null, "id": null, "key": "node_1ifuanpvm36", "name": "调用扩展服务", "nextNodeKey": "node_1ifu6623q7", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(sys_common$org_employee_md)request", "implementation": "TSRM$TSRM_USER_CREATE_WITH_ROLE_ACTION", "implementationName": "用户管理-新建员工自动关联角色", "newAction": true, "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_1ifu6623q7", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}}], "key": "TSRM$SRM_SAVE_EMPLOYEE_WITH_ROLE", "name": "保存员工同时绑定角色", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$SRM_SAVE_EMPLOYEE_WITH_ROLE_service_perm_ac_35a8ee", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}