{"access": "Private", "description": "{}", "key": "TSRM$ORG_USER_UPDATE_SERVICE", "name": "ORG_更新用户服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "TSRM$user", "name": "用户"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$ORG_USER_UPDATE_perm_ac", "relations": [{"actionType": "Action", "code": "TSRM$WQ_ORG_USER_UPDATE_ACTION", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": null, "sourceCode": null}], "returnModel": null, "returnModelArrayWhether": false, "states": []}, "isDeleted": null, "isEnabled": true, "modelKey": "TSRM$user", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_2a14340614", "name": "开始", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "用户"}}], "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_3c57cfa950", "name": "员工管理-更新action", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(TSRM$user)request", "implementation": "TSRM$WQ_ORG_USER_UPDATE_ACTION", "implementationName": "员工管理-更新action", "newAction": true, "output": null, "outputAssign": null, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_de0fc7c651", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "用户"}}], "key": "TSRM$ORG_USER_UPDATE_SERVICE", "name": "ORG_更新用户服务", "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$ORG_USER_UPDATE_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}