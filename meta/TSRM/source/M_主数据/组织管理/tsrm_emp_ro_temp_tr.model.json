{"access": "Private", "key": "TSRM$tsrm_emp_ro_temp_tr", "name": "员工角色临时模型", "props": {"alias": "TSRM$tsrm_emp_ro_temp_tr", "children": [{"alias": "id", "key": "id", "name": "id", "props": {"autoGenerated": false, "columnName": "id", "comment": "id", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": false, "length": 20, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "key", "key": "key", "name": "角色标识", "props": {"autoGenerated": false, "columnName": "key", "comment": "角色标识", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "name", "key": "name", "name": "角色名称", "props": {"autoGenerated": false, "columnName": "name", "comment": "角色名称", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "desc", "key": "desc", "name": "角色描述", "props": {"autoGenerated": false, "columnName": "desc", "comment": "角色描述", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "sourceApplicationId", "key": "source_application_id", "name": "所属门户", "props": {"autoGenerated": false, "columnName": "source_application_id", "comment": "所属门户", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": false, "length": 20, "required": false, "unique": false}, "type": "DataStructField"}], "desc": null, "props": {"config": {"persist": false, "self": false, "selfRelationFieldAlias": null, "system": false}, "mainField": "name", "mainFieldAlias": "name", "orderNumberEnabled": false, "originOrgIdEnabled": false, "physicalDelete": false, "searchModel": false, "tableName": "tsrm_emp_ro_temp_tr", "type": "VIEW"}}, "type": "Model"}