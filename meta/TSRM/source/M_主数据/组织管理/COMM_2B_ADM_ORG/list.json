{"access": "Private", "key": "TSRM$COMM_2B_ADM_ORG:list", "name": "list", "props": {"content": {"children": [{"children": [], "key": "sAyx6z3S4VqeI8ZpYuQcK", "name": "OrgList", "props": {"bizTypeCodes": ["ADM_ORG"], "createOrg": {"permissionKey": "TSRM$COMM_2B_ADM_ORG-list_perm_ac_z_0_createOrg"}, "createOrgTarget": {}, "disableOrg": {"permissionKey": "TSRM$COMM_2B_ADM_ORG-list_perm_ac_z_0_disableOrg"}, "disableOrgTarget": {}, "editOrg": {"permissionKey": "TSRM$COMM_2B_ADM_ORG-list_perm_ac_z_0_editOrg"}, "editOrgTarget": {}, "orgListServiceProps": {"OrgHiddenColFlow": {"serviceKey": "ERP_GEN$ORG_BIZ_TYPE_QUERY_HIDDE_FILED_EVENT_SERVICE", "type": "InvokeService"}, "OrgSearchFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_SEARCH_EVENT_SERVICE", "type": "InvokeService"}, "OrgUnitRoleFlow": {"serviceKey": "ERP_GEN$ORG_ORG_FRONT_ROLE_PAGE_EVENT_SERVICE", "type": "InvokeService"}, "deleteOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_DELETE_EVENT_SERVICE", "type": "InvokeService"}, "disableOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_DISABLE_EVENT_SERVICE", "type": "InvokeService"}, "enableOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_ENABLE_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgBizTypeListFlow": {"serviceKey": "ERP_GEN$ORG_BIZ_TYPE_FIND_ALL_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitByPidFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_QUERY_PID_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitDetailFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_QUERY_DETAIL_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitParentFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_PARENT_PAGE_EVENT_SERVICE", "type": "InvokeService"}, "saveOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_SAVE_EVENT_SERVICE", "type": "InvokeService"}}, "serviceProps": {"OrgHiddenColFlow": {"serviceKey": "ERP_GEN$ORG_BIZ_TYPE_QUERY_HIDDE_FILED_EVENT_SERVICE", "type": "InvokeService"}, "OrgSearchFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_SEARCH_EVENT_SERVICE", "type": "InvokeService"}, "OrgUnitRoleFlow": {"serviceKey": "ERP_GEN$ORG_ORG_FRONT_ROLE_PAGE_EVENT_SERVICE", "type": "InvokeService"}, "deleteOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_DELETE_EVENT_SERVICE", "type": "InvokeService"}, "disableOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_DISABLE_EVENT_SERVICE", "type": "InvokeService"}, "enableOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_ENABLE_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgBizTypeListFlow": {"serviceKey": "ERP_GEN$ORG_BIZ_TYPE_FIND_ALL_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitByPidFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_QUERY_PID_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitDetailFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_QUERY_DETAIL_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitParentFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_PARENT_PAGE_EVENT_SERVICE", "type": "InvokeService"}, "saveOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_SAVE_EVENT_SERVICE", "type": "InvokeService"}}}, "type": "Widget"}], "key": "0pfpBhAcV79Vsh9QeQ1_u", "name": "Page", "props": {"params": [], "showFooter": true, "showHeader": false, "title": "行政组织"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "terp", "service"]}, "i18nConfig": {}, "key": "TSRM$COMM_2B_ADM_ORG-list", "permissionKey": "TSRM$COMM_2B_ADM_ORG-list_perm_ac", "resources": [{"description": null, "key": "sAyx6z3S4VqeI8ZpYuQcK/createOrg", "label": "新建", "path": [{"key": "0pfpBhAcV79Vsh9QeQ1_u", "label": "页面", "type": "Page"}, {"key": "sAyx6z3S4VqeI8ZpYuQcK", "label": "组织列表", "type": "OrgList"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sAyx6z3S4VqeI8ZpYuQcK/editOrg", "label": "编辑", "path": [{"key": "0pfpBhAcV79Vsh9QeQ1_u", "label": "页面", "type": "Page"}, {"key": "sAyx6z3S4VqeI8ZpYuQcK", "label": "组织列表", "type": "OrgList"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sAyx6z3S4VqeI8ZpYuQcK/disableOrg", "label": "停用", "path": [{"key": "0pfpBhAcV79Vsh9QeQ1_u", "label": "页面", "type": "Page"}, {"key": "sAyx6z3S4VqeI8ZpYuQcK", "label": "组织列表", "type": "OrgList"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sAyx6z3S4VqeI8ZpYuQcK", "label": "组织列表", "path": [{"key": "0pfpBhAcV79Vsh9QeQ1_u", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$ORG_ORG_UNIT_QUERY_DETAIL_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_PARENT_PAGE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_BIZ_TYPE_FIND_ALL_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_SAVE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_QUERY_PID_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_DELETE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_DISABLE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_ENABLE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_FRONT_ROLE_PAGE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_SEARCH_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_BIZ_TYPE_QUERY_HIDDE_FILED_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}