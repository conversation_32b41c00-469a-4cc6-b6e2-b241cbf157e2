{"access": "Private", "key": "TSRM$ORG_PURCHASE_AGENT_CF_SAVE_SERVICE", "name": "ORG_采购代理配置_保存服务", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3coh0821", "name": "开始", "nextNodeKey": "node_1hd3cqq8023", "preNodeKey": null, "props": {"desc": null, "globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "checkData", "fieldKey": "checkData", "fieldName": "checkData", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3cqq8c24", "name": "条件", "nextNodeKey": "node_1hd3cvm6q26", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "YDJHCxT-Z4XHZARq22lJB", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "JRtILa52H63AmDh0VRoJ4", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "i7xeE34TNGaqyMbXl3v-C", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3cvm6q26", "name": "查询数据", "nextNodeKey": "node_1hd3d25ce30", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "7kf0tSuRqWq9q52HIBFAU", "key": null, "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": null, "valueKey": "agentCom", "valueName": "代理公司"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "TERP_MIGRATE$gen_com_type_cf"}, "valueKey": "agentCom", "valueName": "代理公司"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "wQVh2onF9BU1O5dUi9pIQ", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "4hEaggXj4fII4OL_Y1qi5", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}]}, "id": "1hd3d0kap27", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": "采购代理公司配置表"}, "valueKey": "NODE_OUTPUT_node_1hd3cvm6q26", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": "采购代理公司配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3d25ce31", "name": "条件", "nextNodeKey": "node_1hd3d31ql33", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "MposE-ww_UVhnoIzNnMJs", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}, {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "t1dagVtKY20sV4N7EnVHS", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "rmV_YQuaxGQMPeHeBuoBm", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3d31ql33", "name": "新增数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}]}, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": "采购代理公司配置表"}, "type": "CascadeCreateDataProperties"}, "renderType": null, "type": "CascadeCreateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3d25ce32", "name": "条件", "nextNodeKey": "node_1hd3d3s6n34", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "zjHXwa2Koe2OnPQsWN1_o", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}, {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "MEVwhg8wVV3pfrKdqLwuR", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "j8Ir29kaPCdUProRDbrC6", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3d3s6n34", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "代理公司重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hd3d25ce31", "node_1hd3d25ce32"], "id": null, "key": "node_1hd3d25ce30", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3cqq8c25", "name": "条件", "nextNodeKey": "node_1hd3d0tpr28", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "XX7DZ42rwu9dfURxxEVLR", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "1FZVu3HTCHdBEjX4MjmZD", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "D_aKXH-WrVq3jxnGZwtfw", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3d0tpr28", "name": "查询数据", "nextNodeKey": "node_1hd3d4c4i35", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "B_MF1TIomdcMSU6pt4D6s", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "NEQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "KzUoUIdg_yV2ULfopgwLX", "key": null, "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": null, "valueKey": "agentCom", "valueName": "代理公司"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "TERP_MIGRATE$gen_com_type_cf"}, "valueKey": "agentCom", "valueName": "代理公司"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "tPIATJxCDeBR7LZBAF6ds", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "QAHCrXJPEKVhxhlzvOkxu", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}]}, "id": "1hd3d1s0729", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": "采购代理公司配置表"}, "valueKey": "NODE_OUTPUT_node_1hd3d0tpr28", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": "采购代理公司配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3d4c5736", "name": "条件", "nextNodeKey": "node_1hd3d55sj38", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "hzrD-WO2y1n5aSeHcb0NW", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}, {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "o_1mWdpzqanPyi01zQ0Df", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "7p67RSTuU6Dc19dRm1yo0", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3d55sj38", "name": "更新数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": "采购代理公司配置表"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3d4c5737", "name": "条件", "nextNodeKey": "node_1hd3d693o39", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "lHEmzobPjyoqiCgiS1axN", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}, {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "n1LXf_gj0Hn8P56yYi9kj", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "6l87mbSmoDVH7OJbb3lni", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3d693o39", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "代理公司重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hd3d4c5736", "node_1hd3d4c5737"], "id": null, "key": "node_1hd3d4c4i35", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}], "headNodeKeys": ["node_1hd3cqq8c24", "node_1hd3cqq8c25"], "id": null, "key": "node_1hd3cqq8023", "name": "排他分支", "nextNodeKey": "node_1hd3coh0822", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3coh0822", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hd3coh0821"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_purchase_agent_cf", "modelKey": "TB2B$ext_wq_purchase_agent_cf", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$ORG_PURCHASE_AGENT_CF_SAVE_SERVICE", "name": "ORG_采购代理配置_保存服务", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_PURCHASE_AGENT_CF_SAVE_SERVICE_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}