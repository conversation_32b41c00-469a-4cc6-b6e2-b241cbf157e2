{"access": "Private", "key": "TSRM$ORG_ORG_ENABLE_EVENT_SERVICE", "name": "ORG_组织管理_启用组织", "props": {"isDeleted": null, "isEnabled": false, "modelKey": null, "serviceDslJson": {"children": null, "headNodeKeys": null, "input": null, "key": "COMMON_2B$ORG_ORG_ENABLE_EVENT_SERVICE", "name": "ORG_组织管理_启用组织", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_ORG_ENABLE_EVENT_SERVICE_perm_ac", "teamId": 1, "transactionPropagation": null, "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}