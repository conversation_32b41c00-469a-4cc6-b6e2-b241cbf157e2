{"access": "Private", "description": "{}", "key": "TSRM$ORG_EMPLOYEE_DISABLE_SERVICE", "name": "ORG_员工管理_禁用服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "sys_common$org_employee_md", "name": "员工信息表\t"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$ORG_EMPLOYEE_DISABLE_perm_ac", "relations": [{"actionType": "Action", "code": "TSRM$WQ_ORG_EMPLOYEE_DISABLE_ACTION", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": "TSRM$SYS_UpdateDataByIdService", "sourceCode": null}, {"actionType": "ServiceDefinition", "code": "TSRM$SYS_UpdateDataByIdService", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": "TSRM$WQ_ORG_USER_DISABLE_ACTION", "sourceCode": null}, {"actionType": "Action", "code": "TSRM$WQ_ORG_USER_DISABLE_ACTION", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": null, "sourceCode": null}], "returnModel": {"children": null, "desc": null, "key": "sys_common$org_employee_md", "name": "员工信息表\t"}, "returnModelArrayWhether": false, "states": []}, "isDeleted": null, "isEnabled": true, "modelKey": "sys_common$org_employee_md", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_c739d3ac5f", "name": "开始", "props": {"globalVariable": [{"fieldAlias": "TSRM_WQ_ORG_EMPLOYEE_DISABLE_ACTION", "fieldKey": "TSRM_WQ_ORG_EMPLOYEE_DISABLE_ACTION", "fieldName": "[员工管理-禁用-修改状态]节点出参", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}}], "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}}], "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_b800e4a1b6", "name": "员工管理-禁用-修改状态", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(sys_common$org_employee_md)request", "implementation": "TSRM$WQ_ORG_EMPLOYEE_DISABLE_ACTION", "implementationName": "员工管理-禁用-修改状态", "newAction": true, "output": [{"elements": [{"fieldAlias": "success", "fieldKey": "success", "fieldName": "success", "fieldType": "Boolean", "id": null}, {"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}}, {"elements": [], "fieldAlias": "err", "fieldKey": "err", "fieldName": "err", "fieldType": "Object", "id": null}, {"elements": [], "fieldAlias": "info", "fieldKey": "info", "fieldName": "info", "fieldType": "Object", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "ActionResponse", "fieldType": "Object", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_b800e4a1b6", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}, {"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "TSRM_WQ_ORG_EMPLOYEE_DISABLE_ACTION", "valueName": "[员工管理-禁用-修改状态]节点出参"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_b800e4a1b6", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_b888bc5fdc", "name": "(系统)更新数据服务", "props": {"async": false, "inputMapping": [{"field": {"elements": [], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "valueKey": "request", "valueName": "request"}]}}, {"field": {"elements": [], "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Object", "id": null}, "id": null, "value": {"constValue": "sys_common$org_employee_md", "fieldType": "Text", "id": null, "type": "ConstValue"}}], "output": null, "outputAssign": null, "serviceKey": "TSRM$SYS_UpdateDataByIdService", "serviceName": "(系统)更新数据服务", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"desc": null, "id": null, "key": "node_879a7ddaf4", "name": "员工管理-停用action", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(sys_common$org_employee_md)request", "implementation": "TSRM$WQ_ORG_USER_DISABLE_ACTION", "implementationName": "员工管理-停用action", "newAction": true, "output": null, "outputAssign": null, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_a82e4048a4", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}}], "key": "TSRM$ORG_EMPLOYEE_DISABLE_SERVICE", "name": "ORG_员工管理_禁用服务", "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$ORG_EMPLOYEE_DISABLE_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}