{"access": "Private", "description": "{}", "key": "TSRM$ORG_COMPANY_ECI_DETAIL_SERVICE", "name": "ORG_公司管理_天眼通_查询证照信息服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "ERP_GEN$gen_com_type_cf", "name": "公司配置表"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$ORG_COMPANY_ECI_DETAIL_perm_ac", "relations": [{"actionType": "Action", "code": "TSRM$WQ_ORG_COMPANY_ECI_DETAIL_ACTION", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": null, "sourceCode": null}], "returnModel": {"children": null, "desc": null, "key": "ERP_GEN$gen_com_type_cf", "name": "公司配置表"}, "returnModelArrayWhether": false, "states": []}, "isDeleted": null, "isEnabled": true, "modelKey": "ERP_GEN$gen_com_type_cf", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_5eec9a25c8", "name": "开始", "props": {"globalVariable": [{"fieldAlias": "TSRM_WQ_ORG_COMPANY_ECI_DETAIL_ACTION", "fieldKey": "TSRM_WQ_ORG_COMPANY_ECI_DETAIL_ACTION", "fieldName": "[公司管理-天眼查-补全证照数据action]节点出参", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}}], "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}}], "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_01bc03c783", "name": "公司管理-天眼查-补全证照数据action", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(ERP_GEN$gen_com_type_cf)request", "implementation": "TSRM$WQ_ORG_COMPANY_ECI_DETAIL_ACTION", "implementationName": "公司管理-天眼查-补全证照数据action", "newAction": true, "output": [{"elements": [{"fieldAlias": "success", "fieldKey": "success", "fieldName": "success", "fieldType": "Boolean", "id": null}, {"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "ERP_GEN$gen_com_type_cf"}}, {"elements": [], "fieldAlias": "err", "fieldKey": "err", "fieldName": "err", "fieldType": "Object", "id": null}, {"elements": [], "fieldAlias": "info", "fieldKey": "info", "fieldName": "info", "fieldType": "Object", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "ActionResponse", "fieldType": "Object", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_01bc03c783", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}, {"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "TSRM_WQ_ORG_COMPANY_ECI_DETAIL_ACTION", "valueName": "[公司管理-天眼查-补全证照数据action]节点出参"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_01bc03c783", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_0494a71f81", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}}], "key": "TSRM$ORG_COMPANY_ECI_DETAIL_SERVICE", "name": "ORG_公司管理_天眼通_查询证照信息服务", "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$ORG_COMPANY_ECI_DETAIL_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}