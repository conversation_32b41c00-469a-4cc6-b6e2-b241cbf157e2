{"access": "Private", "description": "{}", "key": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_EVENT_SERVICE", "name": "ORG_员工管理_保存事件服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "sys_common$org_employee_md", "name": "员工信息表\t"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_EVENT_perm_ac", "relations": [{"actionType": "ServiceDefinition", "code": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_CHECK", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE", "sourceCode": null}, {"actionType": "ServiceDefinition", "code": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": null, "sourceCode": null}], "returnModel": null, "returnModelArrayWhether": false, "states": []}, "isDeleted": null, "isEnabled": true, "modelKey": "sys_common$org_employee_md", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_5170a44626", "name": "开始", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}}], "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_f819ffad20", "name": "ORG_员工管理_保存校验", "props": {"async": false, "inputMapping": [{"field": {"elements": [], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "valueKey": "request", "valueName": "request"}]}}, {"field": {"elements": [], "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Object", "id": null}, "id": null, "value": {"constValue": "sys_common$org_employee_md", "fieldType": "Text", "id": null, "type": "ConstValue"}}], "output": null, "outputAssign": null, "serviceKey": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_CHECK", "serviceName": "ORG_员工管理_保存校验", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"desc": null, "id": null, "key": "node_a4d5d59dfc", "name": "ORG_员工管理_数据保存", "props": {"async": false, "inputMapping": [{"field": {"elements": [], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "valueKey": "request", "valueName": "request"}]}}, {"field": {"elements": [], "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Object", "id": null}, "id": null, "value": {"constValue": "sys_common$org_employee_md", "fieldType": "Text", "id": null, "type": "ConstValue"}}], "output": null, "outputAssign": null, "serviceKey": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE", "serviceName": "ORG_员工管理_数据保存", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"desc": null, "id": null, "key": "node_77322599ad", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}}], "key": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_EVENT_SERVICE", "name": "ORG_员工管理_保存事件服务", "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_EVENT_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}