{"access": "Private", "key": "TSRM$TSRM_USER_CREATE_WITH_ROLE_ACTION", "name": "用户管理-新建员工自动关联角色", "props": {"bean": "TsrmUserAction", "desc": null, "groovyScript": null, "languageType": "Java", "method": "userCreateWithRole", "order": 10, "requestType": "io.terminus.tsrm.md.spi.model.org.dto.OrgEmployeeBaseMdDTO", "responseType": "java.lang.Void", "returnModel": null, "status": "enabled"}, "type": "Action"}