{"access": "Private", "description": "{}", "key": "TSRM$SRM_ENABLE_EMPLOYEE", "name": "启用员工", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1igbb6hl91", "name": "开始", "nextNodeKey": "node_1igbb79hk3", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}}], "output": null, "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1igbb79hk3", "name": "调用扩展服务", "nextNodeKey": "node_1igbb6hl92", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(sys_common$org_employee_md)request", "implementation": "TSRM$TSRM_USER_ENABLED_EMPLOYEE_ACTION", "implementationName": "用户管理-启用员工", "newAction": true, "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_1igbb6hl92", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}}], "key": "TSRM$SRM_ENABLE_EMPLOYEE", "name": "启用员工", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$SRM_ENABLE_EMPLOYEE_service_perm_ac_50ba69", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}