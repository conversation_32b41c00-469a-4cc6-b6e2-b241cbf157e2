{"access": "Private", "key": "TSRM$ORG_EMPLOYEE_MD_DISABLE", "name": "ORG_员工管理_停用", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "key": "node_1hbcs3epj3", "name": "开始", "nextNodeKey": "node_1hbcs66j37", "preNodeKey": null, "props": {"desc": null, "globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "employeeMd", "fieldKey": "employeeMd", "fieldName": "employeeMd", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "id": null, "required": null}], "name": null, "output": [], "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hbcs66j37", "name": "查询数据", "nextNodeKey": "node_1hbcs43fp5", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"key": "4oCp4n7XjjIATNVyRZ-Xb", "leftValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "rightValues": null, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "employeeMd", "valueName": "employeeMd"}]}, "id": "1hbcs71ne8", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hbcs66j37", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hbcs43fp5", "name": "调用编排服务", "nextNodeKey": "node_1hbcs3epj4", "preNodeKey": null, "props": {"desc": null, "inputMapping": [{"field": {"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "TERP_MIGRATE$org_employee_md"}, "relation": null, "required": true}, "id": "1hbcs7c7f9", "value": {"constValue": null, "fieldType": "Model", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "employeeMd", "valueName": "employeeMd"}]}}], "name": null, "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "TERP_MIGRATE$org_employee_md"}, "relation": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "serviceKey": "TSRM$ORG_EMPLOYEE_DISABLE_SERVICE", "serviceName": "ORG_员工管理_禁用服务", "type": "CallServiceProperties"}, "renderType": null, "type": "CallServiceNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hbcs3epj4", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hbcs3epj3"], "input": [{"defaultValue": null, "description": null, "fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "id": null, "required": null}], "key": "COMMON_2B$ORG_EMPLOYEE_MD_DISABLE", "name": "ORG_员工管理_停用", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_EMPLOYEE_MD_DISABLE_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}