{"access": "Private", "key": "TSRM$COMM_2B_PRU_ORG:list", "name": "list", "props": {"content": {"children": [{"children": [], "key": "brZSSKEnX9N1wRfcS0PT0", "name": "OrgList", "props": {"bizTypeCodes": ["PUR_ORG"], "createOrg": {"permissionKey": "TSRM$COMM_2B_PRU_ORG-list_perm_ac_z_0_createOrg"}, "createOrgTarget": {}, "disableOrg": {"permissionKey": "TSRM$COMM_2B_PRU_ORG-list_perm_ac_z_0_disableOrg"}, "disableOrgTarget": {}, "editOrg": {"permissionKey": "TSRM$COMM_2B_PRU_ORG-list_perm_ac_z_0_editOrg"}, "editOrgTarget": {}, "orgListServiceProps": {"OrgHiddenColFlow": {"serviceKey": "ERP_GEN$ORG_BIZ_TYPE_QUERY_HIDDE_FILED_EVENT_SERVICE", "type": "InvokeService"}, "OrgSearchFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_SEARCH_EVENT_SERVICE", "type": "InvokeService"}, "OrgUnitRoleFlow": {"serviceKey": "ERP_GEN$ORG_ORG_FRONT_ROLE_PAGE_EVENT_SERVICE", "type": "InvokeService"}, "deleteOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_DELETE_EVENT_SERVICE", "type": "InvokeService"}, "disableOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_DISABLE_EVENT_SERVICE", "type": "InvokeService"}, "enableOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_ENABLE_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgBizTypeListFlow": {"serviceKey": "ERP_GEN$ORG_BIZ_TYPE_FIND_ALL_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitByPidFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_QUERY_PID_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitDetailFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_QUERY_DETAIL_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitParentFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_PARENT_PAGE_EVENT_SERVICE", "type": "InvokeService"}, "saveOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_SAVE_EVENT_SERVICE", "type": "InvokeService"}}, "serviceProps": {"OrgHiddenColFlow": {"serviceKey": "ERP_GEN$ORG_BIZ_TYPE_QUERY_HIDDE_FILED_EVENT_SERVICE", "type": "InvokeService"}, "OrgSearchFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_SEARCH_EVENT_SERVICE", "type": "InvokeService"}, "OrgUnitRoleFlow": {"serviceKey": "ERP_GEN$ORG_ORG_FRONT_ROLE_PAGE_EVENT_SERVICE", "type": "InvokeService"}, "deleteOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_DELETE_EVENT_SERVICE", "type": "InvokeService"}, "disableOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_DISABLE_EVENT_SERVICE", "type": "InvokeService"}, "enableOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_ENABLE_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgBizTypeListFlow": {"serviceKey": "ERP_GEN$ORG_BIZ_TYPE_FIND_ALL_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitByPidFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_QUERY_PID_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitDetailFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_QUERY_DETAIL_EVENT_SERVICE", "type": "InvokeService"}, "queryOrgUnitParentFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_PARENT_PAGE_EVENT_SERVICE", "type": "InvokeService"}, "saveOrgUnitFlow": {"serviceKey": "ERP_GEN$ORG_ORG_UNIT_SAVE_EVENT_SERVICE", "type": "InvokeService"}}}, "type": "Widget"}], "key": "L8YOa1nGEzhI35fI-5Od1", "name": "Page", "props": {"params": [], "showFooter": true, "showHeader": false, "title": "采购组织配置"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "terp", "service"]}, "i18nConfig": {}, "key": "TSRM$COMM_2B_PRU_ORG-list", "permissionKey": "TSRM$COMM_2B_PRU_ORG-list_perm_ac", "resources": [{"description": null, "key": "brZSSKEnX9N1wRfcS0PT0/createOrg", "label": "新建", "path": [{"key": "L8YOa1nGEzhI35fI-5Od1", "label": "页面", "type": "Page"}, {"key": "brZSSKEnX9N1wRfcS0PT0", "label": "组织列表", "type": "OrgList"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "brZSSKEnX9N1wRfcS0PT0/editOrg", "label": "编辑", "path": [{"key": "L8YOa1nGEzhI35fI-5Od1", "label": "页面", "type": "Page"}, {"key": "brZSSKEnX9N1wRfcS0PT0", "label": "组织列表", "type": "OrgList"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "brZSSKEnX9N1wRfcS0PT0/disableOrg", "label": "停用", "path": [{"key": "L8YOa1nGEzhI35fI-5Od1", "label": "页面", "type": "Page"}, {"key": "brZSSKEnX9N1wRfcS0PT0", "label": "组织列表", "type": "OrgList"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "brZSSKEnX9N1wRfcS0PT0", "label": "组织列表", "path": [{"key": "L8YOa1nGEzhI35fI-5Od1", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$ORG_ORG_UNIT_QUERY_DETAIL_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_PARENT_PAGE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_BIZ_TYPE_FIND_ALL_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_SAVE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_QUERY_PID_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_DELETE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_DISABLE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_ENABLE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_FRONT_ROLE_PAGE_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_ORG_UNIT_SEARCH_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$ORG_BIZ_TYPE_QUERY_HIDDE_FILED_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}