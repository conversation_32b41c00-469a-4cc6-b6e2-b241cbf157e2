{"access": "Private", "key": "TSRM$ORG_EMPLOYEE_MD_ADM_USER_SAVE_SERVICE", "name": "ORG_员工管理_员工账号信息同步保存数据", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd0bnbqi130", "name": "开始", "nextNodeKey": "node_1hd0ddngs4", "preNodeKey": null, "props": {"desc": null, "globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "user", "fieldKey": "user", "fieldName": "user", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": null}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "员工信息", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "name": null, "output": [], "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd0ddngs4", "name": "赋值", "nextNodeKey": "node_1hd0kmrdd234", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": null}, "valueKey": "TSRM$user", "valueName": "user"}, {"modelAlias": "TSRM$user", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "id": "1hd0de26j6", "operator": "EQ", "value": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TERP_MIGRATE$user"}, "valueKey": "userId", "valueName": "用户"}, {"modelAlias": "TSRM$user", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}}, {"field": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": null}, "valueKey": "TSRM$user", "valueName": "user"}, {"modelAlias": "TSRM$user", "relatedModel": null, "valueKey": "nickname", "valueName": "昵称"}]}, "id": "1hd0de2q87", "operator": "EQ", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "name", "valueName": "姓名"}]}}, {"field": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": null}, "valueKey": "TSRM$user", "valueName": "user"}, {"modelAlias": "TSRM$user", "relatedModel": null, "valueKey": "username", "valueName": "用户名"}]}, "id": "1hd0de3kj8", "operator": "EQ", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "员工编码"}]}}, {"field": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": null}, "valueKey": "TSRM$user", "valueName": "user"}, {"modelAlias": "TSRM$user", "relatedModel": null, "valueKey": "email", "valueName": "用户邮箱"}]}, "id": "1hd0de44e9", "operator": "EQ", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}}, {"field": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": null}, "valueKey": "TSRM$user", "valueName": "user"}, {"modelAlias": "TSRM$user", "relatedModel": null, "valueKey": "mobile", "valueName": "用户手机"}]}, "id": "1hd0e7to9226", "operator": "EQ", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd0kmrdd234", "name": "调用Action", "nextNodeKey": "node_1hd0bnbqi131", "preNodeKey": null, "props": {"desc": null, "implementation": "TSRM$WQ_ORG_USER_UPDATE_ACTION", "implementationName": "员工管理-更新action", "inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, "id": "1hd0kn26q236", "value": {"constValue": null, "fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": null}, "valueKey": "TSRM$user", "valueName": "user"}]}}], "name": null, "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "type": "SPIProperties"}, "renderType": null, "type": "SPINode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd0bnbqi131", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hd0bnbqi130"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "员工信息", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$ORG_EMPLOYEE_MD_ADM_USER_SAVE_SERVICE", "name": "ORG_员工管理_员工账号信息同步保存数据", "output": [{"defaultValue": null, "description": null, "fieldAlias": "user", "fieldKey": "user", "fieldName": "user", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": null}, "relation": null, "required": null}], "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_EMPLOYEE_MD_ADM_USER_SAVE_SERVICE_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}