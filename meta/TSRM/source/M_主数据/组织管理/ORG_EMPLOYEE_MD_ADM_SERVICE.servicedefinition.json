{"access": "Private", "key": "TSRM$ORG_EMPLOYEE_MD_ADM_SERVICE", "name": "ORG_员工管理_分配行政组织", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"aiChatMode": false, "aiRoundsStrategy": null, "aiService": false, "children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcr43gor1", "name": "开始", "nextNodeKey": "node_1hd088dpv10", "preNodeKey": null, "props": {"desc": null, "globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "empOrg", "fieldKey": "empOrg", "fieldName": "空库存组织", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": null}, "relation": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "empType", "fieldKey": "empType", "fieldName": "空公司", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "relation": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "empSave", "fieldKey": "empSave", "fieldName": "组织角色员工保存", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelKey": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelName": null}, "relation": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "emp", "fieldKey": "emp", "fieldName": "emp", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "modelKey": "TERP_MIGRATE$org_employee_org_link_cf", "modelName": null}, "relation": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "modelKey": "TERP_MIGRATE$org_employee_org_link_cf", "modelName": null}, "relation": null, "required": null}, "fieldAlias": "empArr", "fieldKey": "empArr", "fieldName": "empArr", "fieldType": "Array", "id": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": [{"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd08ppcr29", "name": "条件", "nextNodeKey": "node_1hd09202t36", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "3RVs5o9cpGos2IxjpQdwe", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_adm_org_cf"}, "valueKey": "admOrg", "valueName": "行政组织"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_inv_org_cf"}, "valueKey": "extWqInvOrg", "valueName": "需求单位"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "_zE7rf1M_koUJnK1za_54", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "molPhYYJNu_xWWQi2wRCR", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd09202t36", "name": "赋值", "nextNodeKey": "node_1hd0958pd38", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": null}, "valueKey": "invOrg", "valueName": "需求单位"}]}, "id": "1hd0921g937", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": null}, "valueKey": "empOrg", "valueName": "空库存组织"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd0958pd38", "name": "赋值", "nextNodeKey": null, "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "comInfo", "valueName": "所属公司"}]}, "id": "1hd095a1439", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "empType", "valueName": "空公司"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd08ppcr30", "name": "条件", "nextNodeKey": "node_1hd0890f811", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "PzUGivaJRxmg4qUZEA0Sp", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_adm_org_cf"}, "valueKey": "admOrg", "valueName": "行政组织"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_inv_org_cf"}, "valueKey": "extWqInvOrg", "valueName": "需求单位"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "8vWW3uVK-FmGUc976SfL7", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "2BcpvgA40KpeihmkheZoB", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd0890f811", "name": "查询数据", "nextNodeKey": "node_1hd08djm012", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "ZO5rpiWAkQ1RKYhxq1eNV", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_struct_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_adm_org_cf"}, "valueKey": "admOrg", "valueName": "行政组织"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_inv_org_cf"}, "valueKey": "extWqInvOrg", "valueName": "需求单位"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "MQbHbpUoRHrn8b3bopZ2a", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "77FqHQiwaoh_kLQ8EJcpt", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "库存组织配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd08djm012", "name": "赋值", "nextNodeKey": "node_1hd08rk6r31", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": null}, "valueKey": "invOrg", "valueName": "需求单位"}]}, "id": "1hd08dkrj13", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "库存组织配置表"}, "valueKey": "NODE_OUTPUT_node_1hd0890f811", "valueName": "[查询数据]节点.output"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd08rk6r32", "name": "条件", "nextNodeKey": "node_1hd096bjt40", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "zmA6Lzvr5bNsQto2dJbqM", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_inv_org_cf"}, "valueKey": "invOrg", "valueName": "需求单位"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "TERP_MIGRATE$gen_com_type_cf"}, "valueKey": "extWqComInfo", "valueName": "公司信息"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "DHFo-21n0YR2RKUi1IzS3", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "GpO2u1D-v5hZE1uR_lhS-", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd096bjt40", "name": "赋值", "nextNodeKey": null, "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "comInfo", "valueName": "所属公司"}]}, "id": "1hd096cnr41", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "empType", "valueName": "空公司"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd08rk6r33", "name": "条件", "nextNodeKey": "node_1hd08f7gb14", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "OgSGXeh1d3BcOzdDo-XUO", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_inv_org_cf"}, "valueKey": "invOrg", "valueName": "需求单位"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "TERP_MIGRATE$gen_com_type_cf"}, "valueKey": "extWqComInfo", "valueName": "公司信息"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "sbYWewzxig5mQG3e0CvWd", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "4VgQTX-xBfJYwWwd4E6ac", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd08f7gb14", "name": "查询数据", "nextNodeKey": "node_1hd08gd4r15", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "M6EBptPZ4ZQSI9A0R4pDz", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_inv_org_cf"}, "valueKey": "invOrg", "valueName": "需求单位"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "TERP_MIGRATE$gen_com_type_cf"}, "valueKey": "extWqComInfo", "valueName": "公司信息"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "K0Svl4lDyKmNTQxvAm_2N", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "g7TM1PD3VWNzVBaCrgSkK", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd08gd4r15", "name": "赋值", "nextNodeKey": null, "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "comInfo", "valueName": "所属公司"}]}, "id": "1hd08gei616", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}, "valueKey": "NODE_OUTPUT_node_1hd08f7gb14", "valueName": "[查询数据]节点.output"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}], "headNodeKeys": ["node_1hd08rk6r32", "node_1hd08rk6r33"], "id": null, "key": "node_1hd08rk6r31", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}], "headNodeKeys": ["node_1hd08ppcr29", "node_1hd08ppcr30"], "id": null, "key": "node_1hd08ppcr28", "name": "排他分支", "nextNodeKey": "node_1hdg553r74", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hdg553r75", "name": "条件", "nextNodeKey": null, "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "UBzgVb7I0DRCwLmCiMAag", "key": null, "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "ERP_GEN$org_rank_cf", "modelKey": "ERP_GEN$org_rank_cf", "modelName": "TERP_MIGRATE$org_rank_cf"}, "valueKey": "rankId", "valueName": "职级"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "533d0eJeKOXBEfv9SpkkT", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "-vg9PAGjlYL0RDFa6P5Ad", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hdg553r76", "name": "条件", "nextNodeKey": "node_1hdg5anh27", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "52B4TZg2OANw1FEO588X2", "key": null, "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "ERP_GEN$org_rank_cf", "modelKey": "ERP_GEN$org_rank_cf", "modelName": "TERP_MIGRATE$org_rank_cf"}, "valueKey": "rankId", "valueName": "职级"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "VoLHpIbICMORnpRr8bxgQ", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "LT4t8tQJFcSEpEVyJSgv4", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hdg5anh27", "name": "赋值", "nextNodeKey": "node_1hdg5h5gv11", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "modelKey": "TERP_MIGRATE$org_employee_org_link_cf", "modelName": null}, "valueKey": "emp", "valueName": "emp"}, {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "relatedModel": {"modelAlias": "TERP_MIGRATE$org_org_unit_cf", "modelKey": "TERP_MIGRATE$org_org_unit_cf", "modelName": null}, "valueKey": "orgUnitId", "valueName": "组织单元"}, {"modelAlias": "TERP_MIGRATE$org_org_unit_cf", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "id": "1hdg5aolv8", "operator": "EQ", "value": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_adm_org_cf"}, "valueKey": "admOrg", "valueName": "行政组织"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}}, {"field": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "modelKey": "TERP_MIGRATE$org_employee_org_link_cf", "modelName": null}, "valueKey": "emp", "valueName": "emp"}, {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "relatedModel": {"modelAlias": "ERP_GEN$org_rank_md", "modelKey": "ERP_GEN$org_rank_md", "modelName": null}, "valueKey": "rankId", "valueName": "职级"}, {"modelAlias": "ERP_GEN$org_rank_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "id": "1hdg5db4h9", "operator": "EQ", "value": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "ERP_GEN$org_rank_cf", "modelKey": "ERP_GEN$org_rank_cf", "modelName": "TERP_MIGRATE$org_rank_cf"}, "valueKey": "rankId", "valueName": "职级"}, {"modelAlias": "ERP_GEN$org_rank_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}}, {"field": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "modelKey": "TERP_MIGRATE$org_employee_org_link_cf", "modelName": null}, "valueKey": "emp", "valueName": "emp"}, {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "employeeId", "valueName": "orgEmployeeMdId"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "id": "1hdg5duco10", "operator": "EQ", "value": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hd088dpv10", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "TERP_MIGRATE$org_employee_md"}, "valueKey": "orgEmployeeMdId", "valueName": "orgEmployeeMdId"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hdg5h5gv11", "name": "赋值", "nextNodeKey": null, "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": null, "valueKey": "empArr", "valueName": "empArr"}]}, "id": "1hdg5h6l412", "operator": "ADD", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "modelKey": "TERP_MIGRATE$org_employee_org_link_cf", "modelName": null}, "valueKey": "emp", "valueName": "emp"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}], "headNodeKeys": ["node_1hdg553r75", "node_1hdg553r76"], "id": null, "key": "node_1hdg553r74", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}], "headNodeKeys": ["node_1hd08ppcr28"], "id": null, "key": "node_1hd088dpv10", "name": "循环", "nextNodeKey": "node_1hcr47eg84", "preNodeKey": null, "props": {"desc": null, "loopData": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "extWqAdmOrg", "valueName": "行政组织"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "extWqAdmOrg", "fieldKey": "extWqAdmOrg", "fieldName": "行政组织", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "relation": null, "required": null}, "loopType": "DATASET_LOOP", "name": null, "stopWhenDataEmpty": false, "type": "LoopProperties"}, "renderType": null, "type": "LoopNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcr47eg84", "name": "查询数据", "nextNodeKey": "node_1hcr48sdq5", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "Od-yuolbPD5CYOy5xDiD7", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "NXOucpHVwb49TtB6cE8Jq", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "MFsaTmllyGYW1GJeoKuK_", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "id": "1hcr6btb322", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hcr47eg84", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcr48sdq5", "name": "赋值", "nextNodeKey": "node_1hcr44jkj3", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "extWqAdmOrg", "valueName": "行政组织"}]}, "id": "1hcr48tvc6", "operator": "EQ", "value": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_adm_org_cf"}, "valueKey": "extWqAdmOrg", "valueName": "行政组织"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcr44jkj3", "name": "更新数据", "nextNodeKey": "node_1hdg5og1f13", "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hdg5og1f13", "name": "赋值", "nextNodeKey": "node_1hdg6ttee2", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "modelKey": "COMMON_2B$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "employeeOrgLinkList", "valueName": "关联的组织集合"}]}, "id": "1hdg5oh5b14", "operator": "EQ", "value": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "modelKey": "TERP_MIGRATE$org_employee_org_link_cf", "modelName": null}, "valueKey": "empArr", "valueName": "empArr"}]}}, {"field": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "modelKey": "COMMON_2B$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "code", "valueName": "员工编码"}]}, "id": "1hdg5ot5t15", "operator": "EQ", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "员工编码"}]}}, {"field": {"constValue": null, "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "modelKey": "COMMON_2B$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "type", "valueName": "类型"}]}, "id": "1hdg5r2hu16", "operator": "EQ", "value": {"constValue": null, "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "type", "valueName": "员工类型"}]}}, {"field": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "modelKey": "COMMON_2B$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "name", "valueName": "名称"}]}, "id": "1hdg5rdke17", "operator": "EQ", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "name", "valueName": "姓名"}]}}, {"field": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "modelKey": "COMMON_2B$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "email", "valueName": "邮箱"}]}, "id": "1hdg5re6c18", "operator": "EQ", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}}, {"field": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "modelKey": "COMMON_2B$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "mobile", "valueName": "手机号码"}]}, "id": "1hdg5rer619", "operator": "EQ", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}}, {"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "modelKey": "COMMON_2B$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "relatedModel": {"modelAlias": "ERP_GEN$gen_addr_type_cf", "modelKey": "ERP_GEN$gen_addr_type_cf", "modelName": null}, "valueKey": "addressId", "valueName": "地址"}]}, "id": "1hdg5rfeq20", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_addr_type_cf", "modelKey": "ERP_GEN$gen_addr_type_cf", "modelName": "TERP_MIGRATE$gen_addr_type_cf"}, "valueKey": "addressId", "valueName": "地址"}]}}, {"field": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "modelKey": "COMMON_2B$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "addressDetail", "valueName": "详细地址"}]}, "id": "1hdg5s9sm21", "operator": "EQ", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "addressDetail", "valueName": "详细地址"}]}}, {"field": {"constValue": null, "fieldType": "DateTime", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "modelKey": "COMMON_2B$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "entryAt", "valueName": "入职日期"}]}, "id": "1hdg5ubbb22", "operator": "EQ", "value": {"constValue": null, "fieldType": "DateTime", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "entryAt", "valueName": "入职日期"}]}}, {"field": {"constValue": null, "fieldType": "DateTime", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "modelKey": "COMMON_2B$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "COMMON_2B$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "resignationAt", "valueName": "离职日期"}]}, "id": "1hdg5ujed23", "operator": "EQ", "value": {"constValue": null, "fieldType": "DateTime", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "resignationAt", "valueName": "离职日期"}]}}, {"field": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelKey": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "TERP_MIGRATE$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "id", "valueName": "主键"}]}, "id": "1hdg9h1no21", "operator": "EQ", "value": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}}, {"field": {"constValue": null, "fieldType": "Boolean", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelKey": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}, {"modelAlias": "TERP_MIGRATE$ext_wq_employee_save_temp", "relatedModel": null, "valueKey": "isHandleRole", "valueName": "是否处理角色"}]}, "id": "1hdnsr6gv17", "operator": "EQ", "value": {"constValue": "false", "fieldType": "Boolean", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hdg6ttee2", "name": "调用编排服务", "nextNodeKey": "node_1hcr43gor2", "preNodeKey": null, "props": {"desc": null, "inputMapping": [{"field": {"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelKey": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelName": null}, "relation": null, "required": null}, "id": "1hdg6uh3f3", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelKey": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelName": null}, "valueKey": "empSave", "valueName": "组织角色员工保存"}]}}], "name": null, "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "serviceKey": "ERP_GEN$ORG_EMPLOYEE_SAVE_SERVICE", "serviceName": "ORG_员工管理_保存组织角色用户关系", "type": "CallServiceProperties"}, "renderType": null, "type": "CallServiceNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcr43gor2", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hcr43gor1"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$ORG_EMPLOYEE_MD_ADM_SERVICE", "name": "ORG_员工管理_分配行政组织", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "desc": null, "name": null, "permissionKey": "TSRM$ORG_EMPLOYEE_MD_ADM_SERVICE_perm_ac", "teamId": 35, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}