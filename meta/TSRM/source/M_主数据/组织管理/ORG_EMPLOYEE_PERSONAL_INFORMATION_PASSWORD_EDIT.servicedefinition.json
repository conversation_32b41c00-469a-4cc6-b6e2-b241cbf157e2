{"access": "Private", "key": "TSRM$ORG_EMPLOYEE_PERSONAL_INFORMATION_PASSWORD_EDIT", "name": "ORG_个人信息_修改密码", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd10569u1", "name": "开始", "nextNodeKey": "node_1hd1062j13", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "员工用户临时模型", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$ext_wq_employee_user_temp", "modelKey": "TSRM$ext_wq_employee_user_temp", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd1062j13", "name": "赋值", "nextNodeKey": "node_1hd106ld95", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TSRM$ext_wq_employee_user_temp", "modelKey": "TSRM$ext_wq_employee_user_temp", "modelName": null}, "valueKey": "request", "valueName": "员工用户临时模型"}, {"modelAlias": "TSRM$ext_wq_employee_user_temp", "relatedModel": null, "valueKey": "userId", "valueName": "用户id"}]}, "id": "1hd1063lq4", "operator": "EQ", "value": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "SYS", "valueName": "系统变量"}, {"modelAlias": null, "relatedModel": null, "valueKey": "CurrentUserId", "valueName": "当前登录人id"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd106ld95", "name": "调用Action", "nextNodeKey": "node_1hd10569u2", "preNodeKey": null, "props": {"desc": null, "implementation": "TSRM$WQ_ORG_EMPLOYEE_PASSWORD_EDIT_ACTION", "implementationName": "员工管理-个人信息-修改密码", "inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, "id": "1hd106q9s7", "value": {"constValue": null, "fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TSRM$ext_wq_employee_user_temp", "modelKey": "TSRM$ext_wq_employee_user_temp", "modelName": null}, "valueKey": "request", "valueName": "员工用户临时模型"}]}}], "name": null, "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "type": "SPIProperties"}, "renderType": null, "type": "SPINode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd10569u2", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hd10569u1"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "员工用户临时模型", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$ext_wq_employee_user_temp", "modelKey": "TSRM$ext_wq_employee_user_temp", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$ORG_EMPLOYEE_PERSONAL_INFORMATION_PASSWORD_EDIT", "name": "ORG_个人信息_修改密码", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_EMPLOYEE_PERSONAL_INFORMATION_PASSWORD_EDIT_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}