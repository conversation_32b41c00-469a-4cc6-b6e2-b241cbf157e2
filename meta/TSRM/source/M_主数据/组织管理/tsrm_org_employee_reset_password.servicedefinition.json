{"access": "Private", "description": "{}", "key": "TSRM$tsrm_org_employee_reset_password", "name": "员工管理-重置密码", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1ifhc22501", "name": "开始", "nextNodeKey": "node_1ifhd7g7e5", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Number", "id": null}], "output": null, "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1ifhd7g7e5", "name": "查询数据", "nextNodeKey": "node_1ifhcm2pc3", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "Wpl9OVHSFcHBR1BhMpIgN", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "sys_common$org_employee_md", "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "HuBLJfDT143id4dOOz4G2", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "NdrxVK04aJErqbMkc2yo2", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": null, "dataType": "MODEL", "desensitized": true, "dynamicCondition": null, "maximum": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_employee_md", "queryFields": [{"fieldKey": "userId"}]}, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表"}, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [], "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"desc": null, "id": null, "key": "node_1ifhcm2pc3", "name": "调用扩展服务", "nextNodeKey": "node_1ifhc22502", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "GLOBAL.(sys_common$org_employee_md)NODE_OUTPUT_node_1ifhd7g7e5", "implementation": "TSRM$TSRM_USER_RESET_PASSWORD_ACTION", "implementationName": "用户管理-重置密码", "newAction": true, "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_1ifhc22502", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Number", "id": null}], "key": "TSRM$tsrm_org_employee_reset_password", "name": "员工管理-重置密码", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$tsrm_org_employee_reset_password_service_perm_ac_f2f8cf", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}