{"access": "Private", "description": "{}", "key": "TSRM$ORG_ORGR_ROLE_SAVE_EVENT_SERVICE", "name": "ORG_组织角色_保存事件服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "TB2B$ext_wq_org_role_cf", "name": "组织角色配置表"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$ORG_ORGR_ROLE_SAVE_EVENT_perm_ac", "relations": [{"actionType": "Validation", "code": "TB2B$ext_wq_org_role_cf:0b92f4a7-8a62-4ed5-97eb-46e4b4a267ab", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": "TERP_MIGRATE$ORG_ORGR_VERIFY_SERVICE", "sourceCode": null}, {"actionType": "ServiceDefinition", "code": "TERP_MIGRATE$ORG_ORGR_VERIFY_SERVICE", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": "TSRM$SYS_SaveDataService", "sourceCode": null}, {"actionType": "ServiceDefinition", "code": "TSRM$SYS_SaveDataService", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": null, "sourceCode": null}], "returnModel": null, "returnModelArrayWhether": false, "states": []}, "isDeleted": null, "isEnabled": true, "modelKey": "TB2B$ext_wq_org_role_cf", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "start0", "name": "开始节点", "nextNodeKey": "AssignNode0", "props": {"globalVariable": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "eventCode", "fieldKey": "eventCode", "fieldName": "eventCode", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "param", "fieldKey": "param", "fieldName": "param", "fieldType": "Model", "id": null, "modelKey": "TB2B$ext_wq_org_role_cf", "relatedModel": {"modelAlias": "TB2B$ext_wq_org_role_cf", "modelKey": "TB2B$ext_wq_org_role_cf", "modelName": "TB2B$ext_wq_org_role_cf"}, "relation": null, "required": null}], "fieldAlias": "event", "fieldKey": "event", "fieldName": "event", "fieldType": "Object", "id": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "TB2B$ext_wq_org_role_cf", "relatedModel": {"modelAlias": "TB2B$ext_wq_org_role_cf", "modelKey": "TB2B$ext_wq_org_role_cf", "modelName": "TB2B$ext_wq_org_role_cf"}, "relation": null, "required": true}], "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "AssignNode0", "name": "赋值", "nextNodeKey": "SPINodeKey", "props": {"assignments": [{"field": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "GLOBAL"}, {"fieldType": null, "valueKey": "event", "valueName": "event"}, {"fieldType": null, "valueKey": "eventCode", "valueName": "eventCode"}]}, "id": null, "operator": "EQ", "value": {"constValue": "TSRM$ORG_ORGR_ROLE_SAVE_EVENT", "fieldType": null, "id": null, "scope": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}, {"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "GLOBAL"}, {"fieldType": null, "valueKey": "event", "valueName": "event"}, {"fieldType": null, "valueKey": "param", "valueName": "param"}]}, "id": null, "operator": "EQ", "value": {"fieldType": null, "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "REQUEST"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}, {"desc": null, "id": null, "key": "SPINodeKey", "name": "调用线下代码", "nextNodeKey": "end0", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": null, "implementation": "EventServiceExecute", "implementationName": null, "inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "event", "fieldKey": "event", "fieldName": "event", "fieldType": "Object", "id": null, "required": null}, "id": null, "value": {"fieldType": null, "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "event", "valueName": "event"}]}}], "newAction": false, "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "ActionResponse", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_SPINodeKey", "valueName": "出参结构体"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}]}}], "outputAssignType": "CUSTOM"}, "relatedModel": null, "transactionPropagation": null, "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "end0", "name": "结束节点", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["start0"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "TB2B$ext_wq_org_role_cf", "relatedModel": {"modelAlias": "TB2B$ext_wq_org_role_cf", "modelKey": "TB2B$ext_wq_org_role_cf", "modelName": "TB2B$ext_wq_org_role_cf"}, "relation": null, "required": true}], "key": "TSRM$ORG_ORGR_ROLE_SAVE_EVENT_SERVICE", "name": "ORG_组织角色_保存事件服务", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "TSRM$ORG_ORGR_ROLE_SAVE_EVENT_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}