{"access": "Private", "key": "TSRM$COMM_2B_IVENTORY_ADDRESS:list", "name": "list", "props": {"containerSelect": {"COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail": [], "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form": [], "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf": []}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "permissionKey": "TSRM$COMM_2B_IVENTORY_ADDRESS-list_perm_ac_z_0_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_inv_loc_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "TERP_MIGRATE$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-TERP_MIGRATE$org_inv_loc_cf-multi-delete", "label": "批量删除", "permissionKey": "TSRM$COMM_2B_IVENTORY_ADDRESS-list_perm_ac_z_0_0_0_1_items_0"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ivR8nbX-9vBqTW6uzW8ga", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "Xjb7fz4K0J22-JqvZ_-GA", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "code", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "NronQTwWQkCNlZAtv3ws-", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "name", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "f8izWe6FuVe6aNCs0574a", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}], "flow": {"containerKey": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_inv_loc_cf"}}], "serviceKey": "TERP_MIGRATE$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showFilterFields": true, "showScope": "all", "tableCondition": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "KqxnROpVA4benODXPvfxJ", "name": "Status", "props": {"text$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.extWqStatus)?.text", "type$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.extWqStatus)?.type"}}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"库存详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_inv_loc_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TERP_MIGRATE$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_IVENTORY_ADDRESS-list_perm_ac_z_0_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "状态", "type": "VarValue", "val": "extWqStatus", "value": "TERP_MIGRATE$org_inv_loc_cf.extWqStatus", "valueType": "VAR", "varVal": "extWqStatus", "varValue": [{"valueKey": "extWqStatus", "valueName": "extWqStatus"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_IVENTORY_ADDRESS-list_perm_ac_z_0_1_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "OaInzGFEzf0t6I6UvLfeJ", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail"}, "type": "action"}}], "service": "TSRM$ORG_INV_LOC_CF_DISABLE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$COMM_2B_IVENTORY_ADDRESS-list_perm_ac_z_0_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"id": "sb3AWgQo3ihGIs_696r7X", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "状态", "type": "VarValue", "val": "extWqStatus", "value": "TERP_MIGRATE$org_inv_loc_cf.extWqStatus", "valueType": "VAR", "varVal": "extWqStatus", "varValue": [{"valueKey": "extWqStatus", "valueName": "extWqStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "-u_-JMZoWF5ZsHlThLTLF", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "BjqYI5oxdeWrtXUniB5ZP", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ZqEzFM1UGNECot2c3y6KQ", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail"}, "type": "action"}}], "service": "TSRM$ORG_INV_LOC_CF_ENABLE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$COMM_2B_IVENTORY_ADDRESS-list_perm_ac_z_0_1_1_0_1_0_3", "showCondition": {"conditions": [{"conditions": [{"id": "3iTHx9DMkLoYUa5cTDVNO", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "状态", "type": "VarValue", "val": "extWqStatus", "value": "TERP_MIGRATE$org_inv_loc_cf.extWqStatus", "valueType": "VAR", "varVal": "extWqStatus", "varValue": [{"valueKey": "extWqStatus", "valueName": "extWqStatus"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "t2SRPFCDCFdJJg9qQGQ1e", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "ZDp8dKuJqg-NwfxSQlNXQ", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_IVENTORY_ADDRESS-list_perm_ac_z_0_1_1_0_1_0_4", "showCondition": {"conditions": [{"conditions": [{"id": "b1qyGpGy1GWwEV3mMcWW1", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "状态", "type": "VarValue", "val": "extWqStatus", "value": "TERP_MIGRATE$org_inv_loc_cf.extWqStatus", "valueType": "VAR", "varVal": "extWqStatus", "varValue": [{"valueKey": "extWqStatus", "valueName": "extWqStatus"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "vgeWdI9NWgL6mjccUVVt1", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detail-TERP_MIGRATE$org_inv_loc_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-code", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "组织编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-name", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "组织名称", "name": "name", "type": "TEXT"}}, {"children": [], "key": "5VIfhiUNdZfzSc_taZwtj", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "shortName", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "组织简称", "name": "shortName", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "JVVUvqH62Dodasz30v9WJ", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "extWqInvOrg", "label": "选择需求单位", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editable": false, "initialValue": null, "label": "所属库存组织", "name": "extWqInvOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "sqKHQFyXA2qOmbmG8msoA", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "extWqWarehouseType", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "仓库类型", "name": "extWqWarehouseType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "iLp81fITZ-m2Su714emO6", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "extWqContacts", "label": "选择联系人", "labelField": "name", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "initialValue": null, "label": "联系人", "name": "extWqContacts", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-area", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "area", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "区号", "name": "area", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-tele", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "tele", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "电话/分机号", "name": "tele", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-addressId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "displayComponentType": "RelationShow", "editable": false, "label": "地区", "name": "addressId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-addressDetail", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "详细地址", "name": "addressDetail", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-postcode", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "postcode", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "邮编", "name": "postcode", "type": "TEXT"}}, {"children": [], "key": "rlz39k7EX8jkyFLk5-40H", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "extWqStatus", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "状态", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false}, "key": "FCHme5vToz41DoCp756fn", "valueRules": null}], "name": "extWqStatus", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "3rJS99DWJ8SbaHEqBJJfc", "name": "DetailGroupItem", "props": {"title": ""}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_IVENTORY_ADDRESS-zs2BQ0b7rFQKfwM3VtWwg", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "serviceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TERP_MIGRATE$org_inv_loc_cf"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"库存\")) : \"新建库存\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": true, "initialValue": null, "label": "ID", "lookup": [{"fieldRules": {"hidden": true, "readOnly": true, "required": false}, "key": "uM9OdpsZVQlfYcV2vG5Sv", "valueRules": null}], "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-code", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "组织编码", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "iqVnBSWev-o4gDmv2yo-n", "operator": null, "valueRules": null}], "name": "code", "rules": [{"message": "请输入组织编码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-name", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "组织名称", "name": "name", "rules": [{"message": "请输入组织名称", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "s6yb2RQEGs4E3i6zkTOCd", "name": "FormField", "props": {"componentProps": {"fieldAlias": "shortName", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "组织简称", "name": "shortName", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "3RLIUsBGQjz9mg69dLVKJ", "name": "FormField", "props": {"componentProps": {"fieldAlias": "extWqInvOrg", "label": "选择需求单位", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId", "extWqNccCode", "extWqSapCodeWq", "extWqNcCodeWq", "extWqK3CodeWq", "extWqComType", "extAbbr", "extEstBank", "extEstName", "extBankAcct"], "fieldAlias": "comOrgId", "label": "选择所属公司组织", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "所属公司组织", "name": "comOrgId", "required": false, "type": "OBJECT", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "mainField": "name", "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "label": "所属库存组织", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "8RJiEDtpcf-Y2U4cEDV1c", "operator": null, "valueRules": null}], "name": "extWqInvOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "I556QSfOwQH1xfXhKtyS_", "name": "FormField", "props": {"componentProps": {"fieldAlias": "extWqWarehouseType", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "仓库类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "Oh788rH12o1m2mpLIrlQ6", "valueRules": null}], "name": "extWqWarehouseType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-addressId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_addr_type_cf"}}], "serviceKey": "ERP_GEN$SYS_FindTreeChildrenDataService", "type": "InvokeSystemService"}, "labelField": ["addrName"], "leafOnly": false, "modelAlias": "ERP_GEN$gen_addr_type_cf", "reverseConstructFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}, "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "SelfRelation", "hidden": false, "label": "地区", "name": "addressId", "rules": [{"message": "请输入地址", "required": true}], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-addressDetail", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "详细地址", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": false, "required": true}, "key": "C_eMfJCo8Idvo3BlgRzCp", "operator": null, "valueRules": null}], "name": "addressDetail", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "eID66Enuhu8ggNu59REKX", "name": "FormField", "props": {"componentProps": {"fieldAlias": "extWqContacts", "label": "选择联系人", "labelField": "name", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_employee_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "员工编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "姓名", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "手机", "name": "mobile", "required": true, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "员工编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "姓名", "name": "name", "required": true, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}], "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "mainField": "name", "modelAlias": "sys_common$org_employee_md", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "label": "仓库处理人", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "j1j6oe7kccA_DHQu_2lzH", "valueRules": null}], "name": "extWqContacts", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-area", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "area", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "区号", "name": "area", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-tele", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "tele", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "电话", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "WjmjzlgFjcPz2jDJ8uDbO", "valueRules": null}], "name": "tele", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "J3z73xKsLtgAWJRHq8C8T", "name": "FormField", "props": {"componentProps": {"fieldAlias": "postcode", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "邮编", "name": "postcode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "NyPOZbS8p2sf961bkQRZF", "name": "FormField", "props": {"componentProps": {"fieldAlias": "extWqStatus", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "initialValue": "INACTIVE", "label": "状态", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "DPKIqvhBAkyvvo_Vpyk63", "valueRules": null}], "name": "extWqStatus", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "vYPD0ry49t4Vz1ylBKQ0B", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": ""}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_inv_loc_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_inv_loc_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route?.query?.copyId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TERP_MIGRATE$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "TERP_MIGRATE$org_inv_loc_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_IVENTORY_ADDRESS-list_perm_ac_z_0_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form"}, "type": "action"}}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"type": "const", "value": "TERP_MIGRATE$org_inv_loc_cf"}}], "service": "TERP_MIGRATE$SYS_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf"}, {"action": "Message", "message": "保存成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_IVENTORY_ADDRESS-list_perm_ac_z_0_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "仓库管理"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["", "地区", "请输入组织名称", "联系人", "状态", "电话/分机号", "停用成功", "保存", "姓名", "请输入ID", "选择需求单位", "ID", "创建人", "启用成功", "所属库存组织", "请选择", "更新时间", "库存", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.extWqStatus)?.text", "编辑", "选择创建人", "组织简称", "邮编", "确认删除吗？", "所属公司组织", "员工编码", "组织名称", "复制", "选择更新人", "手机", "表格", "系统信息", "新建", "删除成功", "新建库存", "停用", "电话", "批量删除", "删除", "详细地址", "邮箱", "启用", "批量操作", "仓库类型", "保存成功", "选择所属公司组织", "库存详情", "请输入组织编码", "组织编码", "主体信息", "请输入地址", "确认启用吗？", "选择地址", "选择联系人", "更新人", "请输入", "区号", "取消", "仓库处理人", "创建时间", "确认停用吗？"], "i18nScanPaths": ["JVVUvqH62Dodasz30v9WJ.props.componentProps.placeholder", "eID66Enuhu8ggNu59REKX.props.editComponentProps.fields.1.componentProps.placeholder", "5VIfhiUNdZfzSc_taZwtj.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-addressDetail.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-createdAt.props.label", "iLp81fITZ-m2Su714emO6.props.componentProps.label", "I556QSfOwQH1xfXhKtyS_.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-name.props.rules.0.message", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch.props.label", "NyPOZbS8p2sf961bkQRZF.props.label", "eID66Enuhu8ggNu59REKX.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-addressId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch.props.items.0.actionConfig.endLogicOtherConfig.1.message", "ZqEzFM1UGNECot2c3y6KQ.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-code.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-tele.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-code.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-action-save.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs.props.items.0.label", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.fields.2.componentProps.label", "eID66Enuhu8ggNu59REKX.props.editComponentProps.fields.3.componentProps.placeholder", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-addressId.props.componentProps.label", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.fields.0.label", "OaInzGFEzf0t6I6UvLfeJ.props.actionConfig.endLogicOtherConfig.1.message", "eID66Enuhu8ggNu59REKX.props.editComponentProps.filterFields.0.componentProps.placeholder", "NyPOZbS8p2sf961bkQRZF.props.componentProps.placeholder", "JVVUvqH62Dodasz30v9WJ.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-tele.props.componentProps.placeholder", "s6yb2RQEGs4E3i6zkTOCd.props.componentProps.placeholder", "rlz39k7EX8jkyFLk5-40H.props.componentProps.placeholder", "s6yb2RQEGs4E3i6zkTOCd.props.label", "eID66Enuhu8ggNu59REKX.props.editComponentProps.filterFields.1.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-createdBy.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-tele.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-area.props.label", "NronQTwWQkCNlZAtv3ws-.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-addressDetail.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf.props.filterFields.0.componentProps.placeholder", "eID66Enuhu8ggNu59REKX.props.editComponentProps.fields.2.label", "NronQTwWQkCNlZAtv3ws-.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-name.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-addressId.props.componentProps.label", "JVVUvqH62Dodasz30v9WJ.props.componentProps.label", "iLp81fITZ-m2Su714emO6.props.componentProps.placeholder", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.filterFields.1.label", "eID66Enuhu8ggNu59REKX.props.componentProps.placeholder", "sqKHQFyXA2qOmbmG8msoA.props.label", "Xjb7fz4K0J22-JqvZ_-GA.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-code.props.rules.0.message", "OaInzGFEzf0t6I6UvLfeJ.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "ivR8nbX-9vBqTW6uzW8ga.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "eID66Enuhu8ggNu59REKX.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-code.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-addressDetail.props.componentProps.placeholder", "vYPD0ry49t4Vz1ylBKQ0B.props.title", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.fields.1.label", "ZqEzFM1UGNECot2c3y6KQ.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "3RLIUsBGQjz9mg69dLVKJ.props.componentProps.label", "eID66Enuhu8ggNu59REKX.props.editComponentProps.fields.3.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-name.props.componentProps.placeholder", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-tele.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-updatedBy.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-addressId.props.label", "sqKHQFyXA2qOmbmG8msoA.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-delete.props.label", "eID66Enuhu8ggNu59REKX.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-id.props.label", "3rJS99DWJ8SbaHEqBJJfc.props.title", "J3z73xKsLtgAWJRHq8C8T.props.componentProps.placeholder", "3RLIUsBGQjz9mg69dLVKJ.props.componentProps.placeholder", "@exp:COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page-title.props.title", "@exp:COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-page-title.props.title", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-name.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-id.props.rules.0.message", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs.props.items.1.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-id.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf.props.filterFields.1.componentProps.placeholder", "eID66Enuhu8ggNu59REKX.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-area.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-name.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf.props.filterFields.1.label", "rlz39k7EX8jkyFLk5-40H.props.label", "Xjb7fz4K0J22-JqvZ_-GA.props.componentProps.placeholder", "3RLIUsBGQjz9mg69dLVKJ.props.label", "OaInzGFEzf0t6I6UvLfeJ.props.label", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch.props.items.0.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-action-cancel.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-copy.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-edit.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-code.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-addressDetail.props.componentProps.placeholder", "eID66Enuhu8ggNu59REKX.props.label", "ivR8nbX-9vBqTW6uzW8ga.props.label", "eID66Enuhu8ggNu59REKX.props.componentProps.label", "iLp81fITZ-m2Su714emO6.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-updatedBy.props.componentProps.label", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-updatedAt.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-addressId.props.rules.0.message", "eID66Enuhu8ggNu59REKX.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-area.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-new.props.label", "eID66Enuhu8ggNu59REKX.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf.props.filterFields.0.label", "KqxnROpVA4benODXPvfxJ.props.text$", "5VIfhiUNdZfzSc_taZwtj.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-addressId.props.label", "I556QSfOwQH1xfXhKtyS_.props.componentProps.placeholder", "J3z73xKsLtgAWJRHq8C8T.props.label", "3RLIUsBGQjz9mg69dLVKJ.props.editComponentProps.filterFields.1.componentProps.placeholder", "ZqEzFM1UGNECot2c3y6KQ.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-addressId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-area.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-action-save.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-postcode.props.label", "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-postcode.props.componentProps.placeholder"]}, "key": "TSRM$COMM_2B_IVENTORY_ADDRESS-list", "permissionKey": "TSRM$COMM_2B_IVENTORY_ADDRESS-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_inv_loc_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-new", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch/items/COMMON_2B$COMM_2B_IVENTORY_ADDRESS-TERP_MIGRATE$org_inv_loc_cf-multi-delete", "label": "批量删除", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "TERP_MIGRATE$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_inv_loc_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "ivR8nbX-9vBqTW6uzW8ga", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf", "label": "表格", "type": "Table"}, {"key": "f8izWe6FuVe6aNCs0574a", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "Xjb7fz4K0J22-JqvZ_-GA", "label": "组织编码", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf", "label": "表格", "type": "Table"}, {"key": "f8izWe6FuVe6aNCs0574a", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "NronQTwWQkCNlZAtv3ws-", "label": "组织名称", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-list-TERP_MIGRATE$org_inv_loc_cf", "label": "表格", "type": "Table"}, {"key": "f8izWe6FuVe6aNCs0574a", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_inv_loc_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_inv_loc_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_inv_loc_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_inv_loc_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_inv_loc_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "OaInzGFEzf0t6I6UvLfeJ", "label": "停用", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$ORG_INV_LOC_CF_DISABLE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ZqEzFM1UGNECot2c3y6KQ", "label": "启用", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$ORG_INV_LOC_CF_ENABLE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detail-TERP_MIGRATE$org_inv_loc_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-zs2BQ0b7rFQKfwM3VtWwg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-zs2BQ0b7rFQKfwM3VtWwg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-zs2BQ0b7rFQKfwM3VtWwg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-zs2BQ0b7rFQKfwM3VtWwg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-code", "label": "组织编码", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-name", "label": "组织名称", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "s6yb2RQEGs4E3i6zkTOCd", "label": "组织简称", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "3RLIUsBGQjz9mg69dLVKJ", "label": "所属库存组织", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "I556QSfOwQH1xfXhKtyS_", "label": "仓库类型", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-addressId", "label": "地区", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindTreeChildrenDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_ReverseConstructTreeService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-addressDetail", "label": "详细地址", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "eID66Enuhu8ggNu59REKX", "label": "仓库处理人", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-area", "label": "区号", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-field-tele", "label": "电话", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "J3z73xKsLtgAWJRHq8C8T", "label": "邮编", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "NyPOZbS8p2sf961bkQRZF", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-z58_5Otz56yw-K_1ulBqd", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-editView-TERP_MIGRATE$org_inv_loc_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "vYPD0ry49t4Vz1ylBKQ0B", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-code", "label": "组织编码", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-name", "label": "组织名称", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "5VIfhiUNdZfzSc_taZwtj", "label": "组织简称", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "JVVUvqH62Dodasz30v9WJ", "label": "所属库存组织", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "sqKHQFyXA2qOmbmG8msoA", "label": "仓库类型", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "iLp81fITZ-m2Su714emO6", "label": "联系人", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-area", "label": "区号", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-tele", "label": "电话/分机号", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-addressId", "label": "地区", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-addressDetail", "label": "详细地址", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-field-postcode", "label": "邮编", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "rlz39k7EX8jkyFLk5-40H", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_IVENTORY_ADDRESS-uo4tNfW2o8BMiVFOB1XK5", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_IVENTORY_ADDRESS-detailView-TERP_MIGRATE$org_inv_loc_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "3rJS99DWJ8SbaHEqBJJfc", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}