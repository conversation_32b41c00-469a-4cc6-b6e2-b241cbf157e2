{"access": "Private", "key": "TSRM$ext_wq_employee_user_temp", "name": "员工用户临时模型", "props": {"alias": "COMMON_2B$ext_wq_employee_user_temp", "children": [{"alias": "userId", "appId": 105307, "key": "user_id", "name": "用户id", "props": {"autoGenerated": false, "columnName": "user_id", "comment": "用户id", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": false, "required": false, "unique": false}, "teamId": 1, "type": "DataStructField"}, {"alias": "originalPwd", "appId": 105307, "key": "original_pwd", "name": "原始密码", "props": {"autoGenerated": false, "columnName": "original_pwd", "comment": "原始密码", "compositeKey": false, "desensitizedRule": "DES_PASSWORD", "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 32, "required": false, "unique": false}, "teamId": 1, "type": "DataStructField"}, {"alias": "userPwd", "appId": 105307, "key": "user_pwd", "name": "新密码", "props": {"autoGenerated": false, "columnName": "user_pwd", "comment": "新密码", "compositeKey": false, "desensitizedRule": "DES_PASSWORD", "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 32, "required": false, "unique": false}, "teamId": 1, "type": "DataStructField"}], "desc": null, "name": "员工用户临时模型", "props": {"config": {"persist": false, "self": false, "selfRelationFieldAlias": null, "system": false}, "mainField": "user_id", "mainFieldAlias": "userId", "physicalDelete": false, "searchModel": false, "tableName": "ext_wq_employee_user_temp", "type": "VIEW"}}, "type": "Model", "validations": [{"access": "Private", "key": "TSRM$ext_wq_employee_user_temp:b6578601-8ad6-4aae-9352-4f4c8fa1c04e", "name": "字段必填&长度校验", "props": {"defaultValidation": true, "fieldValidations": [{"constraints": [{"constraintType": "SIZE", "max": 32, "min": 0}], "field": "originalPwd", "fieldType": "TEXT", "message": "原始密码的长度不能超过:32", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 32, "min": 0}], "field": "userPwd", "fieldType": "TEXT", "message": "新密码的长度不能超过:32", "regexp": null, "required": false}], "links": null}, "type": "Validation"}]}