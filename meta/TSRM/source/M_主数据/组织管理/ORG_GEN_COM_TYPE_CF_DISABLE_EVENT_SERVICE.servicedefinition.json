{"access": "Private", "description": "{}", "key": "TSRM$ORG_GEN_COM_TYPE_CF_DISABLE_EVENT_SERVICE", "name": "ORG_公司管理_禁用服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "ERP_GEN$gen_com_type_cf", "name": "公司配置表"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$ORG_GEN_COM_TYPE_CF_DISABLE_EVENT_perm_ac", "relations": null, "returnModel": null, "returnModelArrayWhether": false, "states": [{"appId": 66008, "conditions": null, "fieldKey": "status", "modelKey": "ERP_GEN$gen_com_type_cf", "module": "TERP_MIGRATE$d36d05c0-9b91-4c9f-94c9-16273908fe6d", "moduleName": "基础", "sourceState": "ENABLED", "targetState": "DISABLED", "teamId": 35}]}, "isDeleted": null, "isEnabled": true, "modelKey": "ERP_GEN$gen_com_type_cf", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_68a9132167", "name": "开始", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}}], "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_0b0e944a28", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}}], "key": "TSRM$ORG_GEN_COM_TYPE_CF_DISABLE_EVENT_SERVICE", "name": "ORG_公司管理_禁用服务", "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$ORG_GEN_COM_TYPE_CF_DISABLE_EVENT_perm_ac", "schedulerJob": null, "stateMachine": {"stateVerify": false, "states": [{"conditionGroup": null, "fieldKey": "status", "modelKey": "ERP_GEN$gen_com_type_cf", "sourceState": "ENABLED", "targetState": "DISABLED"}]}, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}