{"access": "Private", "key": "TSRM$ORG_EMPLOYEE_BIND_USER_CHECK", "name": "ORG_员工管理_绑定用户检查", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hdq3l5ng1", "name": "开始", "nextNodeKey": "node_1hdq3mnam3", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "入参", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hdq3mnam3", "name": "查询数据", "nextNodeKey": "node_1hdq3nrg14", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "PMykEKMtOd6rs_GxIcImA", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "userId", "valueName": "用户"}, {"modelAlias": "TSRM$user", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "入参"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TERP_MIGRATE$user"}, "valueKey": "userId", "valueName": "用户"}, {"modelAlias": "TSRM$user", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "fiKdI02Zvwan_4gCzo2bi", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "NQkkXXo1WQsu1nwE1eVkl", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "ARRAY", "desc": null, "maximum": 10, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hdq4uk3l1", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$EMPLOYEE_USER_EXISTED", "errorMsg": "绑定的用户${name}已被其他员工${empname}绑定，请选择的绑定的用户是否正确！\n", "link": "MetaLink$ErrorCode$COMMON_2B$EMPLOYEE_USER_EXISTED", "name": null, "placeholderMapping": [{"id": null, "key": "name", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "入参"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TERP_MIGRATE$user"}, "valueKey": "userId", "valueName": "用户"}, {"modelAlias": "TSRM$user", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "username", "valueName": "用户名"}]}}, {"id": null, "key": "empname", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hdq3nrg14", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "name", "valueName": "姓名"}]}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hdq4uk3l1"], "id": null, "key": "node_1hdq3nrg14", "name": "循环", "nextNodeKey": "node_1hdq3l5ng2", "preNodeKey": null, "props": {"desc": null, "loopData": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hdq3mnam3", "valueName": "[查询数据]节点.output"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "NODE_OUTPUT_node_1hdq3mnam3", "fieldKey": "NODE_OUTPUT_node_1hdq3mnam3", "fieldName": "[查询数据]节点.output", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "relation": null, "required": null}, "loopType": "DATASET_LOOP", "name": null, "stopWhenDataEmpty": false, "type": "LoopProperties"}, "renderType": null, "type": "LoopNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hdq3l5ng2", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hdq3l5ng1"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "入参", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$ORG_EMPLOYEE_BIND_USER_CHECK", "name": "ORG_员工管理_绑定用户检查", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_EMPLOYEE_BIND_USER_CHECK_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}