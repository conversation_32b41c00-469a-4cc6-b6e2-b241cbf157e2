{"access": "Private", "key": "TSRM$COMM_2B_PUR_ANGENCY:list", "name": "list", "props": {"conditionGroups": {"wQl_OkTvE_t9Ezbd8YAC9": {"conditions": [], "id": "wQl_OkTvE_t9Ezbd8YAC9", "logicOperator": "OR", "type": "ConditionGroup"}}, "containerSelect": {"COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail": [{"field": "employees", "selectFields": []}], "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form": [{"field": "employees", "selectFields": []}], "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf": [], "MmgxvU574MjPjjH50hAYn": []}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "permissionKey": "TSRM$COMM_2B_PUR_ANGENCY-list_perm_ac_z_0_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_purchase_agent_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "TB2B$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-TERP_MIGRATE$ext_wq_purchase_agent_cf-multi-delete", "label": "批量删除", "permissionKey": "TSRM$COMM_2B_PUR_ANGENCY-list_perm_ac_z_0_0_0_1_items_0"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ylvlnBOlsXgp2Q0Ag9Krs", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "0Lnt32jborTZQASwuNOzg", "name": "Field", "props": {"componentProps": {"fieldAlias": "agentCom", "label": "选择代理公司", "labelField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "parentModelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "name", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "公司名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "dM48o13Spept4zoICtzHj", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "代理公司", "name": "agentCom", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "47ecZClsDQ9ZazTKI1A-L", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "shareRatio", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请输入", "precision": 2}, "displayComponentProps": {"precision": 4}, "displayComponentType": "Number", "hidden": false, "label": "服务费比例（%）", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "name": "shareRatio", "type": "NUMBER", "width": 144}, "type": "Widget"}], "key": "UucKWOPCXQdUvnzAi9p_r", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "editComponentProps": {"fields": []}, "editComponentType": "InputText", "filterType": "fuzzy", "hidden": false, "label": "代理公司编码", "name": "agentCom.code", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "editComponentProps": {"fields": []}, "editComponentType": "InputText", "filterType": "fuzzy", "hidden": false, "label": "代理公司名称", "name": "agentCom.name", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf", "context$": "$context", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_purchase_agent_cf"}}], "serviceKey": "TB2B$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [], "id": "wQl_OkTvE_t9Ezbd8YAC9", "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "qK5pi4vZ58Ai6_IggX42B", "name": "Status", "props": {"text$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "type$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.type"}}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"采购代理公司配置表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_purchase_agent_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TB2B$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_PUR_ANGENCY-list_perm_ac_z_0_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_PUR_ANGENCY-list_perm_ac_z_0_1_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_purchase_agent_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TB2B$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$COMM_2B_PUR_ANGENCY-list_perm_ac_z_0_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_purchase_agent_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TB2B$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$COMM_2B_PUR_ANGENCY-list_perm_ac_z_0_1_1_0_1_0_3", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "IS_NOT_NULL", "rightValue": null}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_PUR_ANGENCY-list_perm_ac_z_0_1_1_0_1_0_4", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detail-TERP_MIGRATE$ext_wq_purchase_agent_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "X2CrrSguQYtCylKdAXk0Q", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请输入", "precision": null}, "editable": false, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "Z8JcwWAXzoStXkVPF7fW_", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "agentCom", "label": "选择代理公司", "labelField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "parentModelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "hidden": false, "initialValue": null, "label": "代理公司", "name": "agentCom", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "G9r8SpMKkeYxgG1vl3Jvk", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "shareRatio", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请输入", "precision": 2}, "displayComponentProps": {"precision": 4}, "displayComponentType": "Number", "editable": false, "hidden": false, "initialValue": null, "label": "服务费比例", "name": "shareRatio", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "qXJb5MZWOB5XrfGU1_tB8", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "I06c9I12dStwhL12gylKI", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "initialValue": null, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "deYmL3oM5ZPmV2HQUrPHW", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "initialValue": null, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [{"children": [{"children": [{"children": [], "key": "8XxYa7P7mP76aduHGIM9P", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "u7de1309EhrCqFOiWFjaZ", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "dqgw3djdgpw1O_PcJ5tv3", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "KOWxGm-k7H9PpzkNssPCt", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_purchase_agent_employee_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "4rtTIt1y_hSdgdcyEBQ8A", "name": "Field", "props": {"componentProps": {"fieldAlias": "employee", "label": "选择员工", "labelField": "name", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "TB2B$ext_wq_purchase_agent_employee_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "code", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "员工编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "YiECLSgHQoQUa5WlscP64", "trigger": "auto", "valueRules": null}], "name": "code", "type": "TEXT"}, {"componentProps": {"fieldAlias": "name", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "姓名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "a9ORoyF5Yp2xNvIPH1F50", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT"}, {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "editComponentProps": {}, "editComponentType": "Select", "label": "状态", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "c1aqc4Ym1TB65bTqnjbSS", "trigger": "auto", "valueRules": null}], "name": "status", "type": "SELECT"}, {"componentProps": {"fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "editComponentProps": {}, "editComponentType": "Select", "label": "员工类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "b8hjf1O8gvI-dO5WRqJP0", "trigger": "auto", "valueRules": null}], "name": "type", "type": "SELECT"}, {"componentProps": {"fieldAlias": "mobile", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "手机", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "it0_iZqxFETKzBD8xhhBA", "trigger": "auto", "valueRules": null}], "name": "mobile", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "员工", "name": "employee", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "rPpupttlbU8_QsvgxCaEZ", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "initialValue": null, "label": "员工类型", "name": "employee.type", "required": true, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "LBiGYu7v3rn5djUIl80Y9", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "initialValue": null, "label": "员工编码", "name": "employee.code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "XMKySldthXynkQ0X90IRz", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "initialValue": null, "label": "手机", "name": "employee.mobile", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "mn0KqipvKGazRD7j9qZRo", "name": "Fields", "props": {}, "type": "Meta"}], "key": "LRQc3eaj5J50bD2kFks0k", "name": "Table", "props": {"enableSolution": false, "fieldName": "employees", "flow": {"context$": "$context", "name": "employees", "type": "RelationData"}, "label": "表格", "mode": "simple", "modelAlias": "TB2B$ext_wq_purchase_agent_employee_cf"}, "type": "Container"}], "key": "u7VJpCZrMskqik3WyrFro", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "label": "自定义详情字段"}, "type": "Meta"}], "key": "EpxILsPh5bBz0plOunSUA", "name": "DetailGroupItem", "props": {"title": "采购代理人员"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "context$": "$context", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_purchase_agent_cf"}}], "serviceKey": "TB2B$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TB2B$ext_wq_purchase_agent_cf"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"采购代理公司配置表\")) : \"新建采购代理公司配置表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "U6RUq0PLbcZtZ_Xzo4oVh", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "5_9baDllgBD8Oo8v032fT", "name": "FormField", "props": {"componentProps": {"fieldAlias": "agentCom", "label": "选择代理公司", "labelField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "parentModelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "公司编码", "name": "code", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "公司名称", "name": "name", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "socialcreditCode", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "统一社会信用代码", "name": "socialcreditCode", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司编码", "name": "code", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司名称", "name": "name", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "mainField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "代理公司", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "QGkrQWgYKZmpho9Im3VDp", "operator": null, "valueRules": null}], "name": "agentCom", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "DXJ2hF6Nx5UPAlogqTzwB", "name": "FormField", "props": {"componentProps": {"fieldAlias": "comName", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "公司名称", "lookup": [{"action": "get", "fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "0YLT_N9NVeqY_FNgd_wmw", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "agentCom.name", "value": "TB2B$ext_wq_purchase_agent_cf.agentCom.name", "valueType": "model"}}], "name": "comName", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "35dVILrxrHiCLucbEhQ8e", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "shareRatio", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请输入", "precision": 2}, "displayComponentProps": {"precision": 2}, "displayComponentType": "Number", "editComponentProps": {"precision": 2, "range": [1, 100]}, "editComponentType": "InputNumber", "hidden": false, "label": "服务费比例（%）", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "x4mRT82eYNzYcje_ibGPT", "operator": null, "valueRules": null}], "name": "shareRatio", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "Qht50jwoQLgoYx2DVteFY", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "initialValue": "INACTIVE", "label": "状态", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "Jz-HHLBYf1j6z32tC746E", "operator": null, "valueRules": null}], "name": "status", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "MmgxvU574MjPjjH50hAYn", "name": "ModalRelationSelect", "props": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "userId", "label": "选择用户", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "用户", "name": "userId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "员工编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "姓名", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "员工类型", "name": "type", "required": true, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "手机", "name": "mobile", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "required": true, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["addrCode", "addrName", "postCode", "counId", "<PERSON><PERSON><PERSON><PERSON>", "addrParentId"], "fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "地址", "name": "addressId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": true, "initialValue": null, "label": "详细地址", "name": "addressDetail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "entryAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "入职日期", "name": "entryAt", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "resignationAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "离职日期", "name": "resignationAt", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "extWqSignature", "modelAlias": "sys_common$org_employee_md", "placeholder": "请上传"}, "hidden": true, "initialValue": null, "label": "签名图", "name": "extWqSignature", "required": false, "type": "ATTACHMENT", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "userId", "label": "选择用户", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "用户", "name": "userId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "员工编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "姓名", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "状态", "name": "status", "required": true, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "员工类型", "name": "type", "required": true, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": true, "initialValue": null, "label": "邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": true, "initialValue": null, "label": "手机", "name": "mobile", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["addrCode", "addrName", "postCode", "counId", "<PERSON><PERSON><PERSON><PERSON>", "addrParentId"], "fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "地址", "name": "addressId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": true, "initialValue": null, "label": "详细地址", "name": "addressDetail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "entryAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "入职日期", "name": "entryAt", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "resignationAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "离职日期", "name": "resignationAt", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "extWqSignature", "modelAlias": "sys_common$org_employee_md", "placeholder": "请上传"}, "hidden": true, "initialValue": null, "label": "签名图", "name": "extWqSignature", "required": false, "type": "ATTACHMENT", "width": 120}], "flow": {"containerKey": "MmgxvU574MjPjjH50hAYn", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}], "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "mainField": "name", "mode": "multiple", "modelAlias": "sys_common$org_employee_md", "relationAlias": "employee", "showFilterFields": true, "showScope": "all", "tableAlias": "employees", "tableCondition": null, "title": "选择代理人员"}, "type": "Widget"}], "key": "zo08RRHZDCOXCJSw1ETG8", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "il_i3MAZGsFS0Lg06CYV4", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_purchase_agent_employee_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "r0TiNos0PSafGBVgXJkjR", "name": "Field", "props": {"componentProps": {"fieldAlias": "employee", "label": "选择员工", "labelField": "name", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "TB2B$ext_wq_purchase_agent_employee_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "userId", "label": "选择用户", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "用户", "name": "userId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "员工编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "姓名", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "required": true, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "员工类型", "name": "type", "required": true, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "手机", "name": "mobile", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["addrCode", "addrName", "postCode", "counId", "<PERSON><PERSON><PERSON><PERSON>", "addrParentId"], "fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "地址", "name": "addressId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "详细地址", "name": "addressDetail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "entryAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "入职日期", "name": "entryAt", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "resignationAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "离职日期", "name": "resignationAt", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "extWqSignature", "modelAlias": "sys_common$org_employee_md", "placeholder": "请上传"}, "hidden": false, "initialValue": null, "label": "签名图", "name": "extWqSignature", "required": false, "type": "ATTACHMENT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "mainField": "name", "modelAlias": "sys_common$org_employee_md", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "员工名称", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "3QI12giWnTJHVUIPe8VOJ", "operator": null, "valueRules": null}], "name": "employee", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "iBjupkTC42ucLZLZ80RmJ", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "员工编码", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "y62hSmYeoNJaG9wp9kVUi", "operator": null, "valueRules": null}], "name": "employee.code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "vE1Reg4U89Zrya8Y2F9VO", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentProps": {"filterFields": []}, "editComponentType": "Select", "hidden": false, "label": "员工类型", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "Gd544YTztHz-72OFj-tmf", "operator": null, "valueRules": null}], "name": "employee.type", "required": true, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "n2qYAFUrPE3fWswD2zWvz", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentProps": {"fields": [], "filterFields": []}, "editComponentType": "InputText", "label": "手机", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "NjIr7LmgYBKgRM4PqDYer", "operator": null, "valueRules": null}], "name": "employee.mobile", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "iMf9xhk0PgOf8kyb8vXDz", "name": "Fields", "props": {}, "type": "Meta"}], "key": "x6P_FgHSqiSiD4SJOAHx6", "name": "TableForm", "props": {"fieldName": "employees", "fields": [], "hideCreator": true, "label": "表格表单", "modelAlias": "TB2B$ext_wq_purchase_agent_employee_cf"}, "type": "Widget"}], "key": "SrrudvfJYhIfv7OqL7y0N", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "-FPIDKFnei75TnI-kk81j", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "采购代理人员"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "params$": "{ id: route.recordId }", "serviceKey": "TB2B$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "TB2B$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "TB2B$ext_wq_purchase_agent_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_PUR_ANGENCY-list_perm_ac_z_0_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "TB2B$ext_wq_purchase_agent_cf", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form"}, "type": "action"}}], "service": "TSRM$ORG_PURCHASE_AGENT_CF_SAVE_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PUR_ANGENCY-page"}, {"action": "Message", "message": "保存成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_PUR_ANGENCY-list_perm_ac_z_0_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "采购代理"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["用户", "采购代理公司配置表详情", "状态", "员工", "停用成功", "保存", "姓名", "员工名称", "地址", "请上传", "ID", "创建人", "代理公司编码", "启用成功", "自定义详情字段", "逻辑删除标识", "采购代理公司配置表", "服务费比例", "请选择", "版本号", "采购代理人员", "服务费比例（%）", "更新时间", "新建采购代理公司配置表", "代理公司名称", "编辑", "选择创建人", "统一社会信用代码", "确认删除吗？", "员工编码", "复制", "选择更新人", "手机", "表格", "新建", "删除成功", "公司名称", "停用", "批量删除", "删除", "详细地址", "入职日期", "离职日期", "邮箱", "启用", "签名图", "批量操作", "代理公司", "表格表单", "保存成功", "确认启用吗？", "选择地址", "选择代理公司", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "更新人", "选择用户", "请输入", "取消", "公司编码", "创建时间", "选择员工", "员工类型", "确认停用吗？"], "i18nScanPaths": ["U6RUq0PLbcZtZ_Xzo4oVh.props.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.17.label", "5_9baDllgBD8Oo8v032fT.props.editComponentProps.fields.2.componentProps.placeholder", "n2qYAFUrPE3fWswD2zWvz.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-action-cancel.props.label", "4rtTIt1y_hSdgdcyEBQ8A.props.editComponentProps.fields.2.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.8.label", "vE1Reg4U89Zrya8Y2F9VO.props.componentProps.placeholder", "DXJ2hF6Nx5UPAlogqTzwB.props.label", "r0TiNos0PSafGBVgXJkjR.props.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.5.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.14.componentProps.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.7.componentProps.placeholder", "Z8JcwWAXzoStXkVPF7fW_.props.componentProps.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf.props.label", "LBiGYu7v3rn5djUIl80Y9.props.label", "X2CrrSguQYtCylKdAXk0Q.props.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-batch.props.items.0.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.14.componentProps.placeholder", "5_9baDllgBD8Oo8v032fT.props.editComponentProps.fields.2.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.12.componentProps.placeholder", "vE1Reg4U89Zrya8Y2F9VO.props.label", "4rtTIt1y_hSdgdcyEBQ8A.props.editComponentProps.fields.1.label", "5_9baDllgBD8Oo8v032fT.props.editComponentProps.fields.0.componentProps.placeholder", "5_9baDllgBD8Oo8v032fT.props.componentProps.placeholder", "qXJb5MZWOB5XrfGU1_tB8.props.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.1.componentProps.label", "4rtTIt1y_hSdgdcyEBQ8A.props.editComponentProps.fields.4.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-disable.props.label", "n2qYAFUrPE3fWswD2zWvz.props.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-disable.props.actionConfig.endLogicOtherConfig.1.message", "47ecZClsDQ9ZazTKI1A-L.props.label", "qK5pi4vZ58Ai6_IggX42B.props.text$", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.9.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-batch.props.label", "Z8JcwWAXzoStXkVPF7fW_.props.componentProps.placeholder", "x6P_FgHSqiSiD4SJOAHx6.props.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.12.label", "5_9baDllgBD8Oo8v032fT.props.label", "5_9baDllgBD8Oo8v032fT.props.editComponentProps.filterFields.1.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.7.componentProps.label", "0Lnt32jborTZQASwuNOzg.props.editComponentProps.fields.0.label", "Z8JcwWAXzoStXkVPF7fW_.props.label", "0Lnt32jborTZQASwuNOzg.props.componentProps.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.3.label", "X2CrrSguQYtCylKdAXk0Q.props.label", "XMKySldthXynkQ0X90IRz.props.componentProps.placeholder", "LRQc3eaj5J50bD2kFks0k.props.label", "47ecZClsDQ9ZazTKI1A-L.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf.props.filterFields.0.label", "I06c9I12dStwhL12gylKI.props.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-action-save.props.actionConfig.endLogicOtherConfig.1.message", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.3.componentProps.placeholder", "Qht50jwoQLgoYx2DVteFY.props.label", "ylvlnBOlsXgp2Q0Ag9Krs.props.componentProps.placeholder", "4rtTIt1y_hSdgdcyEBQ8A.props.componentProps.label", "4rtTIt1y_hSdgdcyEBQ8A.props.editComponentProps.fields.4.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.10.componentProps.placeholder", "-FPIDKFnei75TnI-kk81j.props.title", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.9.label", "4rtTIt1y_hSdgdcyEBQ8A.props.editComponentProps.fields.0.componentProps.placeholder", "U6RUq0PLbcZtZ_Xzo4oVh.props.label", "I06c9I12dStwhL12gylKI.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-copy.props.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.11.label", "deYmL3oM5ZPmV2HQUrPHW.props.label", "4rtTIt1y_hSdgdcyEBQ8A.props.editComponentProps.fields.3.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.2.componentProps.placeholder", "0Lnt32jborTZQASwuNOzg.props.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-edit.props.label", "5_9baDllgBD8Oo8v032fT.props.editComponentProps.filterFields.0.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.11.componentProps.placeholder", "@exp:COMMON_2B$COMM_2B_PUR_ANGENCY-editView-page-title.props.title", "G9r8SpMKkeYxgG1vl3Jvk.props.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.16.componentProps.placeholder", "5_9baDllgBD8Oo8v032fT.props.editComponentProps.fields.1.label", "iBjupkTC42ucLZLZ80RmJ.props.label", "u7VJpCZrMskqik3WyrFro.props.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.10.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.8.componentProps.placeholder", "qXJb5MZWOB5XrfGU1_tB8.props.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.15.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-disable.props.actionConfig.beforeLogicConfig.0.text", "4rtTIt1y_hSdgdcyEBQ8A.props.editComponentProps.fields.2.label", "5_9baDllgBD8Oo8v032fT.props.editComponentProps.fields.1.componentProps.placeholder", "4rtTIt1y_hSdgdcyEBQ8A.props.editComponentProps.fields.1.componentProps.placeholder", "5_9baDllgBD8Oo8v032fT.props.editComponentProps.fields.0.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.13.label", "5_9baDllgBD8Oo8v032fT.props.editComponentProps.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-enable.props.label", "0Lnt32jborTZQASwuNOzg.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "LBiGYu7v3rn5djUIl80Y9.props.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.16.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.6.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-action-save.props.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.1.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.4.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.18.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.17.componentProps.placeholder", "il_i3MAZGsFS0Lg06CYV4.props.componentProps.placeholder", "iBjupkTC42ucLZLZ80RmJ.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-enable.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf.props.filterFields.0.componentProps.placeholder", "deYmL3oM5ZPmV2HQUrPHW.props.componentProps.placeholder", "KOWxGm-k7H9PpzkNssPCt.props.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "35dVILrxrHiCLucbEhQ8e.props.componentProps.placeholder", "G9r8SpMKkeYxgG1vl3Jvk.props.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf.props.filterFields.1.label", "EpxILsPh5bBz0plOunSUA.props.title", "il_i3MAZGsFS0Lg06CYV4.props.label", "Qht50jwoQLgoYx2DVteFY.props.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.componentProps.label", "XMKySldthXynkQ0X90IRz.props.label", "r0TiNos0PSafGBVgXJkjR.props.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.4.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.15.label", "KOWxGm-k7H9PpzkNssPCt.props.componentProps.placeholder", "4rtTIt1y_hSdgdcyEBQ8A.props.componentProps.placeholder", "rPpupttlbU8_QsvgxCaEZ.props.componentProps.placeholder", "4rtTIt1y_hSdgdcyEBQ8A.props.label", "5_9baDllgBD8Oo8v032fT.props.componentProps.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-enable.props.actionConfig.endLogicOtherConfig.1.message", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.2.componentProps.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-batch.props.items.0.actionConfig.endLogicOtherConfig.1.message", "@exp:COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page-title.props.title", "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-new.props.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.7.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.18.componentProps.placeholder", "35dVILrxrHiCLucbEhQ8e.props.label", "ylvlnBOlsXgp2Q0Ag9Krs.props.label", "0Lnt32jborTZQASwuNOzg.props.editComponentProps.fields.0.componentProps.placeholder", "5_9baDllgBD8Oo8v032fT.props.editComponentProps.filterFields.1.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.14.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.6.label", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.5.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.13.componentProps.placeholder", "r0TiNos0PSafGBVgXJkjR.props.editComponentProps.fields.0.componentProps.placeholder", "DXJ2hF6Nx5UPAlogqTzwB.props.componentProps.placeholder", "4rtTIt1y_hSdgdcyEBQ8A.props.editComponentProps.fields.0.label", "rPpupttlbU8_QsvgxCaEZ.props.label", "4rtTIt1y_hSdgdcyEBQ8A.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-delete.props.label", "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf.props.filterFields.1.componentProps.placeholder"]}, "key": "TSRM$COMM_2B_PUR_ANGENCY-list", "permissionKey": "TSRM$COMM_2B_PUR_ANGENCY-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "TB2B$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_purchase_agent_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-new", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-batch/items/COMMON_2B$COMM_2B_PUR_ANGENCY-TERP_MIGRATE$ext_wq_purchase_agent_cf-multi-delete", "label": "批量删除", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "TB2B$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_purchase_agent_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ylvlnBOlsXgp2Q0Ag9Krs", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf", "label": "表格", "type": "Table"}, {"key": "UucKWOPCXQdUvnzAi9p_r", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "0Lnt32jborTZQASwuNOzg", "label": "代理公司", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf", "label": "表格", "type": "Table"}, {"key": "UucKWOPCXQdUvnzAi9p_r", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "47ecZClsDQ9ZazTKI1A-L", "label": "服务费比例（%）", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-list-TERP_MIGRATE$ext_wq_purchase_agent_cf", "label": "表格", "type": "Table"}, {"key": "UucKWOPCXQdUvnzAi9p_r", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "TB2B$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_purchase_agent_cf"}, "type": "SystemService"}, {"key": "TB2B$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_purchase_agent_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "TB2B$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_purchase_agent_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "U6RUq0PLbcZtZ_Xzo4oVh", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "5_9baDllgBD8Oo8v032fT", "label": "代理公司", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "DXJ2hF6Nx5UPAlogqTzwB", "label": "公司名称", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "35dVILrxrHiCLucbEhQ8e", "label": "服务费比例（%）", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "Qht50jwoQLgoYx2DVteFY", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "X2CrrSguQYtCylKdAXk0Q", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}], "relations": [], "type": "Container"}, {"description": null, "key": "Z8JcwWAXzoStXkVPF7fW_", "label": "代理公司", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "G9r8SpMKkeYxgG1vl3Jvk", "label": "服务费比例", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}], "relations": [], "type": "Container"}, {"description": null, "key": "qXJb5MZWOB5XrfGU1_tB8", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}], "relations": [], "type": "Container"}, {"description": null, "key": "I06c9I12dStwhL12gylKI", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}], "relations": [], "type": "Container"}, {"description": null, "key": "deYmL3oM5ZPmV2HQUrPHW", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$ORG_PURCHASE_AGENT_CF_SAVE_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TB2B$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_purchase_agent_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-disable", "label": "停用", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TB2B$SYS_MasterData_DisableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_purchase_agent_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-enable", "label": "启用", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TB2B$SYS_MasterData_EnableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_purchase_agent_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detail-TERP_MIGRATE$ext_wq_purchase_agent_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "x6P_FgHSqiSiD4SJOAHx6", "label": "表格表单", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "-FPIDKFnei75TnI-kk81j", "label": "采购代理人员", "type": "FormGroupItem"}, {"key": "SrrudvfJYhIfv7OqL7y0N", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "LRQc3eaj5J50bD2kFks0k", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}, {"key": "EpxILsPh5bBz0plOunSUA", "label": "采购代理人员", "type": "DetailGroupItem"}, {"key": "u7VJpCZrMskqik3WyrFro", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "MmgxvU574MjPjjH50hAYn", "label": "选择关联关系", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "-FPIDKFnei75TnI-kk81j", "label": "采购代理人员", "type": "FormGroupItem"}, {"key": "SrrudvfJYhIfv7OqL7y0N", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "x6P_FgHSqiSiD4SJOAHx6", "label": "表格表单", "type": "TableForm"}, {"key": "zo08RRHZDCOXCJSw1ETG8", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "il_i3MAZGsFS0Lg06CYV4", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "-FPIDKFnei75TnI-kk81j", "label": "采购代理人员", "type": "FormGroupItem"}, {"key": "SrrudvfJYhIfv7OqL7y0N", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "x6P_FgHSqiSiD4SJOAHx6", "label": "表格表单", "type": "TableForm"}, {"key": "iMf9xhk0PgOf8kyb8vXDz", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "r0TiNos0PSafGBVgXJkjR", "label": "员工名称", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "-FPIDKFnei75TnI-kk81j", "label": "采购代理人员", "type": "FormGroupItem"}, {"key": "SrrudvfJYhIfv7OqL7y0N", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "x6P_FgHSqiSiD4SJOAHx6", "label": "表格表单", "type": "TableForm"}, {"key": "iMf9xhk0PgOf8kyb8vXDz", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "iBjupkTC42ucLZLZ80RmJ", "label": "员工编码", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "-FPIDKFnei75TnI-kk81j", "label": "采购代理人员", "type": "FormGroupItem"}, {"key": "SrrudvfJYhIfv7OqL7y0N", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "x6P_FgHSqiSiD4SJOAHx6", "label": "表格表单", "type": "TableForm"}, {"key": "iMf9xhk0PgOf8kyb8vXDz", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "vE1Reg4U89Zrya8Y2F9VO", "label": "员工类型", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "-FPIDKFnei75TnI-kk81j", "label": "采购代理人员", "type": "FormGroupItem"}, {"key": "SrrudvfJYhIfv7OqL7y0N", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "x6P_FgHSqiSiD4SJOAHx6", "label": "表格表单", "type": "TableForm"}, {"key": "iMf9xhk0PgOf8kyb8vXDz", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "n2qYAFUrPE3fWswD2zWvz", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-editView-TERP_MIGRATE$ext_wq_purchase_agent_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "-FPIDKFnei75TnI-kk81j", "label": "采购代理人员", "type": "FormGroupItem"}, {"key": "SrrudvfJYhIfv7OqL7y0N", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "x6P_FgHSqiSiD4SJOAHx6", "label": "表格表单", "type": "TableForm"}, {"key": "iMf9xhk0PgOf8kyb8vXDz", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "KOWxGm-k7H9PpzkNssPCt", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}, {"key": "EpxILsPh5bBz0plOunSUA", "label": "采购代理人员", "type": "DetailGroupItem"}, {"key": "u7VJpCZrMskqik3WyrFro", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "LRQc3eaj5J50bD2kFks0k", "label": "表格", "type": "Table"}, {"key": "mn0KqipvKGazRD7j9qZRo", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "4rtTIt1y_hSdgdcyEBQ8A", "label": "员工", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}, {"key": "EpxILsPh5bBz0plOunSUA", "label": "采购代理人员", "type": "DetailGroupItem"}, {"key": "u7VJpCZrMskqik3WyrFro", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "LRQc3eaj5J50bD2kFks0k", "label": "表格", "type": "Table"}, {"key": "mn0KqipvKGazRD7j9qZRo", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "rPpupttlbU8_QsvgxCaEZ", "label": "员工类型", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}, {"key": "EpxILsPh5bBz0plOunSUA", "label": "采购代理人员", "type": "DetailGroupItem"}, {"key": "u7VJpCZrMskqik3WyrFro", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "LRQc3eaj5J50bD2kFks0k", "label": "表格", "type": "Table"}, {"key": "mn0KqipvKGazRD7j9qZRo", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "LBiGYu7v3rn5djUIl80Y9", "label": "员工编码", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}, {"key": "EpxILsPh5bBz0plOunSUA", "label": "采购代理人员", "type": "DetailGroupItem"}, {"key": "u7VJpCZrMskqik3WyrFro", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "LRQc3eaj5J50bD2kFks0k", "label": "表格", "type": "Table"}, {"key": "mn0KqipvKGazRD7j9qZRo", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "XMKySldthXynkQ0X90IRz", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PUR_ANGENCY-detailView-TERP_MIGRATE$ext_wq_purchase_agent_cf-detail", "label": "详情", "type": "Detail"}, {"key": "EpxILsPh5bBz0plOunSUA", "label": "采购代理人员", "type": "DetailGroupItem"}, {"key": "u7VJpCZrMskqik3WyrFro", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "LRQc3eaj5J50bD2kFks0k", "label": "表格", "type": "Table"}, {"key": "mn0KqipvKGazRD7j9qZRo", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}