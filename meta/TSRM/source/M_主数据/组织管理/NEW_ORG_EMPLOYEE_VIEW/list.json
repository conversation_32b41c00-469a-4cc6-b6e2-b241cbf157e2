{"access": "Private", "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:list", "name": "list", "props": {"content": {"children": [{"children": [{"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-bJ2LEi734ZxAJahh-H-FL", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-D13T6jdM70Y6IXdh3GJ9o", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-FKe3ED5lvaFYAu1zW4I62", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-DlWZ3EDkFwNAtLg35vBkZ", "name": "ActionsGroup", "props": {"params": []}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "员工信息表列表"}, "type": "Meta"}, {"children": [{"children": [{"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1-button-add", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:edit", "name": "edit", "type": "View"}, "refresh": true, "type": "NewPage"}}]}, "label": "新建", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1-button-add_perm_ac", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1-button-batch-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否删除选中单据？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "ids", "fieldName": "ids", "fieldType": "Array", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "name": "ids", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "sys_common$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md"]}, {"action": "Message", "level": "success", "message": "删除成功!"}], "executeLogic": "BindService"}, "confirmOn": "off", "disabled$": "mode === \"design\" ? false : $context.selectedKeys?.length === 0", "isMultiple": true, "label": "删除"}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-lNG_L_wulE514frwx8h1H", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-gayHLyQe0efXDmZqnvRj4", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-QWih_L-6VmxnPXAdWCoVh", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-view", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:detail", "name": "detail", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "label": "查看", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-view_perm_ac"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:edit", "name": "detail", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "label": "编辑", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-edit_perm_ac"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW--record-actions-1-button-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "valueConfig": {"expression": "record", "type": "expression"}}], "service": "TSRM$SRM_DISABLE_EMPLOYEE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": ["TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md"]}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "label": "停用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW--record-actions-1-button-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "valueConfig": {"expression": "record", "type": "expression"}}], "service": "TSRM$SRM_ENABLE_EMPLOYEE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": ["TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md"]}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "label": "启用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "record?.id", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "sys_common$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md"]}, {"action": "Message", "level": "success", "message": "删除成功!"}], "executeLogic": "BindService"}, "label": "删除"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-YmGuny4fAbnX7bnx1xAlM", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "actionId": "b63fGLvYoVo_IhC_GpMOu", "id": "b63fGLvYoVo_IhC_GpMOu-0", "text": "是否确认重置密码？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "valueConfig": {"expression": "record.id", "type": "expression"}}], "service": "TSRM$tsrm_org_employee_reset_password"}, "endLogicOtherConfig": [{"action": "Message", "actionId": "iAU8r7F9OKdhfcHIMPu4W", "id": "iAU8r7F9OKdhfcHIMPu4W-0", "level": "success", "message": "密码重置成功！"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "重置密码", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-z1dVxbH6QVxFaL8s3pI0V", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "actionId": "4DhFQbET0Tlmp5hHRoFRI", "id": "4DhFQbET0Tlmp5hHRoFRI-0", "openViewConfig": {"page": {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL", "name": "绑定角色 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"expression": "record.userId.id", "name": "userId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "绑定角色", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-z1dVxbH6QVxFaL8s3pI0V_perm_ac", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-FD6pjRAnS7P1ht6R-8r3P", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-toolbar-actions-1", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-crbnZ5gtWergOYBHWqVl2", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-b0hwNqsMxP5RoDbaTDfuK", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-23arSU28mCkWwxtUVCzIx", "name": "Field", "props": {"componentProps": {"fieldAlias": "code", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "员工编码", "name": "code", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-dItcx9n4tn6EwR7rzSmAE", "name": "Field", "props": {"componentProps": {"fieldAlias": "userId", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "sys_common$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Jtld8bU0zlzDHwlqct9Y8", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "editComponentType": "RelationSelect", "hidden": false, "label": "用户", "name": "userId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-tvedUDBmAtMT2oJq47ruB", "name": "Field", "props": {"componentProps": {"fieldAlias": "name", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "姓名", "name": "name", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-0TyCG6rvvHbUi_eS1-e7B", "name": "Field", "props": {"componentProps": {"fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "editComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "hidden": false, "label": "员工类型", "name": "type", "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-eRuJ6prrS10IWP-2_QjOK", "name": "Field", "props": {"componentProps": {"fieldAlias": "mobile", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "手机", "name": "mobile", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-GKmaXPmqlsAKriDFnrmS4", "name": "Field", "props": {"componentProps": {"fieldAlias": "email", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "邮箱", "name": "email", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-S_VmcdAvJaRuOJHI5MZcM", "name": "Field", "props": {"componentProps": {"fieldAlias": "orgStructId", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Q0MTl91A4agm6G1nIE_3A", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "editComponentType": "RelationSelect", "hidden": false, "label": "组织", "name": "orgStructId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-xeCcUhwJ5IkSHUkOtgigm", "name": "Field", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "editComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "hidden": false, "label": "状态", "name": "status", "type": "SELECT", "width": 116}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-VekVeddyWs5RT4PykJRdA", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": true, "filterFields": [{"componentProps": {"fieldAlias": "code", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "员工编码", "name": "code", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "name", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "姓名", "name": "name", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "mobile", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "手机", "name": "mobile", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "orgStructId", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "jq_VTmLIxpIzTUG69wIVQ", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "editComponentType": "RelationSelect", "hidden": false, "label": "组织", "name": "orgStructId", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "editComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "hidden": false, "label": "员工类型", "name": "type", "type": "SELECT", "width": 116}, {"componentProps": {"fieldAlias": "email", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "邮箱", "name": "email", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "userId", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "sys_common$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "mv2nVrzDCQRkUG-9GxQTQ", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "editComponentType": "RelationSelect", "hidden": false, "label": "用户", "name": "userId", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "editComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "editComponentType": "Select", "hidden": false, "label": "状态", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "UbsW36Lfklwfveo1aAhNE", "trigger": "auto", "valueRules": null}], "name": "status", "type": "SELECT", "width": 120}], "flow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_EMPLOYEE_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "label": "新员工管理", "mode": "normal", "modelAlias": "sys_common$org_employee_md", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "selectType": "multiple", "serviceKey": "sys_common$SYS_PagingDataService", "showFilterFields": true, "showType": "normal", "subTableConfig": {}, "tableConditionContext$": null, "toolbar": {"search": false}}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Xy_808JN6g3AmEG5-QOgq", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "roleIds", "fieldName": "roleIds", "fieldType": "Array", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "type": "expression"}}, {"fieldAlias": "userId", "fieldName": "userId", "fieldType": "Number", "valueConfig": {"expression": "params.userId", "type": "expression"}}], "service": "sys_common$API_TRANTOR_PORTAL_ROLE_RELATION_USER_FLUSH_POST"}, "endLogicOtherConfig": [{"action": "Close", "actionId": "F14YM4zatmDof0cOAfqnz", "id": "F14YM4zatmDof0cOAfqnz-0", "target": ["TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL"]}, {"action": "RefreshTab", "actionId": "U-vWd3q-wMr971wE9e9gK", "id": "U-vWd3q-wMr971wE9e9gK-1", "light": false, "target": ["current"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-vQxHiq_YDI2wh5UU6goEr", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-llKrq9Wi2SC2PgVUejqDB", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-WrAE5GmCdzNiHhD1L9jb3", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-jdE45H-hLBe72oVo20xGj", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-sBucabrrQ4y1OVbnS7Vkh", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-3UuV7qokYMqwHCR8kVW_G", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-h4fxm-rXRvblQ2bFnk0q2", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-AjDMeFy8hto6e9t0YH60W", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-jI6O9_jfvu5U83GrQAREA", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-3jJLEEncqHpaBR7oWE32C", "name": "Field", "props": {"componentProps": {"fieldAlias": "name", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "角色名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "sg026ay5Yi_ckzSyqkSmN", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-3UTs0N5-1n3di8rNor0N7", "name": "Field", "props": {"componentProps": {"fieldAlias": "key", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "角色标识", "name": "key", "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-8cfFlyauvxbBVDFo4p3wL", "name": "Field", "props": {"componentProps": {"fieldAlias": "desc", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "角色描述", "name": "desc", "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-_epNLnG3-eQ41lsjR0JUQ", "name": "Field", "props": {"align": "right", "componentProps": {"fieldAlias": "sourceApplicationId", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {"precisionDisplayType": "fill-round"}, "displayComponentType": "Number", "editComponentProps": {}, "label": "所属门户", "lookup": [{"fieldRules": {"hidden": true, "required": false}, "key": "fa3LDf09iSsy1hA3ZUuHW", "trigger": "auto", "valueRules": null}], "name": "sourceApplicationId", "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-4MKMkatdrQYs4E06Uri-O", "name": "Field", "props": {"align": "right", "componentProps": {"fieldAlias": "id", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {"precisionDisplayType": "fill-round"}, "displayComponentType": "Number", "editComponentProps": {}, "label": "id", "lookup": [{"fieldRules": {"hidden": true, "required": false}, "key": "stkCwl2xHuyUQq_gLvKGW", "trigger": "auto", "valueRules": null}], "name": "id", "type": "NUMBER"}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-soLYMQt2FC7BNOIaKJf09", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ", "name": "Table", "props": {"allowRowSelect": true, "enableSolution": true, "filterFields": [{"componentProps": {"fieldAlias": "name", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "角色名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "sg026ay5Yi_ckzSyqkSmN", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "key", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "角色标识", "name": "key", "type": "TEXT"}], "flow": {"context$": "$context", "params": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "serviceKey": "TSRM$SRM_SEARCH_ROLES_SERVICE", "type": "InvokeService"}, "label": "新员工管理", "mode": "normal", "modelAlias": "TSRM$tsrm_emp_ro_temp_tr", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "selectType": "multiple", "showType": "normal", "subTableConfig": {}}, "type": "Container"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-eZQqCcl0AeD6oTuZXNsin", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-9QuMssVlkzQYHQ8S2LrMU", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": true, "layout": "modal", "layoutProps": {"bodyStyle": {}, "dragMode": [], "maskClosable": false, "width": 720, "widthType": "middle"}, "params": [{"label": "用户id", "value": "userId"}, {"label": "门户id", "value": "endpointId"}], "title": "绑定角色"}, "type": "Container"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "name": "Page", "props": {"actionConfigs": [], "backResourceTabProcessConfig": "retain", "collectionService": {"createBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/save"}, "deleteBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/delete"}, "getCurrentSceneBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK"}}, "params": [], "showFooter": false, "showHeader": false}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "i18nConfig": {"i18nKeySet": ["组织名称", "用户", "手机", "状态", "停用", "新建", "绑定角色", "查看", "组织", "重置密码", "是否删除选中单据？", "删除", "删除成功!", "停用成功", "姓名", "邮箱", "保存", "启用", "用户名", "id", "启用成功", "角色标识", "确认启用吗？", "请选择", "员工信息表列表", "编辑", "密码重置成功！", "请输入", "角色名称", "员工类型", "所属门户", "确认删除吗？", "是否确认重置密码？", "角色描述", "确认停用吗？", "新员工管理", "员工编码"], "i18nScanPaths": ["TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.2.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ.props.filterFields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.1.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-8cfFlyauvxbBVDFo4p3wL.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-delete.props.actionConfig.beforeLogicConfig.0.text", "TSRM$NEW_ORG_EMPLOYEE_VIEW-GKmaXPmqlsAKriDFnrmS4.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-_epNLnG3-eQ41lsjR0JUQ.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-S_VmcdAvJaRuOJHI5MZcM.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-delete.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.4.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL.props.title", "TSRM$NEW_ORG_EMPLOYEE_VIEW-S_VmcdAvJaRuOJHI5MZcM.props.editComponentProps.fields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.3.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW--record-actions-1-button-disable.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-0TyCG6rvvHbUi_eS1-e7B.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW--record-actions-1-button-disable.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-4MKMkatdrQYs4E06Uri-O.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-GKmaXPmqlsAKriDFnrmS4.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-S_VmcdAvJaRuOJHI5MZcM.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW--record-actions-1-button-enable.props.actionConfig.beforeLogicConfig.0.text", "TSRM$NEW_ORG_EMPLOYEE_VIEW-YmGuny4fAbnX7bnx1xAlM.props.actionConfig.beforeLogicConfig.0.text", "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1-button-batch-delete.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-23arSU28mCkWwxtUVCzIx.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-dItcx9n4tn6EwR7rzSmAE.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-_epNLnG3-eQ41lsjR0JUQ.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-delete.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ.props.filterFields.1.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-view.props.label", "@exp:TSRM$NEW_ORG_EMPLOYEE_VIEW-list-title.props.title", "TSRM$NEW_ORG_EMPLOYEE_VIEW-YmGuny4fAbnX7bnx1xAlM.props.actionConfig.endLogicOtherConfig.0.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW--record-actions-1-button-enable.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-3jJLEEncqHpaBR7oWE32C.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.5.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1-button-batch-delete.props.actionConfig.beforeLogicConfig.0.text", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-YmGuny4fAbnX7bnx1xAlM.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-eRuJ6prrS10IWP-2_QjOK.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-edit.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-8cfFlyauvxbBVDFo4p3wL.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-dItcx9n4tn6EwR7rzSmAE.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.3.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW--record-actions-1-button-disable.props.actionConfig.beforeLogicConfig.0.text", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.6.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-0TyCG6rvvHbUi_eS1-e7B.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-dItcx9n4tn6EwR7rzSmAE.props.editComponentProps.fields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ.props.filterFields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.7.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-4MKMkatdrQYs4E06Uri-O.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-S_VmcdAvJaRuOJHI5MZcM.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-tvedUDBmAtMT2oJq47ruB.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.6.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.2.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1-button-add.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.7.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-tvedUDBmAtMT2oJq47ruB.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-3UTs0N5-1n3di8rNor0N7.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Xy_808JN6g3AmEG5-QOgq.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.5.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-z1dVxbH6QVxFaL8s3pI0V.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1-button-batch-delete.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-xeCcUhwJ5IkSHUkOtgigm.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-3UTs0N5-1n3di8rNor0N7.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.1.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ.props.filterFields.1.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW--record-actions-1-button-enable.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-dItcx9n4tn6EwR7rzSmAE.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-3jJLEEncqHpaBR7oWE32C.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-23arSU28mCkWwxtUVCzIx.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-xeCcUhwJ5IkSHUkOtgigm.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md.props.filterFields.4.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-eRuJ6prrS10IWP-2_QjOK.props.componentProps.placeholder"]}, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:list", "name": "list", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW:list_view_perm_ac_aa7ff8", "resources": [{"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}, {"key": "sys_common$ORG_EMPLOYEE_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-view", "label": "查看", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-edit", "label": "编辑", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW--record-actions-1-button-disable", "label": "停用", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$SRM_DISABLE_EMPLOYEE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW--record-actions-1-button-enable", "label": "启用", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$SRM_ENABLE_EMPLOYEE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1-button-delete", "label": "删除", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "sys_common$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-YmGuny4fAbnX7bnx1xAlM", "label": "重置密码", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$tsrm_org_employee_reset_password", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-z1dVxbH6QVxFaL8s3pI0V", "label": "绑定角色", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-23arSU28mCkWwxtUVCzIx", "label": "员工编码", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-VekVeddyWs5RT4PykJRdA", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-dItcx9n4tn6EwR7rzSmAE", "label": "用户", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-VekVeddyWs5RT4PykJRdA", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-tvedUDBmAtMT2oJq47ruB", "label": "姓名", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-VekVeddyWs5RT4PykJRdA", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-0TyCG6rvvHbUi_eS1-e7B", "label": "员工类型", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-VekVeddyWs5RT4PykJRdA", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-eRuJ6prrS10IWP-2_QjOK", "label": "手机", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-VekVeddyWs5RT4PykJRdA", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-GKmaXPmqlsAKriDFnrmS4", "label": "邮箱", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-VekVeddyWs5RT4PykJRdA", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-S_VmcdAvJaRuOJHI5MZcM", "label": "组织", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-VekVeddyWs5RT4PykJRdA", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-xeCcUhwJ5IkSHUkOtgigm", "label": "状态", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-VekVeddyWs5RT4PykJRdA", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ", "label": "新员工管理", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-eZQqCcl0AeD6oTuZXNsin", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "TSRM$SRM_SEARCH_ROLES_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1-button-add", "label": "新建", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1", "label": "按钮组", "type": "BatchActions"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-lNG_L_wulE514frwx8h1H", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1-button-batch-delete", "label": "删除", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-table-container-sys_common$org_employee_md", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-batch-actions-1", "label": "按钮组", "type": "BatchActions"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-lNG_L_wulE514frwx8h1H", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "sys_common$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-3jJLEEncqHpaBR7oWE32C", "label": "角色名称", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-eZQqCcl0AeD6oTuZXNsin", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-soLYMQt2FC7BNOIaKJf09", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-3UTs0N5-1n3di8rNor0N7", "label": "角色标识", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-eZQqCcl0AeD6oTuZXNsin", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-soLYMQt2FC7BNOIaKJf09", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-8cfFlyauvxbBVDFo4p3wL", "label": "角色描述", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-eZQqCcl0AeD6oTuZXNsin", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-soLYMQt2FC7BNOIaKJf09", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-_epNLnG3-eQ41lsjR0JUQ", "label": "所属门户", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-eZQqCcl0AeD6oTuZXNsin", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-soLYMQt2FC7BNOIaKJf09", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-4MKMkatdrQYs4E06Uri-O", "label": "id", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-eZQqCcl0AeD6oTuZXNsin", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-soLYMQt2FC7BNOIaKJf09", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Xy_808JN6g3AmEG5-QOgq", "label": "保存", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-ALK9hmjHRGMk77y0_doPL", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-eZQqCcl0AeD6oTuZXNsin", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-hRw-6Pfjui6tNGyGA1gUJ", "label": "新员工管理", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-jdE45H-hLBe72oVo20xGj", "label": "按钮组", "type": "BatchActions"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-vQxHiq_YDI2wh5UU6goEr", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "sys_common$API_TRANTOR_PORTAL_ROLE_RELATION_USER_FLUSH_POST", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}], "title": "list", "type": "LIST"}, "type": "View"}