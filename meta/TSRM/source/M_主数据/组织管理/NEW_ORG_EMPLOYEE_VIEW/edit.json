{"access": "Private", "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:edit", "name": "edit", "props": {"containerSelect": {"TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md": [{"conditionGroup": null, "field": "code", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "name", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "type", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "orgStructId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "orgName", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "mobile", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "email", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "userName", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "entryAt", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "resignationAt", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "idCard", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "addressId", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "addressDetail", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "version", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "employeeOrgLinkList", "selectFields": [{"conditionGroup": null, "field": "identityId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "name", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "orgUnitId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "orgName", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "isMainOrg", "selectFields": null, "sortOrders": null}], "sortOrders": null}]}, "content": {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" : \"创建\"}}员工信息表", "useExpression": "true"}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-code", "name": "FormField", "props": {"componentProps": {"fieldAlias": "code", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "员工编码", "name": "code", "rules": [{"message": "请输入员工编码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-name", "name": "FormField", "props": {"componentProps": {"fieldAlias": "name", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "姓名", "name": "name", "rules": [{"message": "请输入姓名", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-type", "name": "FormField", "props": {"componentProps": {"fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "editComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "hidden": false, "label": "员工类型", "name": "type", "rules": [{"message": "请输入员工类型", "required": true}], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-orgStructId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "orgStructId", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"containerKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-orgStructId", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"buttonConfig": {}, "fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "isRelationColumn": true, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "3YRG4RQAvwwWhNlje58Dt", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "filterFields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "n0Nxs5wASLN3Bs4fBCqXV", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}], "findFlow": {"containerKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-orgStructId", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "选择组织", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "组织", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "4z05RzYOSglBQ4MIeyEPt", "trigger": "auto", "valueRules": null}], "name": "orgStructId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-mobile", "name": "FormField", "props": {"componentProps": {"fieldAlias": "mobile", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {}, "editComponentType": "InputText", "hidden": false, "label": "手机", "name": "mobile", "rules": [{"message": "请输入手机", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-email", "name": "FormField", "props": {"componentProps": {"fieldAlias": "email", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "邮箱", "name": "email", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-userName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "userName", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {}, "editComponentType": "InputText", "hidden": false, "label": "用户名", "name": "userName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-entryAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "entryAt", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {}, "displayComponentType": "Date", "editComponentProps": {}, "editComponentType": "DatePicker", "hidden": false, "label": "入职日期", "name": "entryAt", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-resignationAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "resignationAt", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {}, "displayComponentType": "Date", "editComponentProps": {}, "editComponentType": "DatePicker", "hidden": false, "label": "离职日期", "name": "resignationAt", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-idCard", "name": "FormField", "props": {"componentProps": {"fieldAlias": "idCard", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {}, "editComponentType": "InputText", "hidden": false, "label": "身份证号", "name": "idCard", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressId", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {"labelField": ["addrName"], "modelAlias": "ERP_GEN$gen_addr_type_cf", "precisionDisplayType": "fill-round", "valueField": "id"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "addrName", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "label": "地址库名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "t4UpYQk8GHk0LN4Vi0btF", "trigger": "auto", "valueRules": null}], "name": "addrName", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "addrName", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "label": "地址库名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "t4UpYQk8GHk0LN4Vi0btF", "trigger": "auto", "valueRules": null}], "name": "addrName", "type": "TEXT", "width": 120}], "flow": {"context$": "$context", "params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable", "required": null}], "serviceKey": "ERP_GEN$gen_addr_type_cf_FIND_TREE_CHILDREN_DATA_SERVICE", "type": "InvokeService"}, "labelField": "addrName", "leafOnly": false, "modelAlias": "ERP_GEN$gen_addr_type_cf", "precisionDisplayType": "fill-round", "reverseConstructFlow": {"context$": "$context", "params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable", "required": null}], "serviceKey": "ERP_GEN$gen_addr_type_cf_REVERSE_CONSTRUCT_TREE_SERVICE", "type": "InvokeService"}, "shape": "line", "tableConditionContext$": null}, "editComponentType": "SelfRelation", "hidden": false, "label": "地址", "name": "addressId", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressDetail", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressDetail", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {}, "editComponentType": "InputText", "hidden": false, "label": "详细地址", "name": "addressDetail", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "sys_common$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "gBFfsSQap-Gp3yhGeMzw0", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "editComponentType": "RelationSelect", "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "sys_common$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "w6CSPcUUhriRNW8z1GoPp", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "editComponentType": "RelationSelect", "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-originOrgId", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "rules": [], "type": "NUMBER"}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "name": "FormGroupItem", "props": {"title": false}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-X345HpEWAgvf_P2Os8Hxu", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "CustomAction", "actionId": "ufBMNaL2Cjy3rdPEnfR-8", "executeAction": {"key": "batchDelete", "params": [{"expression": "<PERSON><PERSON><PERSON><PERSON>", "name": "data", "type": "expression"}], "target": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iCDszbCRI-6mmS2JjwYgJ"}, "id": "ufBMNaL2Cjy3rdPEnfR-8-0"}]}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-X345HpEWAgvf_P2Os8Hxu_perm_ac", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Zk5iChuDekAzyIynrR_c3", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-py1_GVqxM90rahUlj9blG", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-LkNMV3KEymEDRDIV6ZmuY", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT", "name": "Field", "props": {"componentProps": {"fieldAlias": "identityId", "labelField": "name", "modelAlias": "sys_common$org_identity_cf", "parentModelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "primaryId", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_identity_cf"}], "serviceKey": "sys_common$ORG_IDENTITY_CF_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "labelField": ["name"], "modelAlias": "sys_common$org_identity_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"buttonConfig": {}, "fields": [{"componentProps": {"fieldAlias": "code", "parentModelAlias": "sys_common$org_identity_cf", "placeholder": "请输入"}, "isRelationColumn": true, "label": "编码", "name": "code", "type": "TEXT"}, {"componentProps": {"fieldAlias": "name", "parentModelAlias": "sys_common$org_identity_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "isRelationColumn": true, "label": "名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "BTY_XrIC_iYKZistsnJJl", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT"}], "filterFields": [{"componentProps": {"fieldAlias": "code", "parentModelAlias": "sys_common$org_identity_cf", "placeholder": "请输入"}, "label": "编码", "name": "code", "type": "TEXT"}, {"componentProps": {"fieldAlias": "name", "parentModelAlias": "sys_common$org_identity_cf", "placeholder": "请输入"}, "label": "名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "zqBMKa7I_GnDKC--YPmqJ", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "primaryId", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_identity_cf"}], "serviceKey": "sys_common$ORG_IDENTITY_CF_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "flow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "required": null}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "required": null}], "serviceKey": "sys_common$ORG_IDENTITY_CF_PAGING_DATA_SERVICE", "type": "InvokeService"}, "label": "选择组织身份", "labelField": ["name"], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_identity_cf", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "label": "组织身份", "name": "identityId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH", "name": "Field", "props": {"componentProps": {"fieldAlias": "orgUnitId", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "primaryId", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_struct_md"}], "serviceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"buttonConfig": {}, "fields": [{"componentProps": {"fieldAlias": "orgCode", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "isRelationColumn": true, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Q_0nIx2NyJsrgD6JJI6cH", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "filterFields": [{"componentProps": {"fieldAlias": "orgCode", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "label": "组织编码", "name": "orgCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "NmEjQzG_GI5FKXWz00WoR", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "primaryId", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_struct_md"}], "serviceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "flow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text"}, {"fieldAlias": "orgBusinessTypeCodes", "fieldName": "orgBusinessTypeCodes", "fieldType": "Text"}, {"fieldAlias": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": "Text"}, {"fieldAlias": "parentId", "fieldName": "parentId", "fieldType": "Object"}, {"fieldAlias": "comOrgId", "fieldName": "comOrgId", "fieldType": "Number"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "label": "选择组织单元", "labelField": ["orgName"], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "label": "组织单元", "name": "orgUnitId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-_K0Msj5shDqiJwPn7RAxJ", "name": "Field", "props": {"componentProps": {"defaultValue": false, "fieldAlias": "isMainOrg", "parentModelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择"}, "displayComponentProps": {}, "displayComponentType": "BoolShow", "editComponentProps": {"options": [{"id": "I7Eq_1Hzlg7obv_UVxg_C", "label": "是", "value": true}, {"id": "obyQ85LFNnhF2H6O-x7bQ", "label": "否", "value": false}]}, "editComponentType": "Switch", "initialValue": false, "label": "是否主组织", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "xiqBscERqpI5V2icXGgMr", "trigger": "auto", "valueRules": null}], "name": "isMainOrg", "type": "BOOL", "width": 116}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-jAOQBE10ygdzr8GVMLpXd", "name": "Fields", "props": {}, "type": "Meta"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-J3ZjysfCknU4ySBTRqpjA", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "CustomAction", "actionId": "UR8agbz6ogUbOyMJRdfxH", "executeAction": {"actionScope": "childrow", "key": "deleteChildRow", "params": [{"expression": "record", "name": "record", "type": "expression"}], "target": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iCDszbCRI-6mmS2JjwYgJ"}, "id": "UR8agbz6ogUbOyMJRdfxH-0"}]}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-J3ZjysfCknU4ySBTRqpjA_perm_ac", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-9r8Py2gT-S4FUot1NG0sK", "name": "RecordActions", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iCDszbCRI-6mmS2JjwYgJ", "name": "TableForm", "props": {"allowRowSelect": true, "creatorPosition": "bottom", "fieldName": "employeeOrgLinkList", "fields": [], "hideCreator": false, "hideDefaultDelete": true, "label": "表格表单", "maxHeight": 414, "modelAlias": "sys_common$org_employee_org_link_cf", "pagination": {}, "showType": "normal", "subTableConfig": {}}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-mDJQPVx3zLMM-idYWx7ka", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Sl_ZP_ZXdsv-eSzo1CggO", "name": "FormGroupItem", "props": {"collapsible": true, "defaultCollapsed": false, "enableSubGroup": false, "showSplit": true, "title": "组织单元"}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route.query?.copyId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"refresh": true, "type": "Previous"}}]}, "label": "取消", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer-cancel_perm_ac"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "valueConfig": {"action": {"name": "getData", "target": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md"}, "type": "action"}}], "service": "ERP_GEN$ORG_EMPLOYEE_SAVE_EVENT_SERVICE"}, "endLogicOtherConfig": [{"action": "OpenView", "id": "key-0", "openViewConfig": {"page": {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:list", "name": "list", "type": "View"}, "params": [], "refresh": true, "type": "NewPage"}}, {"action": "Message", "level": "success", "message": "提交成功!"}], "executeLogic": "BindService"}, "label": "保存", "showCondition": {"conditions": [{"conditions": [{"id": "Ni1dELKrRAWCQzpph1LKg", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID(id)", "type": "VarValue", "val": "id", "value": "sys_common$org_employee_md.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "ErDcoa_05W_e9l0H6w7qc", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "YFlstR1SWVEN4dPBPKLAS", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-5GSRu7EQF9f1qUtFBFMDL", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "valueConfig": {"action": {"name": "getData", "target": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md"}, "type": "action"}}], "service": "TSRM$SRM_SAVE_EMPLOYEE_WITH_ROLE"}, "endLogicOtherConfig": [{"action": "OpenView", "actionId": "7k9huMTg4Wbc4cW0BupfD", "id": "7k9huMTg4Wbc4cW0BupfD-0", "openViewConfig": {"page": {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:list", "name": "list", "type": "View"}, "params": [], "refresh": true, "type": "NewPage"}}, {"action": "Message", "actionId": "ulnVUyaxF2Fl4zq2Kz-c5", "id": "ulnVUyaxF2Fl4zq2Kz-c5-1", "level": "success", "message": "提交成功！"}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "showCondition": {"conditions": [{"conditions": [{"id": "6uYwZNxsXGqrWKsv-boR8", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID(id)", "type": "VarValue", "val": "id", "value": "sys_common$org_employee_md.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "DkqUNShMR9OtFlRlzSb0Z", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "MXT4hLZxIpg2-rV5ZH0XG", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iFsIlI5Kqc9QS3qd8PlI1", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-caa1ed4hOudZcmOGU4STX", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-B5FR7VVvvcw-FgiHybyhi", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-w9PQmPnopbE8SlFxiWuIb", "name": "ActionsGroup", "props": {"params": []}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "name": "Page", "props": {"collectionService": {"createBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/save"}, "deleteBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/delete"}, "getCurrentSceneBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK"}}, "params": [], "showFooter": true, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "i18nConfig": {"i18nKeySet": ["组织名称", "手机", "请输入版本号", "请输入员工类型", "名称", "组织", "请输入创建时间", "请输入员工编码", "详细地址", "删除", "入职日期", "离职日期", "姓名", "邮箱", "保存", "请输入ID", "组织单元", "表格表单", "用户名", "地址", "ID", "创建人", "身份证号", "请输入更新时间", "提交成功!", "请输入姓名", "逻辑删除标识", "编码", "请输入手机", "组织编码", "请选择", "版本号", "更新时间", "是否主组织", "更新人", "编辑", "创建", "所属组织", "请输入", "员工信息表", "取消", "组织身份", "创建时间", "员工类型", "提交成功！", "请输入逻辑删除标识", "地址库名称", "员工编码"], "i18nScanPaths": ["TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedAt.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-orgStructId.props.editComponentProps.fields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT.props.editComponentProps.fields.1.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedAt.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-originOrgId.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer-save.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-name.props.rules.0.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-entryAt.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-5GSRu7EQF9f1qUtFBFMDL.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-resignationAt.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-entryAt.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH.props.editComponentProps.fields.1.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-email.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT.props.editComponentProps.fields.1.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-mobile.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressDetail.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-userName.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH.props.editComponentProps.filterFields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-email.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdBy.props.editComponentProps.fields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-id.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-mobile.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedBy.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT.props.editComponentProps.filterFields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-code.props.rules.0.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-version.props.rules.0.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdAt.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-deleted.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-orgStructId.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-orgStructId.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH.props.editComponentProps.filterFields.1.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH.props.editComponentProps.fields.1.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-resignationAt.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-version.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT.props.editComponentProps.fields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-orgStructId.props.editComponentProps.filterFields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-deleted.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-type.props.rules.0.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressId.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-idCard.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-iCDszbCRI-6mmS2JjwYgJ.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdAt.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-name.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT.props.editComponentProps.filterFields.1.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-type.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT.props.editComponentProps.filterFields.1.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH.props.editComponentProps.filterFields.1.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdBy.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-orgStructId.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-_K0Msj5shDqiJwPn7RAxJ.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressId.props.editComponentProps.fields.0.label", "@exp:TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-title.props.title", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-idCard.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-X345HpEWAgvf_P2Os8Hxu.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-code.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedBy.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-J3ZjysfCknU4ySBTRqpjA.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressId.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-version.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-name.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressId.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-_K0Msj5shDqiJwPn7RAxJ.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-deleted.props.rules.0.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-5GSRu7EQF9f1qUtFBFMDL.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-code.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-id.props.rules.0.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressId.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer-cancel.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-type.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressId.props.editComponentProps.filterFields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-id.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer-save.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH.props.editComponentProps.fields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-originOrgId.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressDetail.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedBy.props.editComponentProps.fields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdBy.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdAt.props.rules.0.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-orgStructId.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-userName.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Sl_ZP_ZXdsv-eSzo1CggO.props.title", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-mobile.props.rules.0.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedAt.props.rules.0.message"]}, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:edit", "name": "edit", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW:edit_view_perm_ac_5394d8", "resources": [{"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-code", "label": "员工编码", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-name", "label": "姓名", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-type", "label": "员工类型", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-orgStructId", "label": "组织", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-mobile", "label": "手机", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-email", "label": "邮箱", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-userName", "label": "用户名", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-entryAt", "label": "入职日期", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-resignationAt", "label": "离职日期", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-idCard", "label": "身份证号", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressId", "label": "地址", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$gen_addr_type_cf_FIND_TREE_CHILDREN_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$gen_addr_type_cf_REVERSE_CONSTRUCT_TREE_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-addressDetail", "label": "详细地址", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-id", "label": "ID", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdBy", "label": "创建人", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedBy", "label": "更新人", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-createdAt", "label": "创建时间", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-updatedAt", "label": "更新时间", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-version", "label": "版本号", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-deleted", "label": "逻辑删除标识", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md-for-widget-originOrgId", "label": "所属组织", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-container-sys_common$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iCDszbCRI-6mmS2JjwYgJ", "label": "表格表单", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Sl_ZP_ZXdsv-eSzo1CggO", "label": "组织单元", "type": "FormGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-mDJQPVx3zLMM-idYWx7ka", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer-cancel", "label": "取消", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-w9PQmPnopbE8SlFxiWuIb", "label": "按钮组", "type": "ActionsGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iFsIlI5Kqc9QS3qd8PlI1", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer-save", "label": "保存", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-w9PQmPnopbE8SlFxiWuIb", "label": "按钮组", "type": "ActionsGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iFsIlI5Kqc9QS3qd8PlI1", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "ERP_GEN$ORG_EMPLOYEE_SAVE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-5GSRu7EQF9f1qUtFBFMDL", "label": "保存", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-w9PQmPnopbE8SlFxiWuIb", "label": "按钮组", "type": "ActionsGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iFsIlI5Kqc9QS3qd8PlI1", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "TSRM$SRM_SAVE_EMPLOYEE_WITH_ROLE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-cIxBIvkjiUIvjLCkiOKxT", "label": "组织身份", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Sl_ZP_ZXdsv-eSzo1CggO", "label": "组织单元", "type": "FormGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-mDJQPVx3zLMM-idYWx7ka", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iCDszbCRI-6mmS2JjwYgJ", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-jAOQBE10ygdzr8GVMLpXd", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$ORG_IDENTITY_CF_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$ORG_IDENTITY_CF_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ow5v6jCdx4PK5f7FTnlkH", "label": "组织单元", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Sl_ZP_ZXdsv-eSzo1CggO", "label": "组织单元", "type": "FormGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-mDJQPVx3zLMM-idYWx7ka", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iCDszbCRI-6mmS2JjwYgJ", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-jAOQBE10ygdzr8GVMLpXd", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-_K0Msj5shDqiJwPn7RAxJ", "label": "是否主组织", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Sl_ZP_ZXdsv-eSzo1CggO", "label": "组织单元", "type": "FormGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-mDJQPVx3zLMM-idYWx7ka", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iCDszbCRI-6mmS2JjwYgJ", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-jAOQBE10ygdzr8GVMLpXd", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-J3ZjysfCknU4ySBTRqpjA", "label": "删除", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Sl_ZP_ZXdsv-eSzo1CggO", "label": "组织单元", "type": "FormGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-mDJQPVx3zLMM-idYWx7ka", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iCDszbCRI-6mmS2JjwYgJ", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-9r8Py2gT-S4FUot1NG0sK", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-X345HpEWAgvf_P2Os8Hxu", "label": "删除", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-total-config-sys_common$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Sl_ZP_ZXdsv-eSzo1CggO", "label": "组织单元", "type": "FormGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-mDJQPVx3zLMM-idYWx7ka", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-iCDszbCRI-6mmS2JjwYgJ", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-LkNMV3KEymEDRDIV6ZmuY", "label": "按钮组", "type": "BatchActions"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Zk5iChuDekAzyIynrR_c3", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [], "type": "<PERSON><PERSON>"}], "title": "edit", "type": "FORM"}, "type": "View"}