{"access": "Private", "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:detail", "name": "detail", "props": {"containerSelect": {"TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail": [{"conditionGroup": null, "field": "code", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "userId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "name", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "status", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "type", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "email", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "mobile", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "entryAt", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "resignationAt", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "idCard", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "addressId", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "addressDetail", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "orgStructId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "orgName", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "userName", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "employeeOrgLinkList", "selectFields": [{"conditionGroup": null, "field": "identityId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "name", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "orgUnitId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "orgName", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "isMainOrg", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "created<PERSON>y", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "updatedBy", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "createdAt", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "updatedAt", "selectFields": null, "sortOrders": null}]}, "content": {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "员工信息表详情"}, "type": "Meta"}, {"children": [{"children": [{"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "sys_common$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["TSRM$NEW_ORG_EMPLOYEE_VIEW-detail"]}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "label": "启用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:edit", "name": "detail", "type": "View"}, "params": [{"expression": "route.recordId", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "label": "编辑", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header-edit_perm_ac", "type": "primary"}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-X0MBvNg3ZgYPrxKPN6O_0", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-d8iUM8Ww8-yQv9Tb0wJgL", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-JfAYQL5Z87hGxx_NiAgO7", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-dkrO7ZmC8Vedw20B2uz5g", "name": "ActionsGroup", "props": {"params": []}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-code", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "code", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "员工编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-userId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "userId", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"containerKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-userId", "context$": "$context", "modelAlias": "sys_common$user", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$user"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "sys_common$user"}, "label": "用户", "name": "userId", "type": "OBJECT"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-name", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "name", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "姓名", "name": "name", "type": "TEXT"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-status", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "editComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "label": "状态", "name": "status", "type": "SELECT"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-type", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "editComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "label": "员工类型", "name": "type", "type": "SELECT"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-email", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "email", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "邮箱", "name": "email", "type": "TEXT"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-mobile", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "mobile", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "手机", "name": "mobile", "type": "TEXT"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-entryAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "entryAt", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {}, "displayComponentType": "Date", "editComponentProps": {}, "label": "入职日期", "name": "entryAt", "type": "DATE"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-resignationAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "resignationAt", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {}, "displayComponentType": "Date", "editComponentProps": {}, "label": "离职日期", "name": "resignationAt", "type": "DATE"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-idCard", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "idCard", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "身份证号", "name": "idCard", "type": "TEXT"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressId", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {"findFlow": {"containerKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressId", "context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_addr_type_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": ["addrName"], "modelAlias": "ERP_GEN$gen_addr_type_cf", "precisionDisplayType": "fill-round", "separator": ""}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "addrName", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "label": "地址库名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "7JmwbKPk5AF1i5Wra8XVO", "trigger": "auto", "valueRules": null}], "name": "addrName", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "addrName", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "label": "地址库名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "7JmwbKPk5AF1i5Wra8XVO", "trigger": "auto", "valueRules": null}], "name": "addrName", "type": "TEXT", "width": 120}], "flow": {"context$": "$context", "params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable", "required": null}], "serviceKey": "ERP_GEN$gen_addr_type_cf_FIND_TREE_CHILDREN_DATA_SERVICE", "type": "InvokeService"}, "labelField": "addrName", "leafOnly": false, "modelAlias": "ERP_GEN$gen_addr_type_cf", "reverseConstructFlow": {"context$": "$context", "params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable", "required": null}], "serviceKey": "ERP_GEN$gen_addr_type_cf_REVERSE_CONSTRUCT_TREE_SERVICE", "type": "InvokeService"}, "showScope": "all", "tableConditionContext$": null}, "editComponentType": "SelfRelation", "label": "地址", "name": "addressId", "type": "NUMBER"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressDetail", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressDetail", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {}, "label": "详细地址", "name": "addressDetail", "type": "TEXT"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-orgStructId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "orgStructId", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "sys_common$org_struct_md"}, "label": "组织", "name": "orgStructId", "type": "OBJECT"}}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-userName", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "userName", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "用户名", "name": "userName", "type": "TEXT"}}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-T6cGZXDsQBCOYeCFrLm-x", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Ajh5U8eQGv6fraWllfIX9", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-5w-3TjGF_uyJKpLPVb8kD", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-dhRSZ9RJgCgTeWAqRLSL7", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-c20vWPRZhy9cTZGyS3rIW", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-bptAI2SSzd-tuAaSQJ1YD", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-zoLZEUIKZI2ejSWoNrzDo", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-k8LtIhFDspFwJX7KUbKTO", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-6ahxOSRptupLIvOzlrQnp", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Lhr23w1PfvHhzPGWC3ChD", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "identityId", "labelField": "name", "modelAlias": "sys_common$org_identity_cf", "parentModelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "primaryId", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_identity_cf"}], "serviceKey": "sys_common$ORG_IDENTITY_CF_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "modelAlias": "sys_common$org_identity_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "name", "parentModelAlias": "sys_common$org_identity_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "V2_7RCv_yodjLEPxdC6Wz", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_identity_cf", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_identity_cf", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_identity_cf"}, "editComponentType": "RelationSelect", "label": "组织身份", "modelAlias": "sys_common$org_identity_cf", "name": "identityId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-6A5oZ5vZ13bA1sZBIUKmz", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "orgUnitId", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "primaryId", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_struct_md"}], "serviceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "okdTccpu32DxWw5Fg3x4M", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "editComponentType": "RelationSelect", "label": "组织单元", "modelAlias": "sys_common$org_struct_md", "name": "orgUnitId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-dsHvvlWUISVKi1xBAIWKd", "name": "Field", "props": {"align": "left", "componentProps": {"defaultValue": false, "fieldAlias": "isMainOrg", "parentModelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择"}, "displayComponentProps": {}, "displayComponentType": "BoolShow", "editComponentProps": {}, "initialValue": false, "label": "是否主组织", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "8jeCSuLEw3L1re-fqjOVG", "trigger": "auto", "valueRules": null}], "name": "isMainOrg", "type": "BOOL", "width": 116}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-KQEGWxqQjWW63PrXk2urK", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-sDlnoJL7Ccmx-thqfloV2", "name": "Table", "props": {"enableSolution": false, "fieldName": "employeeOrgLinkList", "flow": {"context$": "$context", "name": "employeeOrgLinkList", "type": "RelationData"}, "isLabelManualModified": true, "label": "", "mode": "normal", "modelAlias": "sys_common$org_employee_org_link_cf", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "showType": "normal", "subTableConfig": {}}, "type": "Container"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW--7zobvbAFQPxEPZ3Go2j1", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-n5fObr4IR_jiJYm5rCr6N", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-hn4W9zZWlCaOIGY1MB5HX", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "sys_common$user"}, "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-8RXPjItfUa9N6G78gUMsZ", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$user", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "sys_common$user"}, "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-JpVj6V9pEBYkQ_Mo4kznm", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {}, "editComponentProps": {}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-BeG6R9n6j9A-LJpb_jAk4", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {}, "editComponentProps": {}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}, "type": "Widget"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-kxYNj6fgNWIg7LkIpA_Qa", "name": "DetailGroupItem", "props": {"collapsible": true, "defaultCollapsed": false, "enableSubGroup": false, "title": ""}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-lFLHt4KsXbUMqH6-hAt-P", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-", "name": "Tabs", "props": {"items": [{"key": "-dp1WJ995R-aWcUn_6I99", "label": "组织单元"}, {"key": "JWVe-5Qe8Cc1kpAlJ6eDr", "label": "系统信息"}], "lookup": [], "mode": "container"}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Q1NXilZSzSy69m-fzVMVA", "name": "DetailGroupItem", "props": {"collapsible": true, "defaultCollapsed": false, "enableSubGroup": false, "title": ""}, "type": "Layout"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "name": "Detail", "props": {"flow": {"containerKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "type": "Container"}], "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "name": "Page", "props": {"collectionService": {"createBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/save"}, "deleteBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/delete"}, "getCurrentSceneBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK"}}, "params": [], "showFooter": false, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "i18nConfig": {"i18nKeySet": ["", "组织名称", "用户", "手机", "系统信息", "状态", "名称", "组织", "详细地址", "入职日期", "离职日期", "姓名", "邮箱", "启用", "组织单元", "地址", "用户名", "创建人", "身份证号", "启用成功", "确认启用吗？", "请选择", "更新时间", "是否主组织", "更新人", "员工信息表详情", "编辑", "请输入", "组织身份", "创建时间", "员工类型", "地址库名称", "员工编码"], "i18nScanPaths": ["TSRM$NEW_ORG_EMPLOYEE_VIEW-6A5oZ5vZ13bA1sZBIUKmz.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Q1NXilZSzSy69m-fzVMVA.props.title", "TSRM$NEW_ORG_EMPLOYEE_VIEW-sDlnoJL7Ccmx-thqfloV2.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Lhr23w1PfvHhzPGWC3ChD.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header-enable.props.actionConfig.beforeLogicConfig.0.text", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Lhr23w1PfvHhzPGWC3ChD.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-userId.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-userName.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-status.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-type.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-orgStructId.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-.props.items.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-userName.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-userId.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-email.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-orgStructId.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-6A5oZ5vZ13bA1sZBIUKmz.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-JpVj6V9pEBYkQ_Mo4kznm.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-entryAt.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-hn4W9zZWlCaOIGY1MB5HX.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-mobile.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-JpVj6V9pEBYkQ_Mo4kznm.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header-enable.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header-edit.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressId.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-code.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-resignationAt.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressId.props.editComponentProps.fields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-mobile.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header-enable.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$NEW_ORG_EMPLOYEE_VIEW-BeG6R9n6j9A-LJpb_jAk4.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-idCard.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressId.props.editComponentProps.filterFields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-entryAt.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressId.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-.props.items.1.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressDetail.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-resignationAt.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-idCard.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Lhr23w1PfvHhzPGWC3ChD.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-dsHvvlWUISVKi1xBAIWKd.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-BeG6R9n6j9A-LJpb_jAk4.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-name.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressDetail.props.label", "@exp:TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-title.props.title", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-email.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-8RXPjItfUa9N6G78gUMsZ.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-dsHvvlWUISVKi1xBAIWKd.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-8RXPjItfUa9N6G78gUMsZ.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-kxYNj6fgNWIg7LkIpA_Qa.props.title", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-type.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-hn4W9zZWlCaOIGY1MB5HX.props.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-6A5oZ5vZ13bA1sZBIUKmz.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-6A5oZ5vZ13bA1sZBIUKmz.props.editComponentProps.fields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-Lhr23w1PfvHhzPGWC3ChD.props.editComponentProps.fields.0.label", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-code.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-status.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-name.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressId.props.componentProps.placeholder", "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressId.props.editComponentProps.fields.0.componentProps.placeholder"]}, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW:detail", "name": "detail", "permissionKey": "TSRM$NEW_ORG_EMPLOYEE_VIEW:detail_view_perm_ac_d27607", "resources": [{"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-code", "label": "员工编码", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-userId", "label": "用户", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-name", "label": "姓名", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-status", "label": "状态", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-type", "label": "员工类型", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-email", "label": "邮箱", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-mobile", "label": "手机", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-entryAt", "label": "入职日期", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-resignationAt", "label": "离职日期", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-idCard", "label": "身份证号", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressId", "label": "地址", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$gen_addr_type_cf_FIND_TREE_CHILDREN_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "ERP_GEN$gen_addr_type_cf_REVERSE_CONSTRUCT_TREE_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-addressDetail", "label": "详细地址", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-orgStructId", "label": "组织", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout-For-DetailField-userName", "label": "用户名", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail-Layout", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header-enable", "label": "启用", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-dkrO7ZmC8Vedw20B2uz5g", "label": "按钮组", "type": "ActionsGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-X0MBvNg3ZgYPrxKPN6O_0", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "sys_common$SYS_MasterData_EnableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header-edit", "label": "编辑", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-dkrO7ZmC8Vedw20B2uz5g", "label": "按钮组", "type": "ActionsGroup"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-X0MBvNg3ZgYPrxKPN6O_0", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-sDlnoJL7Ccmx-thqfloV2", "label": "表格", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Q1NXilZSzSy69m-fzVMVA", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-", "label": "页签", "type": "Tabs"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-n5fObr4IR_jiJYm5rCr6N", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW--7zobvbAFQPxEPZ3Go2j1", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-hn4W9zZWlCaOIGY1MB5HX", "label": "创建人", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Q1NXilZSzSy69m-fzVMVA", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-", "label": "页签", "type": "Tabs"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-lFLHt4KsXbUMqH6-hAt-P", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-kxYNj6fgNWIg7LkIpA_Qa", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-8RXPjItfUa9N6G78gUMsZ", "label": "更新人", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Q1NXilZSzSy69m-fzVMVA", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-", "label": "页签", "type": "Tabs"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-lFLHt4KsXbUMqH6-hAt-P", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-kxYNj6fgNWIg7LkIpA_Qa", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-JpVj6V9pEBYkQ_Mo4kznm", "label": "创建时间", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Q1NXilZSzSy69m-fzVMVA", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-", "label": "页签", "type": "Tabs"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-lFLHt4KsXbUMqH6-hAt-P", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-kxYNj6fgNWIg7LkIpA_Qa", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-BeG6R9n6j9A-LJpb_jAk4", "label": "更新时间", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Q1NXilZSzSy69m-fzVMVA", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-", "label": "页签", "type": "Tabs"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-lFLHt4KsXbUMqH6-hAt-P", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-kxYNj6fgNWIg7LkIpA_Qa", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Lhr23w1PfvHhzPGWC3ChD", "label": "组织身份", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Q1NXilZSzSy69m-fzVMVA", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-", "label": "页签", "type": "Tabs"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-n5fObr4IR_jiJYm5rCr6N", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW--7zobvbAFQPxEPZ3Go2j1", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-sDlnoJL7Ccmx-thqfloV2", "label": "表格", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-KQEGWxqQjWW63PrXk2urK", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$ORG_IDENTITY_CF_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_identity_cf"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_identity_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-6A5oZ5vZ13bA1sZBIUKmz", "label": "组织单元", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Q1NXilZSzSy69m-fzVMVA", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-", "label": "页签", "type": "Tabs"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-n5fObr4IR_jiJYm5rCr6N", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW--7zobvbAFQPxEPZ3Go2j1", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-sDlnoJL7Ccmx-thqfloV2", "label": "表格", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-KQEGWxqQjWW63PrXk2urK", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-dsHvvlWUISVKi1xBAIWKd", "label": "是否主组织", "path": [{"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-Q1NXilZSzSy69m-fzVMVA", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-gMpRhfvNH7uGOX1Eh075-", "label": "页签", "type": "Tabs"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-n5fObr4IR_jiJYm5rCr6N", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW--7zobvbAFQPxEPZ3Go2j1", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-sDlnoJL7Ccmx-thqfloV2", "label": "表格", "type": "Table"}, {"key": "TSRM$NEW_ORG_EMPLOYEE_VIEW-KQEGWxqQjWW63PrXk2urK", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "detail", "type": "DETAIL"}, "type": "View"}