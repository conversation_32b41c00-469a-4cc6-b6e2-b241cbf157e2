{"access": "Private", "description": "{}", "key": "TSRM$ORG_EMPLOYEE_PERSONAL_INFORMATION_EDIT_EVENT_SERVICE", "name": "ORG_个人信息_修改个人信息服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "sys_common$org_employee_md", "name": "员工信息表\t"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$ORG_EMPLOYEE_PERSONAL_INFORMATION_EDIT_EVENT_perm_ac", "relations": [{"actionType": "ServiceDefinition", "code": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_CHECK", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE", "sourceCode": null}, {"actionType": "ServiceDefinition", "code": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": "TSRM$ORG_EMPLOYEE_MD_ADM_USER_SAVE_SERVICE", "sourceCode": null}, {"actionType": "ServiceDefinition", "code": "TSRM$ORG_EMPLOYEE_MD_ADM_USER_SAVE_SERVICE", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": null, "sourceCode": null}], "returnModel": {"children": null, "desc": null, "key": "TSRM$user", "name": "用户"}, "returnModelArrayWhether": false, "states": []}, "isDeleted": null, "isEnabled": true, "modelKey": "sys_common$org_employee_md", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_40c0db7414", "name": "开始", "props": {"globalVariable": [{"elements": null, "fieldAlias": "TSRM_ORG_EMPLOYEE_MD_ADM_USER_SAVE_SERVICE", "fieldKey": "TSRM_ORG_EMPLOYEE_MD_ADM_USER_SAVE_SERVICE", "fieldName": "[ORG_员工管理_员工账号信息同步保存数据]节点出参", "fieldType": "Object", "id": null}], "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}}], "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "用户"}}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_4f77b75892", "name": "ORG_员工管理_保存校验", "props": {"async": false, "inputMapping": [{"field": {"elements": [], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "valueKey": "request", "valueName": "request"}]}}, {"field": {"elements": [], "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Object", "id": null}, "id": null, "value": {"constValue": "sys_common$org_employee_md", "fieldType": "Text", "id": null, "type": "ConstValue"}}], "output": null, "outputAssign": null, "serviceKey": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_CHECK", "serviceName": "ORG_员工管理_保存校验", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"desc": null, "id": null, "key": "node_fd39efc968", "name": "ORG_员工管理_数据保存", "props": {"async": false, "inputMapping": [{"field": {"elements": [], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "valueKey": "request", "valueName": "request"}]}}, {"field": {"elements": [], "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Object", "id": null}, "id": null, "value": {"constValue": "sys_common$org_employee_md", "fieldType": "Text", "id": null, "type": "ConstValue"}}], "output": null, "outputAssign": null, "serviceKey": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE", "serviceName": "ORG_员工管理_数据保存", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"desc": null, "id": null, "key": "node_a6bc0d0685", "name": "ORG_员工管理_员工账号信息同步保存数据", "props": {"async": false, "inputMapping": [{"field": {"elements": [], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "valueKey": "request", "valueName": "request"}]}}, {"field": {"elements": [], "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Object", "id": null}, "id": null, "value": {"constValue": "sys_common$org_employee_md", "fieldType": "Text", "id": null, "type": "ConstValue"}}], "output": [{"fieldAlias": "user", "fieldKey": "user", "fieldName": "user", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_a6bc0d0685", "valueName": "节点出参"}]}}, {"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "TSRM_ORG_EMPLOYEE_MD_ADM_USER_SAVE_SERVICE", "valueName": "[ORG_员工管理_员工账号信息同步保存数据]节点出参"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_a6bc0d0685", "valueName": "节点出参"}]}}], "outputAssignType": "CUSTOM"}, "serviceKey": "TSRM$ORG_EMPLOYEE_MD_ADM_USER_SAVE_SERVICE", "serviceName": "ORG_员工管理_员工账号信息同步保存数据", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"desc": null, "id": null, "key": "node_6ea7728dca", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}}], "key": "TSRM$ORG_EMPLOYEE_PERSONAL_INFORMATION_EDIT_EVENT_SERVICE", "name": "ORG_个人信息_修改个人信息服务", "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "用户"}}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$ORG_EMPLOYEE_PERSONAL_INFORMATION_EDIT_EVENT_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}