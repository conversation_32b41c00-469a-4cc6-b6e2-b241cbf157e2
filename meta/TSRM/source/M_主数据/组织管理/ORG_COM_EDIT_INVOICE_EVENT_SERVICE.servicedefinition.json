{"access": "Private", "description": "{}", "key": "TSRM$ORG_COM_EDIT_INVOICE_EVENT_SERVICE", "name": "ORG_公司管理_维护开票信息服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "ERP_GEN$gen_com_type_cf", "name": "公司配置表"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$ORG_COM_EDIT_INVOICE_EVENT_perm_ac", "relations": [{"actionType": "ServiceDefinition", "code": "TSRM$SYS_SaveDataService", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": null, "sourceCode": null}], "returnModel": null, "returnModelArrayWhether": false, "states": []}, "isDeleted": null, "isEnabled": true, "modelKey": "ERP_GEN$gen_com_type_cf", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_b21f0df783", "name": "开始", "props": {"globalVariable": [{"elements": null, "fieldAlias": "TSRM_SYS_SaveDataService", "fieldKey": "TSRM_SYS_SaveDataService", "fieldName": "[(系统)保存数据服务]节点出参", "fieldType": "Object", "id": null}], "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}}], "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_089f3c4e19", "name": "(系统)保存数据服务", "props": {"async": false, "inputMapping": [{"field": {"elements": [], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": "ERP_GEN$gen_com_type_cf", "valueKey": "request", "valueName": "request"}]}}, {"field": {"elements": [], "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Object", "id": null}, "id": null, "value": {"constValue": "ERP_GEN$gen_com_type_cf", "fieldType": "Text", "id": null, "type": "ConstValue"}}], "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "${modelKey}", "modelKey": "${modelKey}", "modelName": "${modelKey}"}}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_089f3c4e19", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}, {"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "TSRM_SYS_SaveDataService", "valueName": "[(系统)保存数据服务]节点出参"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_089f3c4e19", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "serviceKey": "TSRM$SYS_SaveDataService", "serviceName": "(系统)保存数据服务", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"desc": null, "id": null, "key": "node_964d7d72af", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}}], "key": "TSRM$ORG_COM_EDIT_INVOICE_EVENT_SERVICE", "name": "ORG_公司管理_维护开票信息服务", "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$ORG_COM_EDIT_INVOICE_EVENT_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}