{"access": "Public", "key": "TSRM$ORG_GET_EMPLOYEE_BY_CURRENT_USER", "name": "ORG_根据当前用户获取员工", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"aiChatMode": false, "aiRoundsStrategy": null, "aiService": false, "children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk661jkq1", "name": "开始", "nextNodeKey": "node_1hk662v543", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": null, "name": null, "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk662v543", "name": "查询数据", "nextNodeKey": "node_1hk665se44", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "xZ6et10q6EDF_ajfZ1nYZ", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "userId", "valueName": "用户"}, {"modelAlias": "TSRM$user", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "SYS", "valueName": "系统变量"}, {"modelAlias": null, "relatedModel": null, "valueKey": "CurrentUserId", "valueName": "当前登录人id"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "9BMGSJZKYaw0F12PkBBgc", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "PPIhWrZeRvue_Z9S0J7WX", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk665se44", "name": "赋值", "nextNodeKey": "node_1hk661jkq2", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "id": "1hk665tpt5", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hk662v543", "valueName": "[查询数据]节点.output"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk661jkq2", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hk661jkq1"], "id": null, "input": null, "key": "COMMON_2B$ORG_GET_EMPLOYEE_BY_CURRENT_USER", "name": "ORG_根据当前用户获取员工", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "desc": null, "name": null, "permissionKey": "TSRM$ORG_GET_EMPLOYEE_BY_CURRENT_USER_perm_ac", "teamId": 35, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}