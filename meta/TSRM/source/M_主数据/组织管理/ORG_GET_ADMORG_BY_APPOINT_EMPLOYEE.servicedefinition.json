{"access": "Public", "key": "TSRM$ORG_GET_ADMORG_BY_APPOINT_EMPLOYEE", "name": "ORG_根据指定员工获取行政组织", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk66eq1j11", "name": "开始", "nextNodeKey": "node_1hko872f05", "preNodeKey": null, "props": {"desc": null, "globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "empid", "fieldKey": "empid", "fieldName": "empID", "fieldType": "Number", "id": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Number", "id": null, "required": null}], "name": null, "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": null}, "relation": null, "required": null}], "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hko872f05", "name": "赋值", "nextNodeKey": "node_1hk66fntq13", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": null, "valueKey": "empid", "valueName": "empID"}]}, "id": "1hko873kc6", "operator": "EQ", "value": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk66fntq13", "name": "查询数据", "nextNodeKey": "node_1hk66h92514", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "OA1rOxi1Xe4LvyIuLwhJS", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": null, "valueKey": "orgEmployeeMdId", "valueName": "orgEmployeeMdId"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": null, "valueKey": "empid", "valueName": "empID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "4HuuLXBHvV18kCHQgAHJQ", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "-r8YrRLyu06HgSaACRZL1", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "员工行政组织关联配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"conditionGroup": null, "fieldAlias": "admOrg", "modelAlias": "sys_common$org_struct_md", "subQueryRelatedModels": []}], "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk66h92514", "name": "赋值", "nextNodeKey": "node_1hk66eq1j12", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "id": "1hko9182u7", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "员工行政组织关联配置表"}, "valueKey": "NODE_OUTPUT_node_1hk66fntq13", "valueName": "[查询数据]节点.output"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_adm_org_cf"}, "valueKey": "admOrg", "valueName": "行政组织"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk66eq1j12", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hk66eq1j11"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Number", "id": null, "required": null}], "key": "COMMON_2B$ORG_GET_ADMORG_BY_APPOINT_EMPLOYEE", "name": "ORG_根据指定员工获取行政组织", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": null}, "relation": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "desc": null, "name": null, "permissionKey": "TSRM$ORG_GET_ADMORG_BY_APPOINT_EMPLOYEE_perm_ac", "teamId": 35, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}