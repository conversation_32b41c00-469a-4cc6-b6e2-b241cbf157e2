{"access": "Private", "key": "TSRM$EMPLOYEE_CONVERT_ORG_AND_IDENTITY_ACTION", "name": "员工模型转成组织和身份", "props": {"bean": "TsrmOrgAction", "desc": null, "groovyScript": null, "languageType": "Java", "method": "employeeConvertOrgAndIdentity", "order": 10, "requestType": "io.terminus.tsrm.md.spi.model.org.dto.OrgEmployeeMdDTO", "responseType": "io.terminus.tsrm.md.spi.model.org.dto.OrgAndIdentityResDTO", "returnModel": null, "status": "enabled"}, "type": "Action"}