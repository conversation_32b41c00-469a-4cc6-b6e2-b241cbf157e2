{"access": "Public", "key": "TSRM$ORG_COM_SINGLE_QUERY", "name": "ORG_根据库存组织名称查询服务", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1he265pn71", "name": "开始", "nextNodeKey": "node_1he2660om3", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "companyName", "fieldKey": "companyName", "fieldName": "关联公司名称", "fieldType": "Text", "id": null, "required": null}], "name": null, "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": null}, "relation": null, "required": null}], "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1he2660om3", "name": "查询数据", "nextNodeKey": "node_1he265pn72", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "z9ZDp2v5i0GM9maWhnecs", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_struct_md", "relatedModel": null, "valueKey": "name", "valueName": "组织名称"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": null, "valueKey": "companyName", "valueName": "关联公司名称"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "0VCNbevHx-1fF_Cs4-GR7", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "ewyxGcWLsSdq0rjkpb735", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": 10, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "id": "1he27snc9288", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "库存组织配置表"}, "valueKey": "NODE_OUTPUT_node_1he2660om3", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "库存组织配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"conditionGroup": null, "fieldAlias": "extWqComInfo", "modelAlias": "ERP_GEN$gen_com_type_cf", "subQueryRelatedModels": []}], "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1he265pn72", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1he265pn71"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "companyName", "fieldKey": "companyName", "fieldName": "关联公司名称", "fieldType": "Text", "id": null, "required": null}], "key": "COMMON_2B$ORG_COM_SINGLE_QUERY", "name": "ORG_根据库存组织名称查询服务", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": null}, "relation": null, "required": null}], "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_COM_SINGLE_QUERY_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}