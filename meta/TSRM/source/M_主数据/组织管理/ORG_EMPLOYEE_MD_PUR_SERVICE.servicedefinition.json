{"access": "Private", "key": "TSRM$ORG_EMPLOYEE_MD_PUR_SERVICE", "name": "ORG_员工管理_分配采购组织", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"aiChatMode": false, "aiRoundsStrategy": null, "aiService": false, "children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcr4nvv87", "name": "开始", "nextNodeKey": "node_1hcr4p7kh9", "preNodeKey": null, "props": {"desc": null, "globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "empSave", "fieldKey": "empSave", "fieldName": "组织角色员工保存", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelKey": "TERP_MIGRATE$ext_wq_employee_save_temp", "modelName": null}, "relation": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "emp", "fieldKey": "emp", "fieldName": "emp", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "modelKey": "TERP_MIGRATE$org_employee_org_link_cf", "modelName": null}, "relation": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "modelKey": "TERP_MIGRATE$org_employee_org_link_cf", "modelName": null}, "relation": null, "required": null}, "fieldAlias": "empArr", "fieldKey": "empArr", "fieldName": "empArr", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "user", "fieldKey": "user", "fieldName": "user", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$ext_wq_employee_user_temp", "modelKey": "TSRM$ext_wq_employee_user_temp", "modelName": null}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcr4p7kh9", "name": "查询数据", "nextNodeKey": "node_1hcr4qh6r12", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "OWpsNpAvsqdJcpD9220OG", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "1H73XyvpSX7qD-TYbBMUq", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "cr7J2XYDpy9rHaEgcPzJR", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "id": "1hcr4prui10", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hcr4p7kh9", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcr4qh6r12", "name": "赋值", "nextNodeKey": "node_1hcr4r44j14", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "extWqPurOrg", "valueName": "采购组织"}]}, "id": "1hcr4qiie13", "operator": "EQ", "value": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_pur_org_cf", "modelKey": "TB2B$ext_wq_employee_pur_org_cf", "modelName": "TERP_MIGRATE$ext_wq_employee_pur_org_cf"}, "valueKey": "extWqPurOrg", "valueName": "采购组织"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcr4r44j14", "name": "更新数据", "nextNodeKey": "node_1hcr4nvv88", "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcr4nvv88", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hcr4nvv87"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$ORG_EMPLOYEE_MD_PUR_SERVICE", "name": "ORG_员工管理_分配采购组织", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "desc": null, "name": null, "permissionKey": "TSRM$ORG_EMPLOYEE_MD_PUR_SERVICE_perm_ac", "teamId": 35, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}