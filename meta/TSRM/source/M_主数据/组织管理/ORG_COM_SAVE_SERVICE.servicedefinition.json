{"access": "Private", "key": "TSRM$ORG_COM_SAVE_SERVICE", "name": "ORG_公司管理_保存", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrr0al016", "name": "开始", "nextNodeKey": "node_1hcrr11g918", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrr11g919", "name": "新增", "nextNodeKey": "node_1hcrr312p21", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "OkSqGt8JKFZPGEBhl7zNJ", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "UF8AvXckXMmXrxCky2FTD", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "pGBx8aoD5B76vt_k-Y-EE", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "elseFlag": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrr312p21", "name": "查询数据", "nextNodeKey": "node_1hcrr79g522", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "ZXR9aslc-iUtm8cZ9dBc3", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": null, "valueKey": "name", "valueName": "公司名称"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "name", "valueName": "公司名称"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "fcZsFWeKYyM4zWNGHFZOF", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": null, "valueKey": "socialcreditCode", "valueName": "统一社会信用代码"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "socialcreditCode", "valueName": "统一社会信用代码"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "WcRnJ_6l9s3LhHRlIdupg", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": null, "valueKey": "code", "valueName": "公司编码"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "公司编码"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "YnDT0aUY8OUynU4PYa4L4", "logicOperator": "OR", "type": "ConditionGroup"}], "id": "AmVHPByW-hUF45m9Atyy7", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrr79gk23", "name": "条件", "nextNodeKey": "node_1hcrr8dgu25", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "HXE6AcKyDKBaaZlAwDsZT", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}, "valueKey": "NODE_OUTPUT_node_1hcrr312p21", "valueName": "[查询数据]节点.output"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "yD9lgFxNbr1_jBzALAxMo", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "3XU6RbTnJkAI4obmE9Jf-", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "elseFlag": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrr8dgu25", "name": "新增数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}, "type": "CascadeCreateDataProperties"}, "renderType": null, "type": "CascadeCreateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrr79gk24", "name": "条件", "nextNodeKey": "node_1hcrr91n426", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "RSwZuVkg3v0rpaOLQbMJW", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}, "valueKey": "NODE_OUTPUT_node_1hcrr312p21", "valueName": "[查询数据]节点.output"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "P6w_m3GD9AeXlwQHcUOZ2", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Dj3ulScfLe83M5q9xRnvH", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "elseFlag": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrr91n426", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "公司编码、公司名称或公司代码不能重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hcrr79gk23", "node_1hcrr79gk24"], "id": null, "key": "node_1hcrr79g522", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrr11g920", "name": "修改", "nextNodeKey": "node_1hcrr9ree27", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "jf2b0n2QDLzX_f_WtoDXe", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "CcqtDsJolcev7pUtuEZxS", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "T1XFmNXnmGN5Jr0j69Bf3", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "elseFlag": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrr9ree27", "name": "查询数据", "nextNodeKey": "node_1hcrrb5mv28", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "EiMgzkvMy93lmcX_YbmKN", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": null, "valueKey": "name", "valueName": "公司名称"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "name", "valueName": "公司名称"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "8tKl3yMId9faUMgJRusvc", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": null, "valueKey": "socialcreditCode", "valueName": "统一社会信用代码"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "socialcreditCode", "valueName": "统一社会信用代码"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "UyZjRril51Y8cz3ZjnOM_", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": null, "valueKey": "code", "valueName": "公司编码"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "公司编码"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "aGiUlDuvcjD1DC_Tzn4fk", "logicOperator": "OR", "type": "ConditionGroup"}, {"conditions": [{"id": "kQ345SQ5R2wbO-lGbXh1F", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "NEQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "5r26Ybz4QZo5iu6ne05Ec", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "DXMsAMwIKWU7RD9dzwNr0", "logicOperator": "AND", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrrb5mv29", "name": "条件", "nextNodeKey": "node_1hcrrbvq931", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "KhVHWQ58H5qETMeQ9gKZe", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}, "valueKey": "NODE_OUTPUT_node_1hcrr9ree27", "valueName": "[查询数据]节点.output"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "WLukX3P4knlO-SvWjI-3H", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "ofwHV2oqvEXSNcR3uwEVB", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "elseFlag": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrrbvq931", "name": "更新数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrrb5mv30", "name": "条件", "nextNodeKey": "node_1hcrrcleg32", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "FhgmV_27o0XZjE2Y9kFjN", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "公司配置表"}, "valueKey": "NODE_OUTPUT_node_1hcrr9ree27", "valueName": "[查询数据]节点.output"}, {"modelAlias": "ERP_GEN$gen_com_type_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "hamq0YiWNl0xoZ6Ym9FJt", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "-IEKf7BhaVgpE7JgLbkxV", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "elseFlag": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrrcleg32", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "公司编码、公司名称或公司代码不能重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hcrrb5mv29", "node_1hcrrb5mv30"], "id": null, "key": "node_1hcrrb5mv28", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}], "headNodeKeys": ["node_1hcrr11g919", "node_1hcrr11g920"], "id": null, "key": "node_1hcrr11g918", "name": "排他分支", "nextNodeKey": "node_1hcrr0al017", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcrr0al017", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hcrr0al016"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$ORG_COM_SAVE_SERVICE", "name": "ORG_公司管理_保存", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_COM_SAVE_SERVICE_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}