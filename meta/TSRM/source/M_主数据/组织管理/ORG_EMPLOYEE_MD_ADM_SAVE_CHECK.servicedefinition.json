{"access": "Private", "key": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_CHECK", "name": "ORG_员工管理_保存校验", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcunc29j1", "name": "开始", "nextNodeKey": "node_1hcundp153", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "员工信息", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcundp154", "name": "条件", "nextNodeKey": "node_1hcuo0qti9", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "bMtBuR-XAtv3B4SBvzWMt", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "8z0VSrvcxaYZmzeAE225-", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "pjSMfiVH-Wv21E1YNAHRn", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuo0qti9", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$EMPLOYEE_TEL_NULL", "errorMsg": "员工手机号码为空，请确认输入的员工手机号码是否正确！", "link": "MetaLink$ErrorCode$COMMON_2B$EMPLOYEE_TEL_NULL", "name": null, "placeholderMapping": [], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuo5fts14", "name": "条件", "nextNodeKey": "node_1hcuo6ooa15", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "kgt7ttyKebwLe2NALH6DA", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}, {"id": "jkSLMjVKIrQs5EU7CkeEw", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "jK2h7Ixbc0AvfJNMX4zj7", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "NqiMuaj6CH9qpvW6iQyQM", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuo6ooa15", "name": "查询数据", "nextNodeKey": "node_1hcuo9e5p16", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "JTmg5QeiiCZnlX2VWZ4Pd", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "i-pUqZGmJZfSOthvNCt6T", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "NEQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "a_Lgrw7phxplxvQMu0AiJ", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "zBI0tHz76KitMd_QKghje", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "ARRAY", "desc": null, "maximum": 10, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuo9teo17", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$EMPLOYEE_TEL_EXISTED", "errorMsg": "员工手机号码${phone}已存在，请确认输入的手机号码是否正确！", "link": "MetaLink$ErrorCode$COMMON_2B$EMPLOYEE_TEL_EXISTED", "name": null, "placeholderMapping": [{"id": null, "key": "phone", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hcuo9e5p16", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hcuo9teo17"], "id": null, "key": "node_1hcuo9e5p16", "name": "循环", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "loopData": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hcuo6ooa15", "valueName": "[查询数据]节点.output"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "NODE_OUTPUT_node_1hcuo6ooa15", "fieldKey": "NODE_OUTPUT_node_1hcuo6ooa15", "fieldName": "[查询数据]节点.output", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "relation": null, "required": null}, "loopType": "DATASET_LOOP", "name": null, "stopWhenDataEmpty": false, "type": "LoopProperties"}, "renderType": null, "type": "LoopNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcundp155", "name": "条件", "nextNodeKey": "node_1hcuo1bb310", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "enBAbhOgp8A_3-MQyqSYZ", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}, {"id": "JuavcEk24annNj0P8_6SL", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "ub1DFrRMelkcO2twsvRo9", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "HVAgArp0NEDg-KBU1YSQc", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuo1bb310", "name": "查询数据", "nextNodeKey": "node_1hcuoap2618", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "X_jLgg_rTeR8d4PnRWNH1", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "7uJyUDbJxLX9MVgB8S_u0", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Bi-9iMPprSlUM8pTv1U1D", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "ARRAY", "desc": null, "maximum": 10, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuob25a19", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$EMPLOYEE_TEL_EXISTED", "errorMsg": "员工手机号码${phone}已存在，请确认输入的手机号码是否正确！", "link": "MetaLink$ErrorCode$COMMON_2B$EMPLOYEE_TEL_EXISTED", "name": null, "placeholderMapping": [{"id": null, "key": "phone", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hcuoap2618", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hcuob25a19"], "id": null, "key": "node_1hcuoap2618", "name": "循环", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "loopData": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hcuo1bb310", "valueName": "[查询数据]节点.output"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "NODE_OUTPUT_node_1hcuo1bb310", "fieldKey": "NODE_OUTPUT_node_1hcuo1bb310", "fieldName": "[查询数据]节点.output", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "relation": null, "required": null}, "loopType": "DATASET_LOOP", "name": null, "stopWhenDataEmpty": false, "type": "LoopProperties"}, "renderType": null, "type": "LoopNode"}], "headNodeKeys": ["node_1hcundp154", "node_1hcuo5fts14", "node_1hcundp155"], "id": null, "key": "node_1hcundp153", "name": "排他分支", "nextNodeKey": "node_1hcung5v66", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcung5v67", "name": "条件", "nextNodeKey": "node_1hcuobt1620", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "3wYNNut--xsfMtXbbaPc0", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "WSFNeqbnXQlHYZKS2Z1g0", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "ixAmdu2BWnzODh0wXMSo9", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuobt1620", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$EMPLOYEE_MAIL_NULL", "errorMsg": "员工电子邮箱为空，请确认输入的员工电子邮箱是否正确！", "link": "MetaLink$ErrorCode$COMMON_2B$EMPLOYEE_MAIL_NULL", "name": null, "placeholderMapping": [], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuocph721", "name": "条件", "nextNodeKey": "node_1hcuoekme22", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "YD2dcso6BjIpG1TJWkg_U", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}, {"id": "iE5V1lpfLIl6DzjRoklxx", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "VGWKFBPDa8HXe3JsLXMBU", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "riQEFvOoTWVU0ifMQEKnu", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuoekme22", "name": "查询数据", "nextNodeKey": "node_1hcuoft4p23", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "bjEJR8L2-7cg9jbs8zzlh", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "NEQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "hUAPHIJLs9cWiThv7sMoj", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "email", "valueName": "邮箱"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "BmGWhsgPlYS4WvkhrCM1c", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "HJvtJsfL0tLeN4EszoExw", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "ARRAY", "desc": null, "maximum": 10, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuog6ks24", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$EMPLOYEE_MAIL_EXISTED", "errorMsg": "员工电子邮箱${mail}已存在，请确认输入的电子邮箱是否正确！\n", "link": "MetaLink$ErrorCode$COMMON_2B$EMPLOYEE_MAIL_EXISTED", "name": null, "placeholderMapping": [{"id": null, "key": "mail", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hcuoft4p23", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hcuog6ks24"], "id": null, "key": "node_1hcuoft4p23", "name": "循环", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "loopData": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hcuoekme22", "valueName": "[查询数据]节点.output"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "NODE_OUTPUT_node_1hcuoekme22", "fieldKey": "NODE_OUTPUT_node_1hcuoekme22", "fieldName": "[查询数据]节点.output", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "relation": null, "required": null}, "loopType": "DATASET_LOOP", "name": null, "stopWhenDataEmpty": false, "type": "LoopProperties"}, "renderType": null, "type": "LoopNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcung5v68", "name": "条件", "nextNodeKey": "node_1hcuoh1pj25", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "sQ_dYSDEnXfUEke-MGvCJ", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}, {"id": "LHG1m6Na_fo3O_epecOTN", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "ASpXXV5QKI3IQ60kULADN", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "vs-heIhP-bkSPx0jMFl8r", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuoh1pj25", "name": "查询数据", "nextNodeKey": "node_1hcuohv1326", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "DA11OTcDm0TTvoyuYXDLR", "key": null, "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "email", "valueName": "邮箱"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "员工信息"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "NZxBIEKQjMQ6xFa9k6jA7", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "F_JcOP7nNO-GSAzsKbH2F", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "ARRAY", "desc": null, "maximum": 10, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcuoi9r927", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$EMPLOYEE_MAIL_EXISTED", "errorMsg": "员工电子邮箱${mail}已存在，请确认输入的电子邮箱是否正确！\n", "link": "MetaLink$ErrorCode$COMMON_2B$EMPLOYEE_MAIL_EXISTED", "name": null, "placeholderMapping": [{"id": null, "key": "mail", "value": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hcuohv1326", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "_item", "valueName": "循环元素"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hcuoi9r927"], "id": null, "key": "node_1hcuohv1326", "name": "循环", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "loopData": {"constValue": null, "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hcuoh1pj25", "valueName": "[查询数据]节点.output"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "NODE_OUTPUT_node_1hcuoh1pj25", "fieldKey": "NODE_OUTPUT_node_1hcuoh1pj25", "fieldName": "[查询数据]节点.output", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "relation": null, "required": null}, "loopType": "DATASET_LOOP", "name": null, "stopWhenDataEmpty": false, "type": "LoopProperties"}, "renderType": null, "type": "LoopNode"}], "headNodeKeys": ["node_1hcung5v67", "node_1hcuocph721", "node_1hcung5v68"], "id": null, "key": "node_1hcung5v66", "name": "排他分支", "nextNodeKey": "node_1hcunc29j2", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcunc29j2", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hcunc29j1"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "员工信息", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$ORG_EMPLOYEE_MD_ADM_SAVE_CHECK", "name": "ORG_员工管理_保存校验", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_CHECK_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}