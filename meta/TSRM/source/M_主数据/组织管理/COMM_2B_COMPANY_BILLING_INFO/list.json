{"access": "Private", "key": "TSRM$COMM_2B_COMPANY_BILLING_INFO:list", "name": "list", "props": {"containerSelect": {"COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail": [{"field": "code", "selectFields": null}, {"field": "name", "selectFields": null}, {"field": "socialcreditCode", "selectFields": null}, {"field": "comCorporation", "selectFields": null}, {"field": "bizStatus", "selectFields": null}, {"field": "foundDate", "selectFields": null}, {"field": "openDate", "selectFields": null}, {"field": "closeDate", "selectFields": null}, {"field": "registeredCapital", "selectFields": null}, {"field": "extWqCapitalUnit", "selectFields": null}, {"field": "extWqIsSignature", "selectFields": null}, {"field": "enterpriseType", "selectFields": null}, {"field": "status", "selectFields": null}, {"field": "taxpayersNum", "selectFields": null}, {"field": "taxpayersType", "selectFields": null}, {"field": "addressId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "addrName", "selectFields": null}]}, {"field": "addressDetail", "selectFields": null}, {"field": "phone", "selectFields": null}, {"field": "bankName", "selectFields": null}, {"field": "bankaccount", "selectFields": null}, {"field": "extWqGraphicSeal", "selectFields": []}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}], "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form": [{"field": "version", "selectFields": null}, {"field": "taxpayersNum", "selectFields": null}, {"field": "taxpayersType", "selectFields": null}, {"field": "addressId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "addrName", "selectFields": null}]}, {"field": "addressDetail", "selectFields": null}, {"field": "phone", "selectFields": null}, {"field": "bankName", "selectFields": null}, {"field": "bankaccount", "selectFields": null}]}, "content": {"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-list-TERP_MIGRATE$gen_com_type_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-list-TERP_MIGRATE$gen_com_type_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-bXPoEVY1ScMYAJir4sWew", "name": "Status", "props": {"text$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "type$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.type"}}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"公司配置表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-list-TERP_MIGRATE$gen_com_type_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_COMPANY_BILLING_INFO-list_perm_ac_z_0_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_COMPANY_BILLING_INFO-list_perm_ac_z_0_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$COMM_2B_COMPANY_BILLING_INFO-list_perm_ac_z_0_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$COMM_2B_COMPANY_BILLING_INFO-list_perm_ac_z_0_1_0_1_0_3", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "IS_NOT_NULL", "rightValue": null}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-Nz3x2kvi5EJ3ji7VKk9_E", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "buttonType": "default", "confirmOn": "off", "label": "维护开票信息", "permissionKey": "TSRM$COMM_2B_COMPANY_BILLING_INFO-list_perm_ac_z_0_1_0_1_0_4", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_COMPANY_BILLING_INFO-list_perm_ac_z_0_1_0_1_0_5", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detail-TERP_MIGRATE$gen_com_type_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-code", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "公司编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-name", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "公司名称", "name": "name", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "socialcreditCode", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "公司代码", "name": "socialcreditCode", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "comCorporation", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "法定代表人", "name": "comCorporation", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bizStatus", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Enum", "editable": false, "label": "经营状态", "name": "bizStatus", "type": "SELECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-foundDate", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "foundDate", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "label": "成立日期", "name": "foundDate", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-0gDO0l_UHDaNesFWNJUyl", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "openDate", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "initialValue": null, "label": "营业期限开始日期", "name": "openDate", "required": false, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-pTJl5RfqltNIVgiyn0_dT", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "closeDate", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "initialValue": null, "label": "营业期限结束日期", "name": "closeDate", "required": false, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "registeredCapital", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "注册资本", "name": "registeredCapital", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-4WfHSuMkdrtgAI4-BWGw1", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqCapitalUnit", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "editable": false, "initialValue": null, "label": "注册资本单位", "name": "extWqCapitalUnit", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-GhpBWXaB6_sMSnGLE9IOy", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqIsSignature", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editable": false, "initialValue": null, "label": "是否开通签章", "name": "extWqIsSignature", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "enterpriseType", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Enum", "editable": false, "label": "企业类型", "name": "enterpriseType", "type": "SELECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-status", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Enum", "editable": false, "label": "公司状态", "name": "status", "type": "SELECT"}}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "name": "DetailGroupItem", "props": {"title": "基础信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxpayersNum", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "纳税人识别号", "name": "taxpayersNum", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-DcP9_3VZbGYkSOmnTepZZ", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxpayersType", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "纳税人类别", "name": "taxpayersType", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择注册地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "注册地址", "name": "addressId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "注册详细地址", "name": "addressDetail", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-phone", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "phone", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "手机", "name": "phone", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankName", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bankName", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "银行名称", "name": "bankName", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bankaccount", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "银行账户", "name": "bankaccount", "type": "TEXT"}}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AGxtUZQ-Nk0BH28OVCfWV", "name": "DetailGroupItem", "props": {"title": "开票信息"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-0NrZmi4di-5bptuSbJahN", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-NUV1MnmPcDigIcSI8X583", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-SOrMjPz1-AbHdmZcjBRVe", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "Hp_4Z7J3OiN1EajEJelQH", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_gen_com_graphic_seal_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "HmHqRF9aIDZKHHomOR0Pg", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "graphicSealType", "modelAlias": "TB2B$ext_wq_gen_com_graphic_seal_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "图形章类型", "name": "graphicSealType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "JdzO_2Ah62TnOh0StjxpU", "name": "Field", "props": {"componentProps": {"fieldAlias": "graphicSeal", "modelAlias": "TB2B$ext_wq_gen_com_graphic_seal_cf", "placeholder": "请上传"}, "hidden": false, "initialValue": null, "label": "图形章", "name": "graphicSeal", "required": false, "type": "ATTACHMENT", "width": 120}, "type": "Widget"}], "key": "wHDdYncujvyrlcUCw98F7", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-FdhCwzhg227L_njkfQAZ6", "name": "Table", "props": {"enableSolution": false, "fieldName": "extWqGraphicSeal", "flow": {"context$": "$context", "name": "extWqGraphicSeal", "type": "RelationData"}, "label": "表格", "mode": "normal", "modelAlias": "TB2B$ext_wq_gen_com_graphic_seal_cf"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-9u2V7kTbtlqovo4Wo7BZu", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "label": "自定义详情字段"}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-uIMdPwJTlPsX4cqF0RxwD", "name": "DetailGroupItem", "props": {"title": "图形章"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-PVHUmC0vE3lVHyKEdNPKP", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "${data.name}开票信息", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "aA1_WdbDNp79rKxOXQjyO", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "ahIZAWi1FGyTtdOFuwaYW", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxpayersNum", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "纳税人识别号", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-RwH65PXoXKmgZ6QonwAvJ", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "socialcreditCode", "value": "ERP_GEN$gen_com_type_cf.socialcreditCode", "valueType": "model"}}], "name": "taxpayersNum", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AQBh9JSj1R290-vEHDG9L", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxpayersType", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "Select", "label": "纳税人类别", "name": "taxpayersType", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择注册地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"cascaderField": "addrParentId", "fields": [{"componentProps": {"fieldAlias": "addrName", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "地址库名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "OmPsp_zbrVwgEEfHvRqOA", "trigger": "auto", "valueRules": null}], "name": "addrName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-COMMON_2B$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "addrName", "leafOnly": false, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "editComponentType": "RelationSelect", "hidden": false, "label": "行政区划", "name": "addressId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "注册地址", "name": "addressDetail", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-phone", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "phone", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "电话号码", "name": "phone", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-bankName", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bankName", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "开户银行", "name": "bankName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bankaccount", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "银行账户", "name": "bankaccount", "rules": [], "type": "TEXT"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-_F8hRnGD7Vazek82TCbYF", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "开票信息"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "modelAlias": "ERP_GEN$gen_com_type_cf", "params$": "{ id: route.recordId }", "serviceKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "modelAlias": "ERP_GEN$gen_com_type_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "ERP_GEN$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_COMPANY_BILLING_INFO-list_perm_ac_z_0_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form"}, "type": "action"}}], "service": "ERP_GEN$SYS_MasterData_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh"}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_COMPANY_BILLING_INFO-list_perm_ac_z_0_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "公司管理"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["请输入版本号", "注册地址", "停用成功", "保存", "注册资本单位", "请输入ID", "开票信息", "用户名", "请上传", "ID", "创建人", "请输入更新时间", "保存成功!", "启用成功", "银行账户", "逻辑删除标识", "自定义详情字段", "请选择", "版本号", "更新时间", "营业期限开始日期", "行政区划", "纳税人类别", "编辑", "图形章类型", "选择创建人", "成立日期", "请输入逻辑删除标识", "确认删除吗？", "营业期限结束日期", "注册详细地址", "开户银行", "是否开通签章", "复制", "维护开票信息", "选择更新人", "公司代码", "公司配置表详情", "手机", "系统信息", "表格", "删除成功", "停用", "公司名称", "注册资本", "请输入创建时间", "删除", "启用", "银行名称", "纳税人识别号", "公司状态", "经营状态", "主体信息", "确认启用吗？", "电话号码", "图形章", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "法定代表人", "更新人", "选择注册地址", "请输入", "企业类型", "取消", "创建时间", "公司编码", "确认停用吗？", "地址库名称", "基础信息"], "i18nScanPaths": ["COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-id.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-enable.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedAt.props.rules.0.message", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-bXPoEVY1ScMYAJir4sWew.props.text$", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-deleted.props.rules.0.message", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-DcP9_3VZbGYkSOmnTepZZ.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-enable.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount.props.componentProps.placeholder", "Hp_4Z7J3OiN1EajEJelQH.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-4WfHSuMkdrtgAI4-BWGw1.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-status.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-code.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-action-save.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.componentProps.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-code.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-edit.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-disable.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-4WfHSuMkdrtgAI4-BWGw1.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType.props.label", "JdzO_2Ah62TnOh0StjxpU.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-phone.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankName.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-delete.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-disable.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-id.props.rules.0.message", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL.props.title", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-copy.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-0gDO0l_UHDaNesFWNJUyl.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AGxtUZQ-Nk0BH28OVCfWV.props.title", "HmHqRF9aIDZKHHomOR0Pg.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-createdAt.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-pTJl5RfqltNIVgiyn0_dT.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdBy.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedAt.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-version.props.componentProps.placeholder", "@exp:COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-title.props.title", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankName.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-updatedBy.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-phone.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-version.props.rules.0.message", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdAt.props.rules.0.message", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-9u2V7kTbtlqovo4Wo7BZu.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.label", "HmHqRF9aIDZKHHomOR0Pg.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-Nz3x2kvi5EJ3ji7VKk9_E.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.componentProps.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-foundDate.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-foundDate.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum.props.label", "Hp_4Z7J3OiN1EajEJelQH.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-bankName.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-name.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-createdBy.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-version.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs.props.items.1.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-pTJl5RfqltNIVgiyn0_dT.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-bankName.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-DcP9_3VZbGYkSOmnTepZZ.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-uIMdPwJTlPsX4cqF0RxwD.props.title", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-0gDO0l_UHDaNesFWNJUyl.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-deleted.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-status.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-phone.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-deleted.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-FdhCwzhg227L_njkfQAZ6.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-updatedBy.props.componentProps.label", "@exp:COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-page-title.props.title", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-enable.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-name.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedBy.props.componentProps.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-GhpBWXaB6_sMSnGLE9IOy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-updatedAt.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-GhpBWXaB6_sMSnGLE9IOy.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdAt.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-id.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AQBh9JSj1R290-vEHDG9L.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation.props.componentProps.placeholder", "JdzO_2Ah62TnOh0StjxpU.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-disable.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-_F8hRnGD7Vazek82TCbYF.props.title", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-action-cancel.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedBy.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AQBh9JSj1R290-vEHDG9L.props.label", "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-phone.props.label"]}, "key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-list", "permissionKey": "TSRM$COMM_2B_COMPANY_BILLING_INFO-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$SYS_MasterData_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-disable", "label": "停用", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_MasterData_DisableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-enable", "label": "启用", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_MasterData_EnableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-Nz3x2kvi5EJ3ji7VKk9_E", "label": "维护开票信息", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detail-TERP_MIGRATE$gen_com_type_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-version", "label": "版本号", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-PVHUmC0vE3lVHyKEdNPKP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-PVHUmC0vE3lVHyKEdNPKP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-PVHUmC0vE3lVHyKEdNPKP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-PVHUmC0vE3lVHyKEdNPKP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum", "label": "纳税人识别号", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AQBh9JSj1R290-vEHDG9L", "label": "纳税人类别", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId", "label": "行政区划", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-COMMON_2B$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail", "label": "注册地址", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-phone", "label": "电话号码", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-bankName", "label": "开户银行", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount", "label": "银行账户", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-WLGr_75njRsM53ubTQkCV", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-code", "label": "公司编码", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-name", "label": "公司名称", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode", "label": "公司代码", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation", "label": "法定代表人", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus", "label": "经营状态", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-foundDate", "label": "成立日期", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-0gDO0l_UHDaNesFWNJUyl", "label": "营业期限开始日期", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-pTJl5RfqltNIVgiyn0_dT", "label": "营业期限结束日期", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital", "label": "注册资本", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-4WfHSuMkdrtgAI4-BWGw1", "label": "注册资本单位", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-GhpBWXaB6_sMSnGLE9IOy", "label": "是否开通签章", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType", "label": "企业类型", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-status", "label": "公司状态", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum", "label": "纳税人识别号", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-DcP9_3VZbGYkSOmnTepZZ", "label": "纳税人类别", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressId", "label": "注册地址", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail", "label": "注册详细地址", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-phone", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankName", "label": "银行名称", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount", "label": "银行账户", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-FdhCwzhg227L_njkfQAZ6", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-uIMdPwJTlPsX4cqF0RxwD", "label": "图形章", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-9u2V7kTbtlqovo4Wo7BZu", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "Hp_4Z7J3OiN1EajEJelQH", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-uIMdPwJTlPsX4cqF0RxwD", "label": "图形章", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-9u2V7kTbtlqovo4Wo7BZu", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-FdhCwzhg227L_njkfQAZ6", "label": "表格", "type": "Table"}, {"key": "wHDdYncujvyrlcUCw98F7", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "HmHqRF9aIDZKHHomOR0Pg", "label": "图形章类型", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-uIMdPwJTlPsX4cqF0RxwD", "label": "图形章", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-9u2V7kTbtlqovo4Wo7BZu", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-FdhCwzhg227L_njkfQAZ6", "label": "表格", "type": "Table"}, {"key": "wHDdYncujvyrlcUCw98F7", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "JdzO_2Ah62TnOh0StjxpU", "label": "图形章", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY_BILLING_INFO-V-_kbEK0k0JiCCuhbo2Md", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-uIMdPwJTlPsX4cqF0RxwD", "label": "图形章", "type": "DetailGroupItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-9u2V7kTbtlqovo4Wo7BZu", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "COMMON_2B$COMM_2B_COMPANY_BILLING_INFO-FdhCwzhg227L_njkfQAZ6", "label": "表格", "type": "Table"}, {"key": "wHDdYncujvyrlcUCw98F7", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}