{"access": "Private", "key": "TSRM$COMM_2B_ORGANIZATION:list", "name": "list", "props": {"containerSelect": {"COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail": [], "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form": [], "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf": []}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "permissionKey": "TSRM$COMM_2B_ORGANIZATION-list_perm_ac_z_0_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_com_org_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "TERP_MIGRATE$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "COMMON_2B$COMM_2B_ORGANIZATION-TERP_MIGRATE$org_com_org_cf-multi-delete", "label": "批量删除", "permissionKey": "TSRM$COMM_2B_ORGANIZATION-list_perm_ac_z_0_0_0_1_items_0"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "bfD49Q8_0h9WZ1ZpZnhBd", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "2U3S41Np5YBLs9gXwN2In", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "label": "公司组织编码", "name": "code", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "fGJinRh4G_OzwX_A6DJYV", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "label": "公司组织名称", "name": "name", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "dHT4HclRsDpyRcbQns5Q9", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "label": "公司组织编码", "name": "code", "type": "TEXT", "width": 146}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "label": "公司组织名称", "name": "name", "type": "TEXT", "width": 146}], "flow": {"containerKey": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_com_org_cf"}}], "serviceKey": "TERP_MIGRATE$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": true, "label": "表格", "mode": "simple", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showFilterFields": true, "showScope": "all", "tableCondition": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "KqxnROpVA4benODXPvfxJ", "name": "Status", "props": {"text$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "type$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.type"}}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"公司组织配置表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_com_org_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TERP_MIGRATE$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_ORGANIZATION-list_perm_ac_z_0_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_ORGANIZATION-list_perm_ac_z_0_1_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_com_org_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TERP_MIGRATE$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$COMM_2B_ORGANIZATION-list_perm_ac_z_0_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_com_org_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TERP_MIGRATE$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$COMM_2B_ORGANIZATION-list_perm_ac_z_0_1_1_0_1_0_3", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "IS_NOT_NULL", "rightValue": null}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_ORGANIZATION-list_perm_ac_z_0_1_1_0_1_0_4", "showCondition": {"conditions": [], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detail-TERP_MIGRATE$org_com_org_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-code", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "组织编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-name", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "组织名称", "name": "name", "type": "TEXT"}}, {"children": [], "key": "xPLn9LNd_ct05g3dVNXGA", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "shortName", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "组织简称", "name": "shortName", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-comId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "comId", "label": "选择关联公司", "labelField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "parentModelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "关联公司", "name": "comId", "type": "OBJECT"}}, {"children": [], "key": "ck28Edbs3ilux9xcGzLWK", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqComType", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "editable": false, "initialValue": null, "label": "公司类型", "name": "extWqComType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-status", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "状态", "name": "status", "type": "SELECT"}}], "key": "LRe7V5FDI6Rkd6OA-SOiW", "name": "DetailGroupItem", "props": {"title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "ENu3pbqc0V17gQ4ZsIX94", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqNccCode", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "NCC组织编码", "name": "extWqNccCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ItNup8z_D_rwtb29pCYRB", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqNcCodeWq", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "NC组织编码", "name": "extWqNcCodeWq", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "eNzvQ2ElquHykInvEDp5Y", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqSapCodeWq", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "SAP组织编码", "name": "extWqSapCodeWq", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "IIC4f2EgpAu2WEe3LQ0uV", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqK3CodeWq", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "K3组织编码", "name": "extWqK3CodeWq", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "INXZI2FfcReCjdHLAeDP8", "name": "DetailGroupItem", "props": {"title": "ERP组织编码"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORGANIZATION-frGivJbHWPwB3UfbeD19X", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "serviceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"公司组织配置表\")) : \"新建公司组织配置表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "IoHjbgZafHSfwBkmrPnFq", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "dGNU6p-SxUsj-BTw9SPr0", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": true, "initialValue": 1, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ImFb7j2bwLeacTTtncp_e", "name": "FormField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "initialValue": "INACTIVE", "label": "状态", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "8LGcalno-GfzEL3e10F-9", "valueRules": null}], "name": "status", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-code", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "组织编码", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": false, "required": true}, "key": "-PLoeXA2kE1mB0GmjV9wh", "operator": "SERVICE", "valueRules": {"scope": null, "serviceParams": {"entryNewParams": [{"elements": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"type": "const", "value": "COMMON_2B$ORG_COM_ORG_CF_CODE"}}, {"fieldAlias": "formParams", "fieldName": "formParams", "fieldType": "Object"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ERP_GEN$gen_com_type_cf"}}], "outputParams": [{"fieldAlias": "$result", "fieldName": "取值", "fieldType": "Text", "fixedKey": true, "valueConfig": {"expression": "data", "serviceKey": "TSRM$SYS_InvokeCodeRuleService", "type": "expression"}}]}, "type": "SERVICE", "val": "ERP_GEN$SYS_InvokeCodeRuleService", "value": "ERP_GEN$SYS_InvokeCodeRuleService"}}], "name": "code", "rules": [{"message": "请输入公司组织编码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-name", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "组织名称", "name": "name", "rules": [{"message": "请输入公司组织名称", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "YxOV85O6nwGNX3aAVvrRa", "name": "FormField", "props": {"componentProps": {"fieldAlias": "shortName", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "组织简称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "MsBnY2_bt9H7DeCI9jhzp", "valueRules": null}], "name": "shortName", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "comId", "label": "选择关联公司", "labelField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "parentModelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "公司编码", "name": "code", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "公司名称", "name": "name", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "socialcreditCode", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "统一社会信用代码", "name": "socialcreditCode", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司编码", "name": "code", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司名称", "name": "name", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "mainField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "关联公司", "name": "comId", "rules": [{"message": "请输入关联公司", "required": true}], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "BNXA5QM9xCUN-O_p0wYPq", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqComType", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "公司类型", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "gZUK0x6msjEJQcEABAxlO", "operator": null, "valueRules": null}], "name": "extWqComType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "EUE_Ty-6TxvcNAcWz-fZC", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "oWsOAI0_dMO1cFsMt7LzD", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqNccCode", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "label": "NCC组织编码", "name": "extWqNccCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "x7OrNCmOMhpDfZdpau3Ar", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqNcCodeWq", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "label": "NC组织编码", "name": "extWqNcCodeWq", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "SxRHPAY2eWBz5fa3FyHhV", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqSapCodeWq", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "label": "SAP组织编码", "name": "extWqSapCodeWq", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "wQCvhdcGZXsUV8n41TVJX", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqK3CodeWq", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "label": "K3组织编码", "name": "extWqK3CodeWq", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "JI3Ft7-8rHaw9aMDebfWt", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "ERP组织编码"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "params$": "{ id: route.recordId }", "serviceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "TERP_MIGRATE$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_ORGANIZATION-list_perm_ac_z_0_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_com_org_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form"}, "type": "action"}}], "service": "TERP_MIGRATE$SYS_MasterData_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_ORGANIZATION-list_perm_ac_z_0_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_ORGANIZATION-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "板块管理"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["状态", "请输入版本号", "停用成功", "保存", "请输入ID", "用户名", "ID", "创建人", "请输入公司组织名称", "请输入更新时间", "保存成功!", "启用成功", "逻辑删除标识", "公司组织配置表", "请选择", "版本号", "NCC组织编码", "基本信息", "公司组织配置表详情", "更新时间", "公司类型", "SAP组织编码", "编辑", "选择创建人", "组织简称", "请输入逻辑删除标识", "统一社会信用代码", "确认删除吗？", "组织名称", "复制", "选择更新人", "表格", "系统信息", "新建", "删除成功", "停用", "请输入公司组织编码", "公司名称", "批量删除", "请输入创建时间", "删除", "启用", "批量操作", "公司组织编码", "ERP组织编码", "K3组织编码", "主体信息", "组织编码", "确认启用吗？", "请输入关联公司", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "更新人", "公司组织名称", "请输入", "NC组织编码", "选择关联公司", "取消", "创建时间", "公司编码", "新建公司组织配置表", "确认停用吗？", "关联公司"], "i18nScanPaths": ["COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.editComponentProps.fields.1.componentProps.placeholder", "BNXA5QM9xCUN-O_p0wYPq.props.label", "YxOV85O6nwGNX3aAVvrRa.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-new.props.label", "@exp:COMMON_2B$COMM_2B_ORGANIZATION-editView-page-title.props.title", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-disable.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-comId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-name.props.rules.0.message", "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf.props.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedBy.props.editComponentProps.fields.0.label", "@exp:COMMON_2B$COMM_2B_ORGANIZATION-detailView-page-title.props.title", "EUE_Ty-6TxvcNAcWz-fZC.props.title", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-enable.props.actionConfig.beforeLogicConfig.0.text", "x7OrNCmOMhpDfZdpau3Ar.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-action-cancel.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf.props.filterFields.1.label", "bfD49Q8_0h9WZ1ZpZnhBd.props.componentProps.placeholder", "ck28Edbs3ilux9xcGzLWK.props.label", "IIC4f2EgpAu2WEe3LQ0uV.props.label", "SxRHPAY2eWBz5fa3FyHhV.props.label", "xPLn9LNd_ct05g3dVNXGA.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-disable.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdBy.props.label", "ck28Edbs3ilux9xcGzLWK.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-status.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-code.props.label", "ItNup8z_D_rwtb29pCYRB.props.componentProps.placeholder", "wQCvhdcGZXsUV8n41TVJX.props.label", "ItNup8z_D_rwtb29pCYRB.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.componentProps.placeholder", "2U3S41Np5YBLs9gXwN2In.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch.props.items.0.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-disable.props.label", "ENu3pbqc0V17gQ4ZsIX94.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-code.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-id.props.label", "fGJinRh4G_OzwX_A6DJYV.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-version.props.componentProps.placeholder", "BNXA5QM9xCUN-O_p0wYPq.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-delete.props.label", "eNzvQ2ElquHykInvEDp5Y.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-enable.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-id.props.componentProps.placeholder", "x7OrNCmOMhpDfZdpau3Ar.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-action-save.props.label", "JI3Ft7-8rHaw9aMDebfWt.props.title", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-comId.props.componentProps.label", "fGJinRh4G_OzwX_A6DJYV.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch.props.items.0.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-updatedBy.props.label", "ENu3pbqc0V17gQ4ZsIX94.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-name.props.label", "xPLn9LNd_ct05g3dVNXGA.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-createdAt.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedAt.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-code.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "YxOV85O6nwGNX3aAVvrRa.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf.props.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-comId.props.label", "LRe7V5FDI6Rkd6OA-SOiW.props.title", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-updatedAt.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdAt.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-status.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-code.props.componentProps.placeholder", "SxRHPAY2eWBz5fa3FyHhV.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-deleted.props.rules.0.message", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdAt.props.rules.0.message", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-deleted.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-version.props.label", "2U3S41Np5YBLs9gXwN2In.props.componentProps.placeholder", "IIC4f2EgpAu2WEe3LQ0uV.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-edit.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.rules.0.message", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.editComponentProps.filterFields.1.label", "wQCvhdcGZXsUV8n41TVJX.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-name.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedAt.props.rules.0.message", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "ImFb7j2bwLeacTTtncp_e.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-updatedBy.props.componentProps.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.componentProps.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-version.props.rules.0.message", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs.props.items.1.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.editComponentProps.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-name.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-copy.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf.props.filterFields.0.label", "eNzvQ2ElquHykInvEDp5Y.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-enable.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedBy.props.componentProps.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "KqxnROpVA4benODXPvfxJ.props.text$", "oWsOAI0_dMO1cFsMt7LzD.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-id.props.rules.0.message", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedBy.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-createdBy.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-code.props.rules.0.message", "bfD49Q8_0h9WZ1ZpZnhBd.props.label", "ImFb7j2bwLeacTTtncp_e.props.componentProps.placeholder", "oWsOAI0_dMO1cFsMt7LzD.props.componentProps.placeholder", "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-name.props.label", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedBy.props.componentProps.placeholder", "INXZI2FfcReCjdHLAeDP8.props.title", "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-deleted.props.componentProps.placeholder"]}, "key": "TSRM$COMM_2B_ORGANIZATION-list", "permissionKey": "TSRM$COMM_2B_ORGANIZATION-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-new", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch/items/COMMON_2B$COMM_2B_ORGANIZATION-TERP_MIGRATE$org_com_org_cf-multi-delete", "label": "批量删除", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "TERP_MIGRATE$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "bfD49Q8_0h9WZ1ZpZnhBd", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf", "label": "表格", "type": "Table"}, {"key": "dHT4HclRsDpyRcbQns5Q9", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "2U3S41Np5YBLs9gXwN2In", "label": "公司组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf", "label": "表格", "type": "Table"}, {"key": "dHT4HclRsDpyRcbQns5Q9", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "fGJinRh4G_OzwX_A6DJYV", "label": "公司组织名称", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-list-TERP_MIGRATE$org_com_org_cf", "label": "表格", "type": "Table"}, {"key": "dHT4HclRsDpyRcbQns5Q9", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_MasterData_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-disable", "label": "停用", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_MasterData_DisableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-enable", "label": "启用", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_MasterData_EnableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detail-TERP_MIGRATE$org_com_org_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-version", "label": "版本号", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ImFb7j2bwLeacTTtncp_e", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-frGivJbHWPwB3UfbeD19X", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-frGivJbHWPwB3UfbeD19X", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-frGivJbHWPwB3UfbeD19X", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-frGivJbHWPwB3UfbeD19X", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-code", "label": "组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "EUE_Ty-6TxvcNAcWz-fZC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_InvokeCodeRuleService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-name", "label": "组织名称", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "EUE_Ty-6TxvcNAcWz-fZC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "YxOV85O6nwGNX3aAVvrRa", "label": "组织简称", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "EUE_Ty-6TxvcNAcWz-fZC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-field-comId", "label": "关联公司", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "EUE_Ty-6TxvcNAcWz-fZC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "BNXA5QM9xCUN-O_p0wYPq", "label": "公司类型", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "EUE_Ty-6TxvcNAcWz-fZC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "oWsOAI0_dMO1cFsMt7LzD", "label": "NCC组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "JI3Ft7-8rHaw9aMDebfWt", "label": "ERP组织编码", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "x7OrNCmOMhpDfZdpau3Ar", "label": "NC组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "JI3Ft7-8rHaw9aMDebfWt", "label": "ERP组织编码", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "SxRHPAY2eWBz5fa3FyHhV", "label": "SAP组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "JI3Ft7-8rHaw9aMDebfWt", "label": "ERP组织编码", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "wQCvhdcGZXsUV8n41TVJX", "label": "K3组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-MMLgnU3TqGE7NSNqeFRCg", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-editView-TERP_MIGRATE$org_com_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "JI3Ft7-8rHaw9aMDebfWt", "label": "ERP组织编码", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-code", "label": "组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "LRe7V5FDI6Rkd6OA-SOiW", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-name", "label": "组织名称", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "LRe7V5FDI6Rkd6OA-SOiW", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "xPLn9LNd_ct05g3dVNXGA", "label": "组织简称", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "LRe7V5FDI6Rkd6OA-SOiW", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-comId", "label": "关联公司", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "LRe7V5FDI6Rkd6OA-SOiW", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ck28Edbs3ilux9xcGzLWK", "label": "公司类型", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "LRe7V5FDI6Rkd6OA-SOiW", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-field-status", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "LRe7V5FDI6Rkd6OA-SOiW", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ENu3pbqc0V17gQ4ZsIX94", "label": "NCC组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "INXZI2FfcReCjdHLAeDP8", "label": "ERP组织编码", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ItNup8z_D_rwtb29pCYRB", "label": "NC组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "INXZI2FfcReCjdHLAeDP8", "label": "ERP组织编码", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "eNzvQ2ElquHykInvEDp5Y", "label": "SAP组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "INXZI2FfcReCjdHLAeDP8", "label": "ERP组织编码", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "IIC4f2EgpAu2WEe3LQ0uV", "label": "K3组织编码", "path": [{"key": "COMMON_2B$COMM_2B_ORGANIZATION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_ORGANIZATION-t9TaCDjSqb25txMFzcxP6", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_ORGANIZATION-detailView-TERP_MIGRATE$org_com_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "INXZI2FfcReCjdHLAeDP8", "label": "ERP组织编码", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}