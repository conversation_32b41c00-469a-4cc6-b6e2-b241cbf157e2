{"access": "Private", "key": "TSRM$TSRM_USER_RESET_PASSWORD_ACTION", "name": "用户管理-员工重置密码", "props": {"bean": "TsrmUserAction", "desc": null, "groovyScript": null, "languageType": "Java", "method": "userManageResetPassword", "order": 10, "requestType": "io.terminus.tsrm.md.spi.model.org.dto.OrgEmployeeMdDTO", "responseType": "java.lang.Void", "returnModel": null, "status": "enabled"}, "type": "Action"}