{"access": "Public", "key": "TSRM$ORG_GET_COMP_BY_APPOINT_EMPLOYEE", "name": "ORG_根据指定员工获取公司", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk68fo0516", "name": "开始", "nextNodeKey": "node_1hk68gvv718", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Number", "id": null, "required": null}], "name": null, "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "relation": null, "required": null}], "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk68gvv718", "name": "查询数据", "nextNodeKey": "node_1hk68iumk19", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "fkrR-F5DcicFmznIv7_2r", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": null, "valueKey": "orgEmployeeMdId", "valueName": "orgEmployeeMdId"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "_8UNClr7u1HIz_-zPLObk", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "pN2p8PZ4eGIYQGM91xXVz", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "员工行政组织关联配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"conditionGroup": null, "fieldAlias": "admOrg", "modelAlias": "sys_common$org_struct_md", "subQueryRelatedModels": [{"conditionGroup": null, "fieldAlias": "company", "modelAlias": "ERP_GEN$gen_com_type_cf", "subQueryRelatedModels": []}]}], "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk68iumk19", "name": "赋值", "nextNodeKey": "node_1hk68fo0517", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "valueKey": "data", "valueName": "data"}]}, "id": "1hk68ivq620", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "modelKey": "TB2B$ext_wq_employee_adm_org_cf", "modelName": "员工行政组织关联配置表"}, "valueKey": "NODE_OUTPUT_node_1hk68gvv718", "valueName": "[查询数据]节点.output"}, {"modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_adm_org_cf"}, "valueKey": "admOrg", "valueName": "行政组织"}, {"modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": "TERP_MIGRATE$gen_com_type_cf"}, "valueKey": "company", "valueName": "所属公司"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hk68fo0517", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hk68fo0516"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Number", "id": null, "required": null}], "key": "COMMON_2B$ORG_GET_COMP_BY_APPOINT_EMPLOYEE", "name": "ORG_根据指定员工获取公司", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_com_type_cf", "modelKey": "ERP_GEN$gen_com_type_cf", "modelName": null}, "relation": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "desc": null, "name": null, "permissionKey": "TSRM$ORG_GET_COMP_BY_APPOINT_EMPLOYEE_perm_ac", "teamId": 35, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}