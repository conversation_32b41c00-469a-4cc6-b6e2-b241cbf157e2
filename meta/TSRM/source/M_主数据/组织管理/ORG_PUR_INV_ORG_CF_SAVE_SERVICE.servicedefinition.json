{"access": "Private", "key": "TSRM$ORG_PUR_INV_ORG_CF_SAVE_SERVICE", "name": "ORG_采购组织分配_保存服务", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3bo6nn1", "name": "开始", "nextNodeKey": "node_1hd3bp8se3", "preNodeKey": null, "props": {"desc": null, "globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "checkData", "fieldKey": "checkData", "fieldName": "checkData", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3bp8se4", "name": "新建", "nextNodeKey": "node_1hd3bqd316", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "ijclpwgYzq66ObOVlI2C9", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "l-rtVUrhYg2lbLChScXWf", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "_MSb0YE65EYoVUhr8LKek", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3bqd316", "name": "查询数据", "nextNodeKey": "node_1hd3btc8v8", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "rRh2xRS6hTDYGpbi2EZpa", "key": null, "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": null, "valueKey": "invOrg", "valueName": "需求单位"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_inv_org_cf"}, "valueKey": "invOrg", "valueName": "需求单位"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "N9ablainopNKX_8UBbMjE", "key": null, "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": null, "valueKey": "orgPurOrgCfId", "valueName": "采购组织"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_pur_org_cf"}, "valueKey": "orgPurOrgCfId", "valueName": "采购组织"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "O0dC44chAMUMKu7hjmdZB", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "zxId-Lrx4Guw8rjXNd49m", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}]}, "id": "1hd3bsjol7", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": "采购组织服务组织配置表"}, "valueKey": "NODE_OUTPUT_node_1hd3bqd316", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": "采购组织服务组织配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3btc909", "name": "条件", "nextNodeKey": "node_1hd3c3k3513", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "ZISbKDsEfLiqscsWrV_-R", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "PzlUcj-yl-Z49cQLXjzvJ", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "VMfk8rDZE2w_ioRBUCSqR", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3c3k3513", "name": "新增数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}]}, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": "采购组织服务组织配置表"}, "type": "CascadeCreateDataProperties"}, "renderType": null, "type": "CascadeCreateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3btc9010", "name": "条件", "nextNodeKey": "node_1hd3c25dc12", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "RZmXN2F1LHKbt1lGTF-Nh", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "xLwGgrDOBhEDt_6Vpe4l_", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "p2dbRrdcdYYuGY2kwTA0o", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3c25dc12", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "需求公司、采购组织选择重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hd3btc909", "node_1hd3btc9010"], "id": null, "key": "node_1hd3btc8v8", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3bp8se5", "name": "修改", "nextNodeKey": "node_1hd3c4fma14", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "ZcdDNUapfiVxteENoCyJE", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "2_wpMtRbKrhYDEsnugBAR", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "BCDr1YS2QiFUV0Ms7u-1W", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3c4fma14", "name": "查询数据", "nextNodeKey": "node_1hd3c5ph216", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "VoaNy5811AchD660rzblB", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "NEQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "7fnxkRFgJ9w5zN3DD3yE-", "key": null, "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": null, "valueKey": "invOrg", "valueName": "需求单位"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_inv_org_cf"}, "valueKey": "invOrg", "valueName": "需求单位"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "6QNCK_8iLMsQioG3cyOBG", "key": null, "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": null, "valueKey": "orgPurOrgCfId", "valueName": "采购组织"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "TERP_MIGRATE$org_pur_org_cf"}, "valueKey": "orgPurOrgCfId", "valueName": "采购组织"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "VaJjWWTGy8x_2WfsWTFY8", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "eVu4dpIIjO_OIr77oEj7T", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}]}, "id": "1hd3c5gdl15", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": "采购组织服务组织配置表"}, "valueKey": "NODE_OUTPUT_node_1hd3c4fma14", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": "采购组织服务组织配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3c5ph217", "name": "条件", "nextNodeKey": "node_1hd3c6m5i19", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "Tv5wHhTMlQ46ansTf7aNG", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "JNdPgput67HrQ7pZBi4PL", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "uvf8BtqSNR7qcZ6mbqy53", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3c6m5i19", "name": "更新数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "request", "valueName": "request"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": "采购组织服务组织配置表"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3c5ph218", "name": "条件", "nextNodeKey": "node_1hd3c7a2i20", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "Wv0Ln6GMNgRfKB2GZ7Okd", "key": null, "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "valueKey": "checkData", "valueName": "checkData"}, {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "adPRWY6_BoNdhnikJYea7", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "W8zrdwZQ3-LVkz0LyImJY", "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3c7a2i20", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "需求公司、采购组织选择重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hd3c5ph217", "node_1hd3c5ph218"], "id": null, "key": "node_1hd3c5ph216", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}], "headNodeKeys": ["node_1hd3bp8se4", "node_1hd3bp8se5"], "id": null, "key": "node_1hd3bp8se3", "name": "排他分支", "nextNodeKey": "node_1hd3bo6nn2", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hd3bo6nn2", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hd3bo6nn1"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "modelKey": "TB2B$ext_wq_pur_inv_org_cf", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$ORG_PUR_INV_ORG_CF_SAVE_SERVICE", "name": "ORG_采购组织分配_保存服务", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_PUR_INV_ORG_CF_SAVE_SERVICE_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}