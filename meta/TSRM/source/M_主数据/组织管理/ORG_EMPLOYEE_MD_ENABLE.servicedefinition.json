{"access": "Private", "description": "{}", "key": "TSRM$ORG_EMPLOYEE_MD_ENABLE", "name": "ORG_员工管理_启用", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": "null", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1hb5l7tjm74", "name": "开始", "nextNodeKey": "node_1hb5lq36n83", "props": {"globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "orgEmployee", "fieldKey": "orgEmployee", "fieldName": "orgEmployee", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "relation": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "user", "fieldKey": "user", "fieldName": "user", "fieldType": "Model", "id": null, "modelKey": "TSRM$user", "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "id": null, "required": null}], "output": [], "type": "StartProperties"}, "type": "StartNode"}, {"children": [{"children": null, "desc": null, "id": null, "key": "node_1hb5lq36n84", "name": "条件", "nextNodeKey": "node_1hb5ljggm76", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "wuYu2VGKkgLi04whz1Wsl", "key": "wuYu2VGKkgLi04whz1Wsl", "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"desc": null, "id": null, "key": "node_1hb5ljggm76", "name": "查询数据", "nextNodeKey": "node_1hbctpb9821", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "eOjyIHnshWPMzb7KCNad7", "key": "eOjyIHnshWPMzb7KCNad7", "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "sys_common$org_employee_md", "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "id", "valueName": "id"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": null, "dataType": "MODEL", "desensitized": true, "maximum": null, "outputAssign": {"customAssignments": [{"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "orgEmployee", "valueName": "orgEmployee"}]}, "id": "1hb832leq8", "operator": "EQ", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "valueName": "出参结构体"}]}}, {"field": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "valueKey": "TSRM$user", "valueName": "user"}, {"fieldType": null, "modelAlias": "TSRM$user", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "nickname", "valueName": "昵称"}]}, "id": "1hbct2nbp15", "operator": "EQ", "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "valueName": "出参结构体"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "name", "valueName": "姓名"}]}}, {"field": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "valueKey": "TSRM$user", "valueName": "user"}, {"fieldType": null, "modelAlias": "TSRM$user", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "username", "valueName": "用户名"}]}, "id": "1hbct2nv516", "operator": "EQ", "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "valueName": "出参结构体"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "员工编码"}]}}, {"field": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "valueKey": "TSRM$user", "valueName": "user"}, {"fieldType": null, "modelAlias": "TSRM$user", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "用户邮箱"}]}, "id": "1hbct2ogn17", "operator": "EQ", "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "valueName": "出参结构体"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}}, {"field": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "valueKey": "TSRM$user", "valueName": "user"}, {"fieldType": null, "modelAlias": "TSRM$user", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "用户手机"}]}, "id": "1hbct2p0518", "operator": "EQ", "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "valueName": "出参结构体"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}}, {"field": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "valueKey": "TSRM$user", "valueName": "user"}, {"fieldType": null, "modelAlias": "TSRM$user", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "id": "1hbcu3c0627", "operator": "EQ", "value": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "valueName": "出参结构体"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TERP_MIGRATE$user"}, "valueKey": "userId", "valueName": "用户"}, {"fieldType": null, "modelAlias": "TSRM$user", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "queryModelFields": {"allFields": true, "modelKey": "sys_common$org_employee_md", "queryFields": null}, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrders": null, "stopWhenDataEmpty": true, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"children": [{"children": null, "desc": null, "id": null, "key": "node_1hbctpb9822", "name": "新建用户", "nextNodeKey": "node_1hbctotll20", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "GeCCMp1lGPhE8bR735_hj", "key": "GeCCMp1lGPhE8bR735_hj", "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "valueKey": "TSRM$user", "valueName": "user"}, {"fieldType": null, "modelAlias": "TSRM$user", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"desc": null, "id": null, "key": "node_1hbctotll20", "name": "调用编排服务", "nextNodeKey": "node_1hctl5s171", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "TSRM$user", "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "COMMON_2B$user"}, "relation": null, "required": true}, "id": "1hbctrnpa24", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "valueKey": "TSRM$user", "valueName": "user"}]}}], "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "modelKey": "TSRM$user", "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "COMMON_2B$user"}, "relation": null, "required": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "valueKey": "TSRM$user", "valueName": "user"}]}, "id": "1hbctv81325", "operator": "EQ", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hbctotll20", "valueName": "出参结构体"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "COMMON_2B$user"}, "valueKey": "data", "valueName": "data"}]}}, {"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "orgEmployee", "valueName": "orgEmployee"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TERP_MIGRATE$user"}, "valueKey": "userId", "valueName": "用户"}]}, "id": "1hbcu044d26", "operator": "EQ", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hbctotll20", "valueName": "出参结构体"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "COMMON_2B$user"}, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "serviceKey": "TSRM$ORG_USER_ADD_SERVICE", "serviceName": "ORG_添加用户服务", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"desc": null, "id": null, "key": "node_1hctl5s171", "name": "通知", "props": {"inputMapping": [{"id": null, "key": "password", "required": null, "value": {"constValue": "TERPsrm@2023", "fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}, {"id": null, "key": "password", "required": null, "value": {"constValue": "TERPsrm@2023", "fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "noticeSceneCode": "ERP_GEN$COMM_2B_EMPLOYEE_SENDPASSWORD", "noticeSceneName": "员工管理-启用-默认密码发送", "placeholderMapping": {"NOTICE_TEMPLATE16554810604965888": [{"id": null, "key": "password", "required": null, "value": {"constValue": "TERPsrm@2023", "fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "NOTICE_TEMPLATE16555095968632832": [{"id": null, "key": "password", "required": null, "value": {"constValue": "TERPsrm@2023", "fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}]}, "receiverEmails": [{"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "orgEmployee", "valueName": "orgEmployee"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "email", "valueName": "邮箱"}]}], "receiverMobiles": [{"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "orgEmployee", "valueName": "orgEmployee"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}], "receiverStationLetterIds": null, "receiverUserIds": [], "receiverUsers": [], "sendAfterTransactionCommitted": false, "type": "NoticeProperties"}, "type": "NoticeNode"}, {"children": null, "desc": null, "id": null, "key": "node_1hbctpb9823", "name": "更新用户", "nextNodeKey": "node_1hbd85jkd28", "props": {"conditionGroup": {"conditions": [], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"desc": null, "id": null, "key": "node_1hbd85jkd28", "name": "调用编排服务", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "TSRM$user", "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "COMMON_2B$user"}, "relation": null, "required": true}, "id": "1hbd862og29", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "valueKey": "TSRM$user", "valueName": "user"}]}}], "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "serviceKey": "TSRM$ORG_USER_UPDATE_SERVICE", "serviceName": "ORG_更新用户服务", "transactionPropagation": null, "type": "CallServiceProperties"}, "type": "CallServiceNode"}], "desc": null, "headNodeKeys": ["node_1hbctpb9822", "node_1hbctpb9823"], "id": null, "key": "node_1hbctpb9821", "name": "排他分支", "nextNodeKey": "node_1hb87t8dm13", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"desc": null, "id": null, "key": "node_1hb87t8dm13", "name": "调用编排服务", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "TERP_MIGRATE$org_employee_md"}, "relation": null, "required": true}, "id": "1hb87tut614", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "orgEmployee", "valueName": "orgEmployee"}]}}], "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "TERP_MIGRATE$org_employee_md"}, "relation": null, "required": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "orgEmployee", "valueName": "orgEmployee"}]}, "id": "1hb87uk2415", "operator": "EQ", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hb87t8dm13", "valueName": "出参结构体"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "TERP_MIGRATE$org_employee_md"}, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "serviceKey": "TSRM$ORG_EMPLOYEE_ENABLE_SERVICE", "serviceName": "ORG_员工管理_启用服务", "transactionPropagation": null, "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"children": null, "desc": null, "id": null, "key": "node_1hb5lq36n85", "name": "条件", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "op_q5oG8B8IY3MzqLy605", "key": "op_q5oG8B8IY3MzqLy605", "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "id", "valueName": "id"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}], "desc": null, "headNodeKeys": ["node_1hb5lq36n84", "node_1hb5lq36n85"], "id": null, "key": "node_1hb5lq36n83", "name": "排他分支", "nextNodeKey": "node_1hb5l7tjm75", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"desc": null, "id": null, "key": "node_1hb5l7tjm75", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1hb5l7tjm74"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "id": null, "required": null}], "key": "TSRM$ORG_EMPLOYEE_MD_ENABLE", "name": "ORG_员工管理_启用", "output": [{"defaultValue": null, "description": null, "fieldAlias": "response", "fieldKey": "response", "fieldName": "response", "fieldType": "Model", "id": null, "modelKey": "TSRM$user", "relatedModel": {"modelAlias": "TSRM$user", "modelKey": "TSRM$user", "modelName": "TSRM$user"}, "relation": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "TSRM$ORG_EMPLOYEE_MD_ENABLE_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}