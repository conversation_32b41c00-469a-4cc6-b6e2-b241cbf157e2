{"access": "Private", "key": "TSRM$COMM_2B_COMPANY:list", "name": "list", "props": {"containerSelect": {"COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail": [{"field": "code", "selectFields": null}, {"field": "name", "selectFields": null}, {"field": "socialcreditCode", "selectFields": null}, {"field": "comCorporation", "selectFields": null}, {"field": "bizStatus", "selectFields": null}, {"field": "foundDate", "selectFields": null}, {"field": "openDate", "selectFields": null}, {"field": "closeDate", "selectFields": null}, {"field": "registeredCapital", "selectFields": null}, {"field": "extWqCapitalUnit", "selectFields": null}, {"field": "extWqIsSignature", "selectFields": null}, {"field": "enterpriseType", "selectFields": null}, {"field": "status", "selectFields": null}, {"field": "taxpayersNum", "selectFields": null}, {"field": "taxPayerType", "selectFields": null}, {"field": "addressId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "addrName", "selectFields": null}]}, {"field": "addressDetail", "selectFields": null}, {"field": "phone", "selectFields": null}, {"field": "bankName", "selectFields": null}, {"field": "bankaccount", "selectFields": null}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}], "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form": [{"field": "status", "selectFields": null}, {"field": "code", "selectFields": null}, {"field": "name", "selectFields": null}, {"field": "socialcreditCode", "selectFields": null}, {"field": "comCorporation", "selectFields": null}, {"field": "enterpriseType", "selectFields": null}, {"field": "bizStatus", "selectFields": null}, {"field": "foundDate", "selectFields": null}, {"field": "openDate", "selectFields": null}, {"field": "closeDate", "selectFields": null}, {"field": "registeredCapital", "selectFields": null}, {"field": "extWqCapitalUnit", "selectFields": null}, {"field": "extWqIsSignature", "selectFields": null}, {"field": "taxpayersNum", "selectFields": null}, {"field": "taxPayerType", "selectFields": null}, {"field": "addressId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "addrName", "selectFields": null}]}, {"field": "addressDetail", "selectFields": null}, {"field": "phone", "selectFields": null}, {"field": "bankName", "selectFields": null}, {"field": "bankaccount", "selectFields": null}, {"field": "extWqGraphicSeal", "selectFields": []}], "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf": [{"field": "code", "selectFields": null}, {"field": "name", "selectFields": null}], "jXZzResCcnVMGkdkPKL4B": [{"field": "name", "selectFields": null}, {"field": "taxpayersNum", "selectFields": null}, {"field": "taxPayerType", "selectFields": null}, {"field": "addressId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "addrName", "selectFields": null}]}, {"field": "addressDetail", "selectFields": null}, {"field": "phone", "selectFields": null}, {"field": "bankName", "selectFields": null}, {"field": "bankaccount", "selectFields": null}], "p70buEebtDVO-nWjp8l97": [{"field": "name", "selectFields": null}], "x4uMUoRO5OHNgOHcU8p_V": []}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "JCv3_0UiHwZm-A5XW8DuM", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "PXqVn9ic5gXNF1eT-7uWH", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "公司名称", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "oNW2lAKfqf-dmXC-FWTuu", "operator": null, "valueRules": null}], "name": "name", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "3XgXI13HZ884f-KiZjrOE", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxpayersNum", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "纳税人识别号", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "Omx2XAWu_9da-aoaLojqi", "valueRules": null}], "name": "taxpayersNum", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "_6x4ZXWbLKTiZMk3DXPqz", "name": "FormField", "props": {"componentProps": {"fieldAlias": "taxPayerType", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "纳税人资质", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "u5dGM1I6252sPdEciXnhS", "valueRules": null}], "name": "taxPayerType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "0r77aBSqcbb9oTj9HfwnU", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择注册地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"cascaderField": "addrParentId", "fields": [{"componentProps": {"fieldAlias": "addrName", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "地址库名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "WXNPGwBy9Ef0MXcJ7yb1W", "trigger": "auto", "valueRules": null}], "name": "addrName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "label": "注册地址", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "zzCmmlt7eajQcq4kXbjiR", "valueRules": null}], "name": "addressId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "BPWUa7oIOTqDZa3olvtak", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "注册详细地址", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "NKMDOD6Me42NnXwKnlESL", "valueRules": null}], "name": "addressDetail", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "bX8arRgOMNJJMFiUmSKyo", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "phone", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "手机", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "eq8tD3_1J0yXNWkZnb0JK", "valueRules": null}], "name": "phone", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Ycglvh1FN1VDmPVb7Ouon", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bankName", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "银行名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "_ss5fzcExk06Mxp1orCXM", "valueRules": null}], "name": "bankName", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Iw0uFJrUnDFo9oQqFa6_3", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bankaccount", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "银行账户", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "7EKiFH0lHJ2FuZ2XPuc2N", "valueRules": null}], "name": "bankaccount", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "displayName": "维护开票信息", "key": "jXZzResCcnVMGkdkPKL4B", "name": "FormGroup", "props": {"flow": {"containerKey": "jXZzResCcnVMGkdkPKL4B", "context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ERP_GEN$gen_com_type_cf"}}, {"element": {"elements": [{"fieldAlias": "field", "fieldName": "field", "fieldType": "Text"}, {"element": {"fieldAlias": "element", "fieldName": "element", "fieldType": "Object"}, "fieldAlias": "selectFields", "fieldName": "selectFields", "fieldType": "Array"}], "fieldAlias": "element", "fieldName": "element", "fieldType": "Object"}, "fieldAlias": "selectFields", "fieldName": "selectFields", "fieldType": "Array"}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "params?.id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "Container"}], "key": "bAENfpZbIF9LMJ2YaEAMp", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "XNONsP7xiactlFbdSiuxN", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["RoO2k-iRQwcG51G40JZv7"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_COMPANY-detailView-page"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_0_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "f4FidSReyoSntvXcAkps2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "valueConfig": {"action": {"selector": "", "target": "jXZzResCcnVMGkdkPKL4B"}, "type": "action"}}], "service": "TSRM$ORG_COM_EDIT_INVOICE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["RoO2k-iRQwcG51G40JZv7"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_COMPANY-detailView-page"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_0_1_1", "type": "primary"}, "type": "Widget"}], "key": "B41USNWgWxyGlry4MmCbs", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "RoO2k-iRQwcG51G40JZv7", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"width": 560}, "params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "title": "维护开票信息"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_1_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "COMMON_2B$COMM_2B_COMPANY-TERP_MIGRATE$gen_com_type_cf-multi-delete", "label": "批量删除", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_1_0_0_1_items_0"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "IaTTFhYFO1VZYXEWawTZ4", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "66o564d3oDPaTe60hy5Ol", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "公司编码", "name": "code", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ncMwMd-CsLTQlU7X_rtx-", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "公司名称", "name": "name", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "Bf-7XwYzqTEVTLXaDTVD_", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司编码", "name": "code", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "公司名称", "name": "name", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "公司编码", "name": "code", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "公司名称", "name": "name", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf", "context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_com_type_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showFilterFields": true, "showScope": "all", "tableCondition": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "bXPoEVY1ScMYAJir4sWew", "name": "Status", "props": {"text$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "type$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.type"}}], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{\"公司信息详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "ERP_GEN$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail"]}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_1_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "danger"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_1_1_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindFlowConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_GEN$gen_com_type_cf", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail"}, "type": "action"}}], "service": "TSRM$ORG_GEN_COM_TYPE_CF_DISABLE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_COMPANY-detailView-view"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_1_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "options": [{"label": "草稿态", "value": "DRAFT"}, {"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}, {"label": "已删", "value": "DELETED"}], "scope": "row", "title": "公司状态", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_com_type_cf.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_GEN$gen_com_type_cf", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail"}, "type": "action"}}], "service": "TSRM$ORG_GEN_COM_TYPE_CF_ENABLE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_COMPANY-detailView-view"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_1_1_1_0_1_0_3", "showCondition": {"conditions": [{"conditions": [{"id": "yPsmWDttpgYpFREEjSJCO", "leftValue": {"fieldType": "Enum", "options": [{"label": "草稿态", "value": "DRAFT"}, {"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}, {"label": "已删", "value": "DELETED"}], "scope": "row", "title": "公司状态", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_com_type_cf.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "9E_FYIqtn1Nu3cZ9PFda7", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "Nz3x2kvi5EJ3ji7VKk9_E", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "RoO2k-iRQwcG51G40JZv7", "name": "维护开票信息 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"expression": "route.recordId", "name": "id", "type": "expression"}], "type": "Modal"}}, "buttonType": "default", "confirmOn": "off", "label": "维护开票信息", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_1_1_1_0_1_0_4", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_1_1_1_0_1_0_5", "showCondition": {"conditions": [], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detail-TERP_MIGRATE$gen_com_type_cf-logs", "name": "Logs", "props": {"currentModel": "TSRM$user", "label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-code", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "公司编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-name", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "公司名称", "name": "name", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "socialcreditCode", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "公司代码", "name": "socialcreditCode", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "comCorporation", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "法定代表人", "name": "comCorporation", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bizStatus", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Enum", "editable": false, "label": "经营状态", "name": "bizStatus", "type": "SELECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-foundDate", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "foundDate", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "label": "成立日期", "name": "foundDate", "type": "DATE"}}, {"children": [], "key": "0gDO0l_UHDaNesFWNJUyl", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "openDate", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "initialValue": null, "label": "营业期限开始日期", "name": "openDate", "required": false, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "pTJl5RfqltNIVgiyn0_dT", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "closeDate", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "initialValue": null, "label": "营业期限结束日期", "name": "closeDate", "required": false, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "registeredCapital", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "注册资本", "name": "registeredCapital", "type": "TEXT"}}, {"children": [], "key": "4WfHSuMkdrtgAI4-BWGw1", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqCapitalUnit", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "editable": false, "initialValue": null, "label": "注册资本单位", "name": "extWqCapitalUnit", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "GhpBWXaB6_sMSnGLE9IOy", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqIsSignature", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editable": false, "initialValue": null, "label": "是否开通签章", "name": "extWqIsSignature", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "enterpriseType", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Enum", "editable": false, "label": "企业类型", "name": "enterpriseType", "type": "SELECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-status", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "公司状态", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false}, "key": "DHDjxpQ13T0nSzIn_4OT8", "valueRules": null}], "name": "status", "type": "SELECT"}}], "key": "7ZgDfpg0lJj2tcAOxfSrL", "name": "DetailGroupItem", "props": {"title": "基础信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxpayersNum", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "纳税人识别号", "name": "taxpayersNum", "type": "TEXT"}}, {"children": [], "key": "DcP9_3VZbGYkSOmnTepZZ", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "taxPayerType", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "纳税人资质", "name": "taxPayerType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择注册地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "注册地址", "name": "addressId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "注册详细地址", "name": "addressDetail", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-phone", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "phone", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "手机", "name": "phone", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankName", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bankName", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "银行名称", "name": "bankName", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bankaccount", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "银行账户", "name": "bankaccount", "type": "TEXT"}}], "key": "AGxtUZQ-Nk0BH28OVCfWV", "name": "DetailGroupItem", "props": {"title": "开票信息"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_COMPANY-MAUEuy8F931__E_67QqdI", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}}], "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "displayName": "详情页面", "key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"公司信息\")) : \"新建公司信息\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "FxRGK_JdqBWou5FV2npkQ", "name": "FormField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "initialValue": "INACTIVE", "label": "公司状态", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "zyYflG1LVEWXWTma3aM2x", "valueRules": null}], "name": "status", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-code", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "公司编码", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"key": "ZMFKXoTZPFXYrDs9Z4iJb", "leftValue": {"fieldType": "Number", "options": [], "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "ERP_GEN$gen_com_type_cf.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "key": "liejZnqOGoNJioansogPQ", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "rb-b-vf6n6V47ZmQ2XV9B", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": false, "required": true}, "key": "1JO9dOXcICsT_9E1Ry-cd", "operator": "SERVICE", "valueRules": {"scope": null, "serviceParams": {"entryNewParams": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}, {"name": "request.rule<PERSON>ey", "type": "const", "value": "TERP_MIGRATE$COMM_COMPANY_CODE_RULE"}, {"name": "request.formParams", "value": null}], "outParams": "data", "outType": "Text"}, "title": "(系统)调用取号规则服务", "type": "SERVICE", "val": "ERP_GEN$SYS_InvokeCodeRuleService", "value": "ERP_GEN$SYS_InvokeCodeRuleService"}}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"key": "NZZDhxlU1Jx_ya32YUisS", "leftValue": {"fieldType": "Number", "options": [], "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "ERP_GEN$gen_com_type_cf.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "key": "0wuJ-xSFa0qQr41GFM7L5", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "LJjApNZ2JbVmTtQvnFH_J", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "v_JMlhJ67ljcchtjlSxc6", "operator": null, "valueRules": null}], "name": "code", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "CC2XevVJAVQ8HUqB8uD1Y", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "公司名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "8i2nRx82Py8TKCx7CvAgu", "valueRules": null}], "name": "name", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "socialcreditCode", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "公司代码", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "pn1z9F2kfmIWQGFSWZjed", "operator": null, "valueRules": null}], "name": "socialcreditCode", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "comCorporation", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "法定代表人", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "k99-U608rcUgae-47Q7h6", "operator": null, "valueRules": null}], "name": "comCorporation", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "enterpriseType", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "关联类型", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "Qm1HCcmeQhPywVCojqtTQ", "operator": null, "valueRules": null}], "name": "enterpriseType", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bizStatus", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "经营状态", "name": "bizStatus", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-foundDate", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "foundDate", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "label": "成立日期", "name": "foundDate", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "3tJl9AFTdSndZqNmsfO1D", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "openDate", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "editComponentType": "DatePicker", "label": "营业期限开始日期", "name": "openDate", "required": false, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "iYQyfCBtvE1LycqKALKkn", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "closeDate", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "editComponentType": "DatePicker", "label": "营业期限结束日期", "name": "closeDate", "required": false, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "registeredCapital", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "注册资本", "name": "registeredCapital", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "3qDEcyAAx-YRsKL0-tNkd", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqCapitalUnit", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "注册资本单位", "name": "extWqCapitalUnit", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "NUnppBW--BUWtaDDUESQQ", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqIsSignature", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "editComponentType": "Switch", "label": "是否开通签章", "name": "extWqIsSignature", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}], "key": "uyRQ6-mn-jcTFLv6rKyVd", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"key": "U4ZyXG4fZYP93oIFyHn0k", "leftValue": {"fieldType": "Number", "options": [], "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "ERP_GEN$gen_com_type_cf.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "key": "AYFf_P2suHj4Y9thkEu9M", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "vFkKZLUSnS8wKl28OlQSD", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "NY2oCCyfbAZ-0qC4oN_NU", "operator": null, "valueRules": null}], "showSplit": true, "title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "taxpayersNum", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "纳税人识别号", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": false, "required": true}, "key": "RwH65PXoXKmgZ6QonwAvJ", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "socialcreditCode", "value": "ERP_GEN$gen_com_type_cf.socialcreditCode", "valueType": "model"}}], "name": "taxpayersNum", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "AQBh9JSj1R290-vEHDG9L", "name": "FormField", "props": {"componentProps": {"fieldAlias": "taxPayerType", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "纳税人资质", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "_w3GvHpniuUCgJZ6uuXnJ", "valueRules": null}], "name": "taxPayerType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择注册地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"cascaderField": "addrParentId", "fields": [{"componentProps": {"fieldAlias": "addrName", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "地址库名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "7mriReS0xtnQHlqaS_yi4", "trigger": "auto", "valueRules": null}], "name": "addrName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "outputParams": {"expression": "data.data", "serviceKey": "TSRM$SYS_PagingDataService", "type": "expression"}, "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_addr_type_cf"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "addrName", "leafOnly": false, "modelAlias": "ERP_GEN$gen_addr_type_cf", "reverseConstructFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}, "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "行政区划", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "bIvm_rPzxtScObdfuZHj5", "valueRules": null}], "name": "addressId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "注册地址", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "3GQpL--3ub718A0cJHYoL", "valueRules": null}], "name": "addressDetail", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-phone", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "phone", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "电话号码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "b6THr9A71M0tc1Gs31CMA", "valueRules": null}], "name": "phone", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bankName", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bankName", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "开户银行", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "W_n49V9cSpiU9fKwFF63O", "valueRules": null}], "name": "bankName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "bankaccount", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "银行账户", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "yAb92Oz7VQRatGEw77_0a", "valueRules": null}], "name": "bankaccount", "rules": [], "type": "TEXT"}, "type": "Widget"}], "key": "_F8hRnGD7Vazek82TCbYF", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "开票信息"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "CHxj__vfjM-vEj3Y0uRwt", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "SMco0AsXJvbdJzzLcsk-i", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_gen_com_graphic_seal_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "_XimRAJX7Js-BED8KYlfg", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "graphicSealType", "modelAlias": "TB2B$ext_wq_gen_com_graphic_seal_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "图形章类型", "name": "graphicSealType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "rG6U5ltJgVoAaotulf383", "name": "Field", "props": {"componentProps": {"fieldAlias": "graphicSeal", "modelAlias": "TB2B$ext_wq_gen_com_graphic_seal_cf", "placeholder": "请上传"}, "hidden": false, "initialValue": null, "label": "图形章", "name": "graphicSeal", "required": false, "type": "ATTACHMENT", "width": 120}, "type": "Widget"}], "key": "noQ1DxXdlRqKo90gELIAF", "name": "Fields", "props": {}, "type": "Meta"}], "key": "cXpNQe6SRCFsFHnIMxTGn", "name": "TableForm", "props": {"fieldName": "extWqGraphicSeal", "fields": [], "label": "表格表单", "modelAlias": "TB2B$ext_wq_gen_com_graphic_seal_cf"}, "type": "Widget"}], "key": "lQroPDK0_ak2MG6RxzWVq", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "tPjnIPJwN9GsEOHgzqG0n", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"key": "itb9HZ7c4c9vQSthcfEkw", "leftValue": {"fieldType": "Boolean", "options": [], "scope": "form", "title": "是否开通签章", "type": "VarValue", "val": "extWqIsSignature", "value": "ERP_GEN$gen_com_type_cf.extWqIsSignature", "valueType": "VAR", "varVal": "extWqIsSignature", "varValue": [{"valueKey": "extWqIsSignature", "valueName": "extWqIsSignature"}]}, "operator": "EQ", "rightValue": {"constValue": "false", "fieldType": "Boolean", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "key": "_NIFOfh2QjocQ2jlJtxDN", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "dUMz_6bkDDEakDpf4CHUb", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "qMa5b6opB5iBUL5tej55U", "operator": null, "valueRules": null}], "showSplit": true, "title": "图形章信息"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "modelAlias": "ERP_GEN$gen_com_type_cf", "params$": "{ id: route.recordId }", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "modelAlias": "ERP_GEN$gen_com_type_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "ERP_GEN$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_1_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "data", "fieldName": "data", "fieldType": "Model", "modelAlias": "ERP_GEN$gen_com_type_cf", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form"}, "type": "action"}}], "service": "TSRM$ORG_COM_SAVE_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "list"}, {"action": "Message", "message": "保存成功"}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_COMPANY-page"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac_z_1_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_COMPANY-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "5b6C_waHCI-4Euqzb3Vtf", "name": "FormField", "props": {"label": "表单字段", "name": ""}, "type": "Widget"}, {"children": [], "key": "h7imuzmfcmneYgOMnA_RS", "name": "FormField", "props": {"label": "表单字段", "name": ""}, "type": "Widget"}], "key": "x4uMUoRO5OHNgOHcU8p_V", "name": "FormGroup", "props": {"flow": {"containerKey": "x4uMUoRO5OHNgOHcU8p_V", "context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_com_type_cf"}}], "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "Container"}], "key": "qlqpEGyYFVaXOuHvuJeZF", "name": "Box", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "JMVHD9adgra2FMAd4fl3d", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "公司名称", "name": "name", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "p70buEebtDVO-nWjp8l97", "name": "FormGroup", "props": {"flow": {"containerKey": "p70buEebtDVO-nWjp8l97", "context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "params": [], "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}, {"key": "billing", "label": "维护开票信息"}], "key": "list"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_COMPANY-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_COMPANY-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COMPANY-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "公司管理"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["关联类型", "注册地址", "停用成功", "保存", "注册资本单位", "请输入ID", "开票信息", "请上传", "ID", "创建人", "银行账户", "启用成功", "纳税人资质", "逻辑删除标识", "请选择", "基本信息", "表单字段", "更新时间", "营业期限开始日期", "行政区划", "编辑", "图形章类型", "选择创建人", "成立日期", "请输入逻辑删除标识", "确认删除吗？", "营业期限结束日期", "注册详细地址", "公司信息详情", "是否开通签章", "开户银行", "维护开票信息", "复制", "选择更新人", "公司代码", "手机", "表格", "系统信息", "公司名称", "新建", "删除成功", "停用", "注册资本", "批量删除", "删除", "启用", "批量操作", "银行名称", "表格表单", "纳税人识别号", "保存成功", "公司状态", "经营状态", "主体信息", "确认启用吗？", "电话号码", "图形章", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "法定代表人", "更新人", "选择注册地址", "公司信息", "请输入", "企业类型", "取消", "公司编码", "图形章信息", "创建时间", "新建公司信息", "地址库名称", "确认停用吗？", "基础信息"], "i18nScanPaths": ["@exp:COMMON_2B$COMM_2B_COMPANY-detailView-page-title.props.title", "COMMON_2B$COMM_2B_COMPANY-editView-action-save.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "pTJl5RfqltNIVgiyn0_dT.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf.props.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital.props.componentProps.placeholder", "pTJl5RfqltNIVgiyn0_dT.props.label", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-batch.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-disable.props.actionConfig.beforeLogicConfig.0.text", "IaTTFhYFO1VZYXEWawTZ4.props.label", "0r77aBSqcbb9oTj9HfwnU.props.componentProps.label", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-batch.props.items.0.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-foundDate.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-deleted.props.rules.0.message", "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "h7imuzmfcmneYgOMnA_RS.props.label", "FxRGK_JdqBWou5FV2npkQ.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode.props.componentProps.placeholder", "BPWUa7oIOTqDZa3olvtak.props.componentProps.placeholder", "Nz3x2kvi5EJ3ji7VKk9_E.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-status.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bankName.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-foundDate.props.componentProps.placeholder", "Ycglvh1FN1VDmPVb7Ouon.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-foundDate.props.label", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.componentProps.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode.props.label", "0gDO0l_UHDaNesFWNJUyl.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-id.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-code.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-phone.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf.props.filterFields.0.componentProps.placeholder", "IaTTFhYFO1VZYXEWawTZ4.props.componentProps.placeholder", "0r77aBSqcbb9oTj9HfwnU.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-edit.props.label", "66o564d3oDPaTe60hy5Ol.props.label", "4WfHSuMkdrtgAI4-BWGw1.props.componentProps.placeholder", "0r77aBSqcbb9oTj9HfwnU.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital.props.label", "SMco0AsXJvbdJzzLcsk-i.props.label", "JMVHD9adgra2FMAd4fl3d.props.componentProps.placeholder", "bX8arRgOMNJJMFiUmSKyo.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-enable.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-copy.props.label", "GhpBWXaB6_sMSnGLE9IOy.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount.props.label", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf.props.filterFields.1.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-batch.props.items.0.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-enable.props.actionConfig.endLogicOtherConfig.1.message", "_6x4ZXWbLKTiZMk3DXPqz.props.label", "_F8hRnGD7Vazek82TCbYF.props.title", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-delete.props.label", "0gDO0l_UHDaNesFWNJUyl.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankName.props.label", "ncMwMd-CsLTQlU7X_rtx-.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-disable.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.componentProps.placeholder", "66o564d3oDPaTe60hy5Ol.props.componentProps.placeholder", "Ycglvh1FN1VDmPVb7Ouon.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum.props.label", "uyRQ6-mn-jcTFLv6rKyVd.props.title", "@exp:COMMON_2B$COMM_2B_COMPANY-editView-page-title.props.title", "bX8arRgOMNJJMFiUmSKyo.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankName.props.componentProps.placeholder", "NUnppBW--BUWtaDDUESQQ.props.label", "0r77aBSqcbb9oTj9HfwnU.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-new.props.label", "DcP9_3VZbGYkSOmnTepZZ.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-createdBy.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-id.props.rules.0.message", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode.props.componentProps.placeholder", "AQBh9JSj1R290-vEHDG9L.props.label", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf.props.fields.0.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-foundDate.props.label", "f4FidSReyoSntvXcAkps2.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-phone.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-status.props.label", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf.props.filterFields.0.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-phone.props.label", "JCv3_0UiHwZm-A5XW8DuM.props.componentProps.placeholder", "iYQyfCBtvE1LycqKALKkn.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount.props.componentProps.placeholder", "Iw0uFJrUnDFo9oQqFa6_3.props.label", "DcP9_3VZbGYkSOmnTepZZ.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf.props.fields.1.componentProps.placeholder", "0r77aBSqcbb9oTj9HfwnU.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-phone.props.label", "iYQyfCBtvE1LycqKALKkn.props.componentProps.placeholder", "CC2XevVJAVQ8HUqB8uD1Y.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation.props.label", "_XimRAJX7Js-BED8KYlfg.props.componentProps.placeholder", "PXqVn9ic5gXNF1eT-7uWH.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-updatedAt.props.label", "PXqVn9ic5gXNF1eT-7uWH.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-disable.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-name.props.componentProps.placeholder", "3qDEcyAAx-YRsKL0-tNkd.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-code.props.componentProps.placeholder", "ncMwMd-CsLTQlU7X_rtx-.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType.props.componentProps.placeholder", "CC2XevVJAVQ8HUqB8uD1Y.props.componentProps.placeholder", "NUnppBW--BUWtaDDUESQQ.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail.props.componentProps.placeholder", "XNONsP7xiactlFbdSiuxN.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-deleted.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation.props.componentProps.placeholder", "bXPoEVY1ScMYAJir4sWew.props.text$", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital.props.componentProps.placeholder", "4WfHSuMkdrtgAI4-BWGw1.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.label", "BPWUa7oIOTqDZa3olvtak.props.label", "3tJl9AFTdSndZqNmsfO1D.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "AQBh9JSj1R290-vEHDG9L.props.componentProps.placeholder", "5b6C_waHCI-4Euqzb3Vtf.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-action-save.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-code.props.label", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf.props.filterFields.1.componentProps.placeholder", "JMVHD9adgra2FMAd4fl3d.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus.props.componentProps.placeholder", "3tJl9AFTdSndZqNmsfO1D.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bankName.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount.props.label", "JCv3_0UiHwZm-A5XW8DuM.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs.props.items.1.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-id.props.componentProps.placeholder", "AGxtUZQ-Nk0BH28OVCfWV.props.title", "rG6U5ltJgVoAaotulf383.props.label", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf.props.label", "Iw0uFJrUnDFo9oQqFa6_3.props.componentProps.placeholder", "3XgXI13HZ884f-KiZjrOE.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-updatedBy.props.label", "3qDEcyAAx-YRsKL0-tNkd.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.componentProps.label", "_XimRAJX7Js-BED8KYlfg.props.label", "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf.props.fields.1.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-name.props.label", "3XgXI13HZ884f-KiZjrOE.props.componentProps.placeholder", "SMco0AsXJvbdJzzLcsk-i.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType.props.label", "RoO2k-iRQwcG51G40JZv7.props.title", "_6x4ZXWbLKTiZMk3DXPqz.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-deleted.props.label", "FxRGK_JdqBWou5FV2npkQ.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-code.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-createdAt.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-action-cancel.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital.props.label", "tPjnIPJwN9GsEOHgzqG0n.props.title", "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-updatedBy.props.componentProps.label", "GhpBWXaB6_sMSnGLE9IOy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus.props.label", "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId.props.editComponentProps.fields.0.componentProps.placeholder", "7ZgDfpg0lJj2tcAOxfSrL.props.title", "cXpNQe6SRCFsFHnIMxTGn.props.label", "COMMON_2B$COMM_2B_COMPANY-detailView-actions-enable.props.label", "rG6U5ltJgVoAaotulf383.props.componentProps.placeholder"]}, "key": "TSRM$COMM_2B_COMPANY-list", "permissionKey": "TSRM$COMM_2B_COMPANY-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "jXZzResCcnVMGkdkPKL4B", "label": "维护开票信息", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "bAENfpZbIF9LMJ2YaEAMp", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "XNONsP7xiactlFbdSiuxN", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "B41USNWgWxyGlry4MmCbs", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "f4FidSReyoSntvXcAkps2", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "B41USNWgWxyGlry4MmCbs", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "TSRM$ORG_COM_EDIT_INVOICE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "p70buEebtDVO-nWjp8l97", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "JCv3_0UiHwZm-A5XW8DuM", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "bAENfpZbIF9LMJ2YaEAMp", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "jXZzResCcnVMGkdkPKL4B", "label": "维护开票信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "PXqVn9ic5gXNF1eT-7uWH", "label": "公司名称", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "bAENfpZbIF9LMJ2YaEAMp", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "jXZzResCcnVMGkdkPKL4B", "label": "维护开票信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "3XgXI13HZ884f-KiZjrOE", "label": "纳税人识别号", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "bAENfpZbIF9LMJ2YaEAMp", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "jXZzResCcnVMGkdkPKL4B", "label": "维护开票信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "_6x4ZXWbLKTiZMk3DXPqz", "label": "纳税人资质", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "bAENfpZbIF9LMJ2YaEAMp", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "jXZzResCcnVMGkdkPKL4B", "label": "维护开票信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "0r77aBSqcbb9oTj9HfwnU", "label": "注册地址", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "bAENfpZbIF9LMJ2YaEAMp", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "jXZzResCcnVMGkdkPKL4B", "label": "维护开票信息", "type": "FormGroup"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "BPWUa7oIOTqDZa3olvtak", "label": "注册详细地址", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "bAENfpZbIF9LMJ2YaEAMp", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "jXZzResCcnVMGkdkPKL4B", "label": "维护开票信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "bX8arRgOMNJJMFiUmSKyo", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "bAENfpZbIF9LMJ2YaEAMp", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "jXZzResCcnVMGkdkPKL4B", "label": "维护开票信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "Ycglvh1FN1VDmPVb7Ouon", "label": "银行名称", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "bAENfpZbIF9LMJ2YaEAMp", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "jXZzResCcnVMGkdkPKL4B", "label": "维护开票信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "Iw0uFJrUnDFo9oQqFa6_3", "label": "银行账户", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "RoO2k-iRQwcG51G40JZv7", "label": "维护开票信息", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "bAENfpZbIF9LMJ2YaEAMp", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "jXZzResCcnVMGkdkPKL4B", "label": "维护开票信息", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-new", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-batch/items/COMMON_2B$COMM_2B_COMPANY-TERP_MIGRATE$gen_com_type_cf-multi-delete", "label": "批量删除", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "ERP_GEN$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "IaTTFhYFO1VZYXEWawTZ4", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf", "label": "表格", "type": "Table"}, {"key": "Bf-7XwYzqTEVTLXaDTVD_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "66o564d3oDPaTe60hy5Ol", "label": "公司编码", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf", "label": "表格", "type": "Table"}, {"key": "Bf-7XwYzqTEVTLXaDTVD_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ncMwMd-CsLTQlU7X_rtx-", "label": "公司名称", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-list-TERP_MIGRATE$gen_com_type_cf", "label": "表格", "type": "Table"}, {"key": "Bf-7XwYzqTEVTLXaDTVD_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "x4uMUoRO5OHNgOHcU8p_V", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "qlqpEGyYFVaXOuHvuJeZF", "label": "区块", "type": "Box"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "JMVHD9adgra2FMAd4fl3d", "label": "公司名称", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "p70buEebtDVO-nWjp8l97", "label": "表单组", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "5b6C_waHCI-4Euqzb3Vtf", "label": "表单字段", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "qlqpEGyYFVaXOuHvuJeZF", "label": "区块", "type": "Box"}, {"key": "x4uMUoRO5OHNgOHcU8p_V", "label": "表单组", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "h7imuzmfcmneYgOMnA_RS", "label": "表单字段", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "qlqpEGyYFVaXOuHvuJeZF", "label": "区块", "type": "Box"}, {"key": "x4uMUoRO5OHNgOHcU8p_V", "label": "表单组", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$ORG_COM_SAVE_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions-disable", "label": "停用", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$ORG_GEN_COM_TYPE_CF_DISABLE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions-enable", "label": "启用", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$ORG_GEN_COM_TYPE_CF_ENABLE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "Nz3x2kvi5EJ3ji7VKk9_E", "label": "维护开票信息", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detail-TERP_MIGRATE$gen_com_type_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-MAUEuy8F931__E_67QqdI", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-MAUEuy8F931__E_67QqdI", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-MAUEuy8F931__E_67QqdI", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-MAUEuy8F931__E_67QqdI", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "FxRGK_JdqBWou5FV2npkQ", "label": "公司状态", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-code", "label": "公司编码", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_InvokeCodeRuleService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "CC2XevVJAVQ8HUqB8uD1Y", "label": "公司名称", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode", "label": "公司代码", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation", "label": "法定代表人", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType", "label": "关联类型", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus", "label": "经营状态", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-foundDate", "label": "成立日期", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "3tJl9AFTdSndZqNmsfO1D", "label": "营业期限开始日期", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "iYQyfCBtvE1LycqKALKkn", "label": "营业期限结束日期", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital", "label": "注册资本", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "3qDEcyAAx-YRsKL0-tNkd", "label": "注册资本单位", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "NUnppBW--BUWtaDDUESQQ", "label": "是否开通签章", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "uyRQ6-mn-jcTFLv6rKyVd", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum", "label": "纳税人识别号", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "AQBh9JSj1R290-vEHDG9L", "label": "纳税人资质", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressId", "label": "行政区划", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_ReverseConstructTreeService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail", "label": "注册地址", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-phone", "label": "电话号码", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bankName", "label": "开户银行", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount", "label": "银行账户", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "_F8hRnGD7Vazek82TCbYF", "label": "开票信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-code", "label": "公司编码", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-name", "label": "公司名称", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-socialcreditCode", "label": "公司代码", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-comCorporation", "label": "法定代表人", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bizStatus", "label": "经营状态", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-foundDate", "label": "成立日期", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "0gDO0l_UHDaNesFWNJUyl", "label": "营业期限开始日期", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "pTJl5RfqltNIVgiyn0_dT", "label": "营业期限结束日期", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-registeredCapital", "label": "注册资本", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "4WfHSuMkdrtgAI4-BWGw1", "label": "注册资本单位", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "GhpBWXaB6_sMSnGLE9IOy", "label": "是否开通签章", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-enterpriseType", "label": "企业类型", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-status", "label": "公司状态", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "7ZgDfpg0lJj2tcAOxfSrL", "label": "基础信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-taxpayersNum", "label": "纳税人识别号", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "DcP9_3VZbGYkSOmnTepZZ", "label": "纳税人资质", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressId", "label": "注册地址", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-addressDetail", "label": "注册详细地址", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-phone", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankName", "label": "银行名称", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-field-bankaccount", "label": "银行账户", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-page", "label": "详情页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-iPLalsGMQdairmjXsLO9Z", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-detailView-TERP_MIGRATE$gen_com_type_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "AGxtUZQ-Nk0BH28OVCfWV", "label": "开票信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "cXpNQe6SRCFsFHnIMxTGn", "label": "表格表单", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "tPjnIPJwN9GsEOHgzqG0n", "label": "图形章信息", "type": "FormGroupItem"}, {"key": "lQroPDK0_ak2MG6RxzWVq", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "SMco0AsXJvbdJzzLcsk-i", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "tPjnIPJwN9GsEOHgzqG0n", "label": "图形章信息", "type": "FormGroupItem"}, {"key": "lQroPDK0_ak2MG6RxzWVq", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "cXpNQe6SRCFsFHnIMxTGn", "label": "表格表单", "type": "TableForm"}, {"key": "noQ1DxXdlRqKo90gELIAF", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "_XimRAJX7Js-BED8KYlfg", "label": "图形章类型", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "tPjnIPJwN9GsEOHgzqG0n", "label": "图形章信息", "type": "FormGroupItem"}, {"key": "lQroPDK0_ak2MG6RxzWVq", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "cXpNQe6SRCFsFHnIMxTGn", "label": "表格表单", "type": "TableForm"}, {"key": "noQ1DxXdlRqKo90gELIAF", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "rG6U5ltJgVoAaotulf383", "label": "图形章", "path": [{"key": "COMMON_2B$COMM_2B_COMPANY-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COMPANY-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COMPANY-2gXz3tlBEJ7NrVTHTMTnP", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COMPANY-editView-TERP_MIGRATE$gen_com_type_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "tPjnIPJwN9GsEOHgzqG0n", "label": "图形章信息", "type": "FormGroupItem"}, {"key": "lQroPDK0_ak2MG6RxzWVq", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "cXpNQe6SRCFsFHnIMxTGn", "label": "表格表单", "type": "TableForm"}, {"key": "noQ1DxXdlRqKo90gELIAF", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}