{"access": "Private", "key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION:list", "name": "list", "props": {"containerSelect": {"COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail": [], "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form": [], "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf": []}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "permissionKey": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list_perm_ac_z_0_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_pur_inv_org_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "TB2B$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-TERP_MIGRATE$ext_wq_pur_inv_org_cf-multi-delete", "label": "批量删除", "permissionKey": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list_perm_ac_z_0_0_0_1_items_0"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-import", "name": "ImportButton", "props": {"addApprovalManageServiceProps": {"predictFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/gei/task/config/predict"}}, "deleteApprovalManageServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "downloadFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}, "downloadServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}}, "editApprovalManageServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "label": "导入", "permissionKey": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list_perm_ac_z_0_0_0_2", "predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}, "predictServiceProps": {"predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}}, "saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}, "saveSubServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}}, "showCondition": {"conditions": [{"conditions": [{"id": "F6K7zlKvbD13xRFsNiG3r", "leftValue": {"fieldType": "Number", "options": [], "scope": "row", "title": "ID", "type": "VarValue", "val": "id", "value": "TB2B$ext_wq_pur_inv_org_cf.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"constValue": "0", "fieldType": "Number", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "7x4N70Z4n9zkeEDDuF1Uo", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "juEMesRy25MBFf9aua5Ap", "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"getUserInfoFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/portal/user/current"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "label": "导出", "permissionKey": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list_perm_ac_z_0_0_0_3", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "IK2E9AhCg_i6GBfNg4vh8", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "gqP-RqBG6MVuwZRTQMYT0", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "需求公司编码", "name": "invOrg.code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "tNSjV339adsAsbEljGoei", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "需求公司名称", "modelAlias": "sys_common$org_struct_md", "name": "invOrg.name", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "9mTXB_FpvUVv6mXYHZrDf", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentProps": {"fields": [], "filterFields": []}, "editComponentType": "InputText", "label": "采购组织编码", "name": "orgPurOrgCfId.code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "31T-gCNChd56y2mwABsh5", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentProps": {"fields": [], "filterFields": []}, "editComponentType": "InputText", "label": "采购组织名称", "name": "orgPurOrgCfId.name", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "d17rATmKOqWiXnKofE11h", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "invOrg", "label": "选择需求单位", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "a_9LoGIXqTrF5M0FHNj-3", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "需求单位", "name": "invOrg", "type": "OBJECT", "width": 168}], "flow": {"containerKey": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "context$": "$context", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_pur_inv_org_cf"}}], "serviceKey": "TB2B$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showScope": "all", "tableCondition": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"采购组织服务组织配置表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_pur_inv_org_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TB2B$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list_perm_ac_z_0_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list_perm_ac_z_0_1_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list_perm_ac_z_0_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detail-TERP_MIGRATE$ext_wq_pur_inv_org_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "需求公司编码", "name": "invOrg.code", "required": true, "type": "TEXT", "width": 120}}, {"children": [], "key": "Zz6bS1sIbx-CBc2Bdr8Ha", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "需求公司名称", "name": "invOrg.name", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "T4oVwaiz9yj5qy8NHjOZn", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "采购组织编码", "name": "orgPurOrgCfId.code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "YsEJMYXhnaZufgQKxp-6I", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "采购组织名称", "name": "orgPurOrgCfId.name", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-UzwZBpl9rjjjDo0jI5EWU", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-dzAI_tdk0wvwqurnntolJ", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "context$": "$context", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "serviceKey": "TB2B$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TB2B$ext_wq_pur_inv_org_cf"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"采购组织服务组织配置表\")) : \"新建采购组织服务组织配置表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "LS9xpt7QG8KK5bFOWoadM", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "nickname", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "昵称", "name": "nickname", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "username", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "用户名", "name": "username", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "用户邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "用户手机", "name": "mobile", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "nickname", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "昵称", "name": "nickname", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "username", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "用户名", "name": "username", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "用户邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "用户手机", "name": "mobile", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "username", "mainField": "username", "modelAlias": "TSRM$user"}, "editComponentType": "RelationSelect", "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": true, "initialValue": 1, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg", "name": "FormField", "props": {"componentProps": {"fieldAlias": "invOrg", "label": "选择需求单位", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId", "extWqNccCode", "extWqSapCodeWq", "extWqNcCodeWq", "extWqK3CodeWq", "extWqComType"], "fieldAlias": "comOrgId", "label": "选择所属公司组织", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "所属公司组织", "name": "comOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["languageCode", "languageName"], "fieldAlias": "languageId", "label": "选择语言", "labelField": "languageName", "modelAlias": "ERP_GEN$gen_language_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "语言", "name": "languageId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "area", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "区号", "name": "area", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "tele", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "电话/分机号", "name": "tele", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "fax", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "传真/分机号", "name": "fax", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "mail", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "邮箱", "name": "mail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "phone", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "手机", "name": "phone", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["addrCode", "addrName", "postCode", "counId", "<PERSON><PERSON><PERSON><PERSON>", "addrParentId"], "fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "地址", "name": "addressId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name"], "fieldAlias": "orgSlsDcId", "label": "选择销售渠道", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_sls_dc_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "销售渠道", "name": "orgSlsDcId", "required": false, "type": "OBJECT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "code", "mainField": "code", "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "需求公司编码", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "kY4XF8WRd6-vpDlGe9-9g", "operator": null, "valueRules": null}], "name": "invOrg", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "5setP67n1iTU4_Ch0gAu4", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentProps": {"fields": [], "filterFields": []}, "editComponentType": "InputText", "label": "需求公司名称", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "og8FO7SStczM4C1tEFHSo", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "invOrg.name", "value": "TB2B$ext_wq_pur_inv_org_cf.invOrg.name", "valueType": "model"}}], "name": "invOrg.name", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "wC86TF8Wkifw5kOVop9UO", "name": "FormField", "props": {"componentProps": {"fieldAlias": "orgPurOrgCfId", "label": "选择采购组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_pur_inv_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId", "extWqNccCode", "extWqSapCodeWq", "extWqNcCodeWq", "extWqK3CodeWq", "extWqComType"], "fieldAlias": "comOrgCode", "label": "选择所属公司组织", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "所属公司组织", "name": "comOrgCode", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["languageCode", "languageName"], "fieldAlias": "languageId", "label": "选择语言", "labelField": "languageName", "modelAlias": "ERP_GEN$gen_language_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "语言", "name": "languageId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "area", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "区号", "name": "area", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "tele", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "电话/分机号", "name": "tele", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "fax", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "传真/分机号", "name": "fax", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "mail", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "邮箱", "name": "mail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "phone", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "手机", "name": "phone", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["addrCode", "addrName", "postCode", "counId", "<PERSON><PERSON><PERSON><PERSON>", "addrParentId"], "fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "地址", "name": "addressId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "街道", "name": "addressDetail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["timezoneCode", "timezoneDesc", "counId", "timezoneDistrict", "timezoneFormat", "isSummerTime", "summerTimeStart", "summerTimeEnd", "status"], "fieldAlias": "timezoneId", "label": "选择时区", "labelField": "timezoneDesc", "modelAlias": "ERP_GEN$gen_timezone_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "时区", "name": "timezoneId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "postcode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "邮编", "name": "postcode", "required": true, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "code", "mainField": "code", "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "label": "采购组织编码", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "EpebEeuy_Lea0zhyAWgq7", "operator": null, "valueRules": null}], "name": "orgPurOrgCfId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TApYuHlTDE1yZYh0-VUas", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentProps": {"fields": [], "filterFields": []}, "editComponentType": "InputText", "label": "采购组织名称", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "hiFnjahzG0e71UFBIOzqT", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "orgPurOrgCfId.name", "value": "TB2B$ext_wq_pur_inv_org_cf.orgPurOrgCfId.name", "valueType": "model"}}], "name": "orgPurOrgCfId.name", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "params$": "{ id: route.recordId }", "serviceKey": "TB2B$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "TB2B$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf"}, "type": "Container"}, {"children": [], "key": "UNWrz5STfJpAc97bL4G9F", "name": "FormGroup", "props": {}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list_perm_ac_z_0_1_2_4_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "TB2B$ext_wq_pur_inv_org_cf", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form"}, "type": "action"}}], "service": "TSRM$ORG_PUR_INV_ORG_CF_SAVE_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page"}, {"action": "Message", "message": "保存成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list_perm_ac_z_0_1_2_4_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "采购组织分配"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["请输入版本号", "电话/分机号", "时区", "采购组织编码", "保存", "请输入ID", "选择需求单位", "用户名", "地址", "ID", "创建人", "请输入更新时间", "需求单位", "传真/分机号", "用户手机", "语言", "逻辑删除标识", "选择采购组织", "请选择", "版本号", "更新时间", "新建采购组织服务组织配置表", "编辑", "采购组织名称", "选择创建人", "邮编", "请输入逻辑删除标识", "确认删除吗？", "所属公司组织", "组织名称", "复制", "选择更新人", "手机", "表格", "系统信息", "新建", "删除成功", "需求公司名称", "批量删除", "请输入创建时间", "选择语言", "街道", "删除", "邮箱", "批量操作", "用户邮箱", "昵称", "需求公司编码", "保存成功", "采购组织服务组织配置表详情", "选择所属公司组织", "主体信息", "组织编码", "选择地址", "销售渠道", "更新人", "选择销售渠道", "请输入", "区号", "取消", "创建时间", "选择时区", "采购组织服务组织配置表"], "i18nScanPaths": ["COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-deleted.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.7.label", "YsEJMYXhnaZufgQKxp-6I.props.label", "31T-gCNChd56y2mwABsh5.props.componentProps.placeholder", "TApYuHlTDE1yZYh0-VUas.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.2.componentProps.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf.props.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-id.props.rules.0.message", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch.props.items.0.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.2.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.10.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.9.componentProps.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.filterFields.1.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.3.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-deleted.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.10.componentProps.label", "IK2E9AhCg_i6GBfNg4vh8.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-copy.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-version.props.label", "5setP67n1iTU4_Ch0gAu4.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.filterFields.3.componentProps.placeholder", "T4oVwaiz9yj5qy8NHjOZn.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.filterFields.1.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-delete.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.0.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.4.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-updatedBy.props.label", "@exp:COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page-title.props.title", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.4.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.1.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.filterFields.0.label", "gqP-RqBG6MVuwZRTQMYT0.props.componentProps.placeholder", "@exp:COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-page-title.props.title", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.10.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "tNSjV339adsAsbEljGoei.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.6.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch.props.items.0.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-new.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-action-save.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdAt.props.rules.0.message", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.6.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.7.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.2.componentProps.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-updatedBy.props.componentProps.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.3.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.8.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.10.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.5.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-version.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-deleted.props.rules.0.message", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.componentProps.label", "31T-gCNChd56y2mwABsh5.props.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.11.label", "gqP-RqBG6MVuwZRTQMYT0.props.label", "T4oVwaiz9yj5qy8NHjOZn.props.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.componentProps.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-edit.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.filterFields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.5.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.9.label", "Zz6bS1sIbx-CBc2Bdr8Ha.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.label", "Zz6bS1sIbx-CBc2Bdr8Ha.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.4.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.filterFields.1.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-updatedAt.props.label", "9mTXB_FpvUVv6mXYHZrDf.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.7.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdAt.props.label", "IK2E9AhCg_i6GBfNg4vh8.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.filterFields.2.label", "YsEJMYXhnaZufgQKxp-6I.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedAt.props.rules.0.message", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-createdBy.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.9.componentProps.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.4.componentProps.placeholder", "9mTXB_FpvUVv6mXYHZrDf.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-action-cancel.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-version.props.rules.0.message", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.8.label", "TApYuHlTDE1yZYh0-VUas.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.1.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.12.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs.props.items.1.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.3.componentProps.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.12.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.9.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.8.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.fields.2.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.6.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.11.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-id.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.9.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-createdAt.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-action-save.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.7.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.9.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.10.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdBy.props.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.componentProps.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.5.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.fields.1.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.11.componentProps.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-id.props.componentProps.placeholder", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.6.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.8.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch.props.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.filterFields.1.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf.props.filterFields.0.componentProps.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.filterFields.3.label", "wC86TF8Wkifw5kOVop9UO.props.editComponentProps.fields.5.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf.props.filterFields.0.label", "5setP67n1iTU4_Ch0gAu4.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.componentProps.placeholder", "tNSjV339adsAsbEljGoei.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy.props.editComponentProps.fields.3.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg.props.editComponentProps.fields.3.componentProps.label", "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedAt.props.label", "wC86TF8Wkifw5kOVop9UO.props.label"]}, "key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list", "permissionKey": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "TB2B$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_pur_inv_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-new", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch/items/COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-TERP_MIGRATE$ext_wq_pur_inv_org_cf-multi-delete", "label": "批量删除", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "TB2B$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_pur_inv_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-import", "label": "导入", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-sub-model", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/template/download", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/config/predict", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}, {"key": "/api/trantor/workflow/v2/{param0}", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-export", "label": "导出", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/trantor/portal/user/current", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "IK2E9AhCg_i6GBfNg4vh8", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "type": "Table"}, {"key": "d17rATmKOqWiXnKofE11h", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "gqP-RqBG6MVuwZRTQMYT0", "label": "需求公司编码", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "type": "Table"}, {"key": "d17rATmKOqWiXnKofE11h", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "tNSjV339adsAsbEljGoei", "label": "需求公司名称", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "type": "Table"}, {"key": "d17rATmKOqWiXnKofE11h", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "9mTXB_FpvUVv6mXYHZrDf", "label": "采购组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "type": "Table"}, {"key": "d17rATmKOqWiXnKofE11h", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "31T-gCNChd56y2mwABsh5", "label": "采购组织名称", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-list-TERP_MIGRATE$ext_wq_pur_inv_org_cf", "label": "表格", "type": "Table"}, {"key": "d17rATmKOqWiXnKofE11h", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "TB2B$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_pur_inv_org_cf"}, "type": "SystemService"}, {"key": "TB2B$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_pur_inv_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "UNWrz5STfJpAc97bL4G9F", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "TB2B$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_pur_inv_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$ORG_PUR_INV_ORG_CF_SAVE_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TB2B$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_pur_inv_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detail-TERP_MIGRATE$ext_wq_pur_inv_org_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-version", "label": "版本号", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg", "label": "需求公司编码", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_language_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_language_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_sls_dc_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_sls_dc_cf"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "5setP67n1iTU4_Ch0gAu4", "label": "需求公司名称", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "wC86TF8Wkifw5kOVop9UO", "label": "采购组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_language_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_language_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_timezone_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_timezone_type_cf"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TApYuHlTDE1yZYh0-VUas", "label": "采购组织名称", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-q5Uzs4z1uZuMNBsyrX4Ks", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-editView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-field-invOrg", "label": "需求公司编码", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-UzwZBpl9rjjjDo0jI5EWU", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "Zz6bS1sIbx-CBc2Bdr8Ha", "label": "需求公司名称", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-UzwZBpl9rjjjDo0jI5EWU", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "T4oVwaiz9yj5qy8NHjOZn", "label": "采购组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-UzwZBpl9rjjjDo0jI5EWU", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "YsEJMYXhnaZufgQKxp-6I", "label": "采购组织名称", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-UzwZBpl9rjjjDo0jI5EWU", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-dzAI_tdk0wvwqurnntolJ", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-dzAI_tdk0wvwqurnntolJ", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-dzAI_tdk0wvwqurnntolJ", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-TERP_MIGRATE$ext_wq_pur_inv_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-dzAI_tdk0wvwqurnntolJ", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PRUCHASE_ORG_DISTRIBUTION-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}