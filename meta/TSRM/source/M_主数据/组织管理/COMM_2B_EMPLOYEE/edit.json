{"access": "Private", "key": "TSRM$COMM_2B_EMPLOYEE:edit", "name": "edit", "props": {"containerSelect": {"COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md": [{"field": "version", "selectFields": null}, {"field": "code", "selectFields": null}, {"field": "name", "selectFields": null}, {"field": "status", "selectFields": null}, {"field": "type", "selectFields": null}, {"field": "email", "selectFields": null}, {"field": "mobile", "selectFields": null}, {"field": "addressId", "selectFields": null}, {"field": "addressDetail", "selectFields": null}, {"field": "entryAt", "selectFields": null}, {"field": "resignationAt", "selectFields": null}]}, "content": {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-editView-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑员工信息表\t\" : \"创建员工信息表\t\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "G6E58O19OIeHej7wy0BNR", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "uJfOIZYRhqHx-y-QOYdoH", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-code", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "员工编码", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "fEAEfqk9Rfah7xM1vQcaa", "leftValue": {"fieldType": "Number", "options": [], "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "sys_common$org_employee_md.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "Kev0_Vq3Rtm8uRcnDM_N3", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "GCBsqVewFRDko_qpGj2PM", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "DkS6BxbvhKB9R35k9eCKL", "operator": null, "valueRules": null}], "name": "code", "rules": [{"message": "请输入员工编码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-name", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "姓名", "name": "name", "rules": [{"message": "请输入姓名", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-status", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "initialValue": "INACTIVE", "label": "状态", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "6jeSE9Lf4EHL577S6XP8x", "operator": null, "valueRules": null}], "name": "status", "rules": [{"message": "请输入状态", "required": true}], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-type", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "员工类型", "name": "type", "rules": [{"message": "请输入员工类型", "required": true}], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-email", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "邮箱", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "l_74P_qZHvOKe9cDlWaUP", "valueRules": null}], "name": "email", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-mobile", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "手机", "name": "mobile", "rules": [{"message": "请输入手机", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-addressId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"cascaderField": "addrParentId", "fields": [], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_addr_type_cf"}}], "serviceKey": "ERP_GEN$SYS_FindTreeChildrenDataService", "type": "InvokeSystemService"}, "labelField": ["addrName"], "leafOnly": false, "modelAlias": "ERP_GEN$gen_addr_type_cf", "reverseConstructFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}, "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "SelfRelation", "hidden": false, "label": "地址", "name": "addressId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-addressDetail", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "详细地址", "name": "addressDetail", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-entryAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "entryAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "入职日期", "name": "entryAt", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-resignationAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "resignationAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "离职日期", "name": "resignationAt", "rules": [], "type": "DATE"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": false}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "modelAlias": "sys_common$org_employee_md", "params$": "{ id: route.recordId }", "serviceKey": "sys_common$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "modelAlias": "sys_common$org_employee_md", "params$": "{ id: route?.query?.copyId }", "serviceKey": "sys_common$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "modelAlias": "sys_common$org_employee_md"}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-edit_perm_ac_z_2_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md"}, "type": "action"}}], "service": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["ROOT"]}, {"action": "Message", "message": "保存成功"}, {"action": "OpenView", "openViewConfig": {"page": {"key": "TSRM$COMM_2B_EMPLOYEE-list", "name": "list", "type": "View"}, "refresh": true, "type": "NewPage"}}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-edit_perm_ac_z_2_1", "showCondition": {}, "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "i18nConfig": {"i18nKeySet": ["选择更新人", "手机", "状态", "请输入版本号", "请输入员工类型", "请输入创建时间", "请输入员工编码", "详细地址", "入职日期", "离职日期", "保存", "姓名", "邮箱", "请输入ID", "保存成功", "用户名", "地址", "ID", "创建人", "请输入更新时间", "请输入姓名", "编辑员工信息表", "逻辑删除标识", "请输入手机", "选择地址", "请选择", "版本号", "请输入状态", "更新时间", "创建员工信息表", "更新人", "请输入", "选择创建人", "取消", "创建时间", "员工类型", "请输入逻辑删除标识", "员工编码"], "i18nScanPaths": ["COMMON_2B$COMM_2B_EMPLOYEE-editView-footer-save.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-status.props.rules.0.message", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-mobile.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-name.props.rules.0.message", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedAt.props.rules.0.message", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-type.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-addressId.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-id.props.rules.0.message", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdBy.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedAt.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedBy.props.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-name.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-id.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-addressDetail.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-version.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-deleted.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-entryAt.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-addressId.props.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-type.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-status.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-name.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-version.props.rules.0.message", "@exp:COMMON_2B$COMM_2B_EMPLOYEE-editView-title.props.title", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-status.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-deleted.props.rules.0.message", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-id.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-entryAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-addressId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-resignationAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-editView-footer-save.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-version.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-code.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-code.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-resignationAt.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-code.props.rules.0.message", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-addressDetail.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdAt.props.rules.0.message", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-mobile.props.rules.0.message", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-email.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-mobile.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-type.props.rules.0.message", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedBy.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-email.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdAt.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-editView-footer-cancel.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-deleted.props.componentProps.placeholder"]}, "key": "TSRM$COMM_2B_EMPLOYEE-edit", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-edit_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-editView-footer-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-editView-footer-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [{"key": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-version", "label": "版本号", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-deleted", "label": "逻辑删除标识", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-code", "label": "员工编码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-name", "label": "姓名", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-status", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-type", "label": "员工类型", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-email", "label": "邮箱", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-mobile", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-addressId", "label": "地址", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindTreeChildrenDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_ReverseConstructTreeService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-addressDetail", "label": "详细地址", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-entryAt", "label": "入职日期", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md-for-widget-resignationAt", "label": "离职日期", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-TERP_MIGRATE$org_employee_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-total-config-container-TERP_MIGRATE$org_employee_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "title": "edit", "type": "FORM"}, "type": "View"}