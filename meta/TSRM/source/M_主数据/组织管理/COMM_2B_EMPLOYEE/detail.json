{"access": "Private", "key": "TSRM$COMM_2B_EMPLOYEE:detail", "name": "detail", "props": {"content": {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "员工信息详情", "useExpression": false}, "type": "Meta"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-detail_perm_ac_z_1_0", "showCondition": {"conditions": [{"conditions": [{"id": "w5U5BqqPLLwS64GgZiWWd", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "INACTIVE", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "PYLP2YlXviSwhKO1h-39K", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "DISABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "QKKEE5xzMtkIeLIH33iuh", "logicOperator": "OR", "type": "ConditionGroup"}], "id": "9l8U1UJAXw1-QmpWBOI6y", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-TERP_MIGRATE$org_employee_md-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-code", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "员工编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-name", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "姓名", "name": "name", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-type", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentType": "Enum", "editable": false, "label": "员工类型", "name": "type", "type": "SELECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-mobile", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "手机", "name": "mobile", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-email", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "邮箱", "name": "email", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "地址", "name": "addressId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressDetail", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "addressDetail", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "详细地址", "name": "addressDetail", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-entryAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "entryAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "label": "入职日期", "name": "entryAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-resignationAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "resignationAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "label": "离职日期", "name": "resignationAt", "type": "DATE"}}, {"children": [], "key": "-TIJSgQuvJMjJAiIAGwem", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "extWqResignationFlag", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editable": false, "label": "离职标记", "name": "extWqResignationFlag", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-status", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "状态", "name": "status", "type": "SELECT"}}], "key": "A45jI71YowHwN4V0LyYvT", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-userId", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "nickname", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "昵称", "name": "userId.nickname", "required": false, "type": "TEXT", "width": 120}}, {"children": [], "key": "ZVrD-LfB_crTVkBL0xpMH", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "username", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "用户名", "name": "userId.username", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Zg6ypb2jsziTOpSMdeVop", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "用户邮箱", "name": "userId.email", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "3Z8EXPsqTmE7zfvG5B37d", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "用户手机", "name": "userId.mobile", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "cn4e74lxANutwHKp0oD-a", "name": "DetailGroupItem", "props": {"title": "员工账号信息"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "jISZdvIaZMLhWm0M7DMC3", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$COMM_2B_EMPLOYEE-LzTNBgGQdOcM5Ie2WLE7V", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [], "key": "znBzesXPAsqqSstn1A6_z", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "mDNcnAHu-V8tK5qsPbTNi", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "gFJP-a7Q_xbSsdnXGAV5w", "name": "Field", "props": {"componentProps": {"fieldAlias": "admOrg", "label": "选择行政组织", "labelField": "code", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "name", "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "qMUs7fCJugiICYrb4rTwt", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "行政组织", "modelAlias": "sys_common$org_struct_md", "name": "admOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "jQXxEmCP6HG9jMY4NYegn", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "orgType", "status"], "fieldAlias": "orgRole", "label": "选择组织角色", "labelField": "code", "modelAlias": "TB2B$ext_wq_org_role_cf", "parentModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TB2B$SYS_FindDataByIdService", "searchServiceKey": "TB2B$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "组织角色", "name": "orgRole", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "W0SoupspT58ceR7Wj9TF2", "name": "Field", "props": {"componentProps": {"fieldAlias": "invOrg", "label": "选择需求单位", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "ez42xAodqY-qg_8PMv9lt", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "库存组织", "name": "invOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "_2Rhg51FwbG8_daSuvSGQ", "name": "Field", "props": {"componentProps": {"fieldAlias": "comInfo", "label": "选择所属公司", "labelField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "parentModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "name", "parentModelAlias": "ERP_GEN$gen_com_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "公司名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "CT7glOLTEDMMilPva6-RN", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "所属公司", "name": "comInfo", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ROmQRrcf2ic8951aWR_V8", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "leader", "modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "是否领导", "name": "leader", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}], "key": "s6sK8V18SXtXP7CrV1yv_", "name": "Fields", "props": {}, "type": "Meta"}], "key": "Nv_xp0aUNzyyYMyum8ACY", "name": "Table", "props": {"enableSolution": false, "fieldName": "extWqAdmOrg", "flow": {"context$": "$context", "name": "extWqAdmOrg", "type": "RelationData"}, "label": "表格", "mode": "simple", "showType": "normal"}, "type": "Container"}], "key": "tX1sS1pyYVoW3LDXUQ15P", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "label": "自定义详情字段"}, "type": "Meta"}], "key": "XvYp2hD-6hF1vhOCUslFr", "name": "DetailGroupItem", "props": {"title": "行政组织"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "3J1BNN5XQOqLk3SW3xWZl", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$COMM_2B_EMPLOYEE-PO68oRbBa67jxmenFPqb9", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [], "key": "y0k6NfwFx-JZ_CQZ6dOXo", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "zpJMCEXoOnL5GZameLZkx", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "HlFh0bJJNe2KonHYRcqLZ", "name": "Field", "props": {"componentProps": {"fieldAlias": "purOrg", "label": "选择采购组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_employee_pur_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Ezw0BYBoY5eZmcO7TbJF_", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "采购组织", "name": "purOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-ukQ_jSYyQ8YcfO_63Rmuw", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "label": "采购组织编码", "modelAlias": "sys_common$org_struct_md", "name": "purOrg.code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "X0cCTgioSDfHmOOMHPQJT", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TamMu9lcXFWSpxCfjeWOi", "name": "Table", "props": {"enableSolution": false, "fieldName": "extWqPurOrg", "flow": {"context$": "$context", "name": "extWqPurOrg", "type": "RelationData"}, "label": "表格", "mode": "simple", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf"}, "type": "Container"}], "key": "iUxUeNnWGtMtUquDOADvh", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "label": "自定义详情字段"}, "type": "Meta"}], "key": "jeltjNBA2-ADZ1lcYiTIu", "name": "DetailGroupItem", "props": {"title": "采购组织"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": ""}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "name": "Detail", "props": {"flow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "code", "fieldName": "员工编码", "fieldType": "TEXT"}, {"fieldAlias": "name", "fieldName": "姓名", "fieldType": "TEXT"}, {"fieldAlias": "status", "fieldName": "状态", "fieldType": "ENUM"}, {"fieldAlias": "type", "fieldName": "员工类型", "fieldType": "ENUM"}, {"fieldAlias": "email", "fieldName": "邮箱", "fieldType": "TEXT"}, {"fieldAlias": "mobile", "fieldName": "手机", "fieldType": "TEXT"}, {"fieldAlias": "addressDetail", "fieldName": "详细地址", "fieldType": "TEXT"}, {"fieldAlias": "entryAt", "fieldName": "入职日期", "fieldType": "DATE"}, {"fieldAlias": "resignationAt", "fieldName": "离职日期", "fieldType": "DATE"}, {"fieldAlias": "idCard", "fieldName": "身份证号", "fieldType": "TEXT"}, {"fieldAlias": "userId", "fieldName": "用户", "fieldType": "OBJECT"}, {"fieldAlias": "addressId", "fieldName": "地址", "fieldType": "NUMBER"}, {"fieldAlias": "employeeOrgLinkList", "fieldName": "组织单元", "fieldType": "Array"}, {"fieldAlias": "dingTalkCode", "fieldName": "钉钉用户ID", "fieldType": "TEXT"}, {"fieldAlias": "orgStructId", "fieldName": "组织", "fieldType": "OBJECT"}, {"fieldAlias": "userName", "fieldName": "用户名", "fieldType": "TEXT"}, {"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "route.recordId", "type": "expression"}}, {"fieldAlias": "created<PERSON>y", "fieldName": "创建人", "fieldType": "OBJECT"}, {"fieldAlias": "updatedBy", "fieldName": "更新人", "fieldType": "OBJECT"}, {"fieldAlias": "createdAt", "fieldName": "创建时间", "fieldType": "DATE"}, {"fieldAlias": "updatedAt", "fieldName": "更新时间", "fieldType": "DATE"}, {"fieldAlias": "version", "fieldName": "版本号", "fieldType": "NUMBER"}, {"fieldAlias": "deleted", "fieldName": "逻辑删除标识", "fieldType": "NUMBER"}, {"fieldAlias": "originOrgId", "fieldName": "所属组织", "fieldType": "NUMBER"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "required": null}], "serviceKey": "sys_common$org_employee_md_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "layout": "horizontal", "modelAlias": "sys_common$org_employee_md", "onFinishActionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "valueConfig": {"action": {"name": "getData", "target": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail"}, "type": "action"}}], "service": "sys_common$SYS_SaveDataService"}, "endLogic": "Other", "executeLogic": "BindService"}, "saveType": "service"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["组织名称", "手机", "表格", "状态", "公司名称", "采购组织编码", "库存组织", "是否领导", "详细地址", "入职日期", "离职日期", "姓名", "邮箱", "用户邮箱", "选择需求单位", "昵称", "地址", "用户名", "ID", "员工账号信息", "用户手机", "自定义详情字段", "详情组标题", "所属公司", "选择采购组织", "选择地址", "请选择", "基本信息", "采购组织", "组织角色", "员工信息详情", "编辑", "行政组织", "请输入", "选择组织角色", "员工类型", "选择所属公司", "离职标记", "选择行政组织", "员工编码"], "i18nScanPaths": ["COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout.props.title", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-userId.props.componentProps.placeholder", "mDNcnAHu-V8tK5qsPbTNi.props.componentProps.placeholder", "jQXxEmCP6HG9jMY4NYegn.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-status.props.componentProps.placeholder", "Zg6ypb2jsziTOpSMdeVop.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-ukQ_jSYyQ8YcfO_63Rmuw.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-name.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressId.props.componentProps.placeholder", "Nv_xp0aUNzyyYMyum8ACY.props.label", "gFJP-a7Q_xbSsdnXGAV5w.props.editComponentProps.fields.0.componentProps.placeholder", "TamMu9lcXFWSpxCfjeWOi.props.label", "3Z8EXPsqTmE7zfvG5B37d.props.label", "3Z8EXPsqTmE7zfvG5B37d.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-email.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-status.props.label", "_2Rhg51FwbG8_daSuvSGQ.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-email.props.label", "gFJP-a7Q_xbSsdnXGAV5w.props.componentProps.label", "W0SoupspT58ceR7Wj9TF2.props.componentProps.placeholder", "gFJP-a7Q_xbSsdnXGAV5w.props.componentProps.placeholder", "Zg6ypb2jsziTOpSMdeVop.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressDetail.props.label", "W0SoupspT58ceR7Wj9TF2.props.label", "jeltjNBA2-ADZ1lcYiTIu.props.title", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressDetail.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressId.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-entryAt.props.label", "_2Rhg51FwbG8_daSuvSGQ.props.label", "ROmQRrcf2ic8951aWR_V8.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-userId.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-mobile.props.label", "XvYp2hD-6hF1vhOCUslFr.props.title", "_2Rhg51FwbG8_daSuvSGQ.props.editComponentProps.fields.0.componentProps.placeholder", "cn4e74lxANutwHKp0oD-a.props.title", "A45jI71YowHwN4V0LyYvT.props.title", "ZVrD-LfB_crTVkBL0xpMH.props.componentProps.placeholder", "-TIJSgQuvJMjJAiIAGwem.props.label", "zpJMCEXoOnL5GZameLZkx.props.componentProps.placeholder", "jQXxEmCP6HG9jMY4NYegn.props.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressId.props.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-name.props.label", "ZVrD-LfB_crTVkBL0xpMH.props.label", "_2Rhg51FwbG8_daSuvSGQ.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-resignationAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-code.props.componentProps.placeholder", "jQXxEmCP6HG9jMY4NYegn.props.label", "gFJP-a7Q_xbSsdnXGAV5w.props.editComponentProps.fields.0.label", "HlFh0bJJNe2KonHYRcqLZ.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-entryAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-ukQ_jSYyQ8YcfO_63Rmuw.props.label", "mDNcnAHu-V8tK5qsPbTNi.props.label", "W0SoupspT58ceR7Wj9TF2.props.editComponentProps.fields.0.componentProps.placeholder", "W0SoupspT58ceR7Wj9TF2.props.editComponentProps.fields.0.label", "@exp:COMMON_2B$COMM_2B_EMPLOYEE-detail-page-title.props.title", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-resignationAt.props.label", "gFJP-a7Q_xbSsdnXGAV5w.props.label", "ROmQRrcf2ic8951aWR_V8.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-mobile.props.componentProps.placeholder", "tX1sS1pyYVoW3LDXUQ15P.props.label", "_2Rhg51FwbG8_daSuvSGQ.props.componentProps.label", "HlFh0bJJNe2KonHYRcqLZ.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-type.props.label", "-TIJSgQuvJMjJAiIAGwem.props.componentProps.placeholder", "W0SoupspT58ceR7Wj9TF2.props.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-code.props.label", "HlFh0bJJNe2KonHYRcqLZ.props.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-type.props.componentProps.placeholder", "iUxUeNnWGtMtUquDOADvh.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header-edit.props.label", "zpJMCEXoOnL5GZameLZkx.props.label", "HlFh0bJJNe2KonHYRcqLZ.props.editComponentProps.fields.0.componentProps.placeholder", "HlFh0bJJNe2KonHYRcqLZ.props.label"]}, "key": "TSRM$COMM_2B_EMPLOYEE-detail", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-detail_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}], "relations": [{"key": "sys_common$org_employee_md_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-TERP_MIGRATE$org_employee_md-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-code", "label": "员工编码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-name", "label": "姓名", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-type", "label": "员工类型", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-mobile", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-email", "label": "邮箱", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressId", "label": "地址", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-addressDetail", "label": "详细地址", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-entryAt", "label": "入职日期", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-resignationAt", "label": "离职日期", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "-TIJSgQuvJMjJAiIAGwem", "label": "离职标记", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-status", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "A45jI71YowHwN4V0LyYvT", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout-For-DetailField-userId", "label": "昵称", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "cn4e74lxANutwHKp0oD-a", "label": "员工账号信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ZVrD-LfB_crTVkBL0xpMH", "label": "用户名", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "cn4e74lxANutwHKp0oD-a", "label": "员工账号信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "Zg6ypb2jsziTOpSMdeVop", "label": "用户邮箱", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "cn4e74lxANutwHKp0oD-a", "label": "员工账号信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "3Z8EXPsqTmE7zfvG5B37d", "label": "用户手机", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "cn4e74lxANutwHKp0oD-a", "label": "员工账号信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TamMu9lcXFWSpxCfjeWOi", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "jeltjNBA2-ADZ1lcYiTIu", "label": "采购组织", "type": "DetailGroupItem"}, {"key": "iUxUeNnWGtMtUquDOADvh", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "mDNcnAHu-V8tK5qsPbTNi", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "gFJP-a7Q_xbSsdnXGAV5w", "label": "行政组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "jQXxEmCP6HG9jMY4NYegn", "label": "组织角色", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TB2B$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_org_role_cf"}, "type": "SystemService"}, {"key": "TB2B$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_org_role_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "W0SoupspT58ceR7Wj9TF2", "label": "库存组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "_2Rhg51FwbG8_daSuvSGQ", "label": "所属公司", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ROmQRrcf2ic8951aWR_V8", "label": "是否领导", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "XvYp2hD-6hF1vhOCUslFr", "label": "行政组织", "type": "DetailGroupItem"}, {"key": "tX1sS1pyYVoW3LDXUQ15P", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "Nv_xp0aUNzyyYMyum8ACY", "label": "表格", "type": "Table"}, {"key": "s6sK8V18SXtXP7CrV1yv_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "zpJMCEXoOnL5GZameLZkx", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "jeltjNBA2-ADZ1lcYiTIu", "label": "采购组织", "type": "DetailGroupItem"}, {"key": "iUxUeNnWGtMtUquDOADvh", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TamMu9lcXFWSpxCfjeWOi", "label": "表格", "type": "Table"}, {"key": "X0cCTgioSDfHmOOMHPQJT", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "HlFh0bJJNe2KonHYRcqLZ", "label": "采购组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "jeltjNBA2-ADZ1lcYiTIu", "label": "采购组织", "type": "DetailGroupItem"}, {"key": "iUxUeNnWGtMtUquDOADvh", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TamMu9lcXFWSpxCfjeWOi", "label": "表格", "type": "Table"}, {"key": "X0cCTgioSDfHmOOMHPQJT", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-ukQ_jSYyQ8YcfO_63Rmuw", "label": "采购组织编码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}, {"key": "jeltjNBA2-ADZ1lcYiTIu", "label": "采购组织", "type": "DetailGroupItem"}, {"key": "iUxUeNnWGtMtUquDOADvh", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TamMu9lcXFWSpxCfjeWOi", "label": "表格", "type": "Table"}, {"key": "X0cCTgioSDfHmOOMHPQJT", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "detail", "type": "DETAIL"}, "type": "View"}