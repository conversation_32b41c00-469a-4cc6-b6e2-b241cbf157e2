{"access": "Private", "key": "TSRM$COMM_2B_EMPLOYEE:list", "name": "list", "props": {"containerSelect": {"9PvbyRxGyPL66MufXbIku": [{"field": "id", "selectFields": null}, {"field": "code", "selectFields": null}, {"field": "name", "selectFields": null}, {"field": "status", "selectFields": null}, {"field": "type", "selectFields": null}, {"field": "mobile", "selectFields": null}, {"field": "userId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}], "P8y-C3JsFmBc5wwUG_g5m": [{"field": "id", "selectFields": null}, {"field": "extWqAdmOrg", "selectFields": []}], "vK9BIf6Z_d4pVGORjP1oy": [{"field": "id", "selectFields": null}, {"field": "extWqPurOrg", "selectFields": []}]}, "content": {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-list-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-list-title", "name": "Page<PERSON><PERSON>le", "props": {"title": ""}, "type": "Meta"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "bHuMYXy9wY4y2xh9qwtRh", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "label": "ID", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "readOnly": true, "required": false}, "key": "tfXMBQ_6ulWPHLS8M5fy7", "operator": null, "valueRules": null}], "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [{"children": [], "key": "zLjKKdRDC40--WGOy-RAB", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "H6d6sliVdPLSrpV5vdOWl", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "bIw1CaBqQFzqPesVNHJ9x", "name": "Field", "props": {"componentProps": {"fieldAlias": "admOrg", "label": "选择行政组织", "labelField": "code", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "name", "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": false, "isRelationColumn": true, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"fieldAlias": "extWqNccCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "NCC组织编码", "name": "extWqNccCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "extWqNcCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "NC组织编码", "name": "extWqNcCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "extWqSapCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "SAP组织编码", "name": "extWqSapCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "extWqK3Code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "K3组织编码", "name": "extWqK3Code", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "vendCode", "status", "purOrgId", "slsOrgId", "slsCustInfoId", "purVendInfoId", "orgSlsDcId", "extBelongType", "testbomHeader1Id", "extWqNccCode", "extWqNcCode", "extWqSapCode", "extWqK3Code", "extWqComInfo", "extWqErpType", "extWqErpPurOrgCode", "extWqErpPurOrgName"], "fieldAlias": "extWqInvOrg", "label": "选择需求单位", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "isRelationColumn": true, "label": "需求单位", "name": "extWqInvOrg", "required": false, "type": "OBJECT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": false, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"fieldAlias": "extWqNccCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "NCC组织编码", "name": "extWqNccCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "extWqNcCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "NC组织编码", "name": "extWqNcCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "extWqSapCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "SAP组织编码", "name": "extWqSapCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "extWqK3Code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "K3组织编码", "name": "extWqK3Code", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "vendCode", "status", "purOrgId", "slsOrgId", "slsCustInfoId", "purVendInfoId", "orgSlsDcId", "extBelongType", "testbomHeader1Id", "extWqNccCode", "extWqNcCode", "extWqSapCode", "extWqK3Code", "extWqComInfo", "extWqErpType", "extWqErpPurOrgCode", "extWqErpPurOrgName"], "fieldAlias": "extWqInvOrg", "label": "选择需求单位", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "需求单位", "name": "extWqInvOrg", "required": false, "type": "OBJECT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "行政组织", "name": "admOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-Sq_sLoMqfePbFpU0tjPtP", "name": "Field", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "D4awmaMZ8FphIEbscuv-Z", "valueRules": null}], "name": "admOrg.code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "QP5f4YFv2cFQSmODDmsxb", "name": "Fields", "props": {}, "type": "Meta"}], "key": "yi_q7SfgEi2oi3RNQrPnp", "name": "TableForm", "props": {"fieldName": "extWqAdmOrg", "fields": [], "hideCreator": false, "label": "表格表单", "modelAlias": "TB2B$ext_wq_employee_adm_org_cf"}, "type": "Widget"}], "key": "MULmo8eYBQ1fcfH7z4V03", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "displayName": "行政组织", "key": "P8y-C3JsFmBc5wwUG_g5m", "name": "FormGroup", "props": {"flow": {"children": [{"containerKey": "P8y-C3JsFmBc5wwUG_g5m", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_FindDataByIdService", "test": true, "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "sys_common$org_employee_md"}, "type": "Container"}], "key": "A7ijKeHWCZCd7wo3m807o", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "qDJhA7D9Ru5IQTDovNyyG", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["VWZFot32AzPGwzG9VzKVK"]}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_2_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "DYOMehrzeIu69NgfqW7sh", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "data", "fieldName": "data", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "required": null, "valueConfig": {"action": {"selector": "", "target": "P8y-C3JsFmBc5wwUG_g5m"}, "type": "action"}}], "service": "TSRM$ORG_EMPLOYEE_MD_ADM_SERVICE_NEW"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["VWZFot32AzPGwzG9VzKVK"]}, {"action": "Message", "message": "保存成功"}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_2_1_1", "showCondition": {}, "type": "primary"}, "type": "Widget"}], "key": "nzXvTjbH7WiU5k3oXIzkI", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "VWZFot32AzPGwzG9VzKVK", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"bodyStyle": {}, "width": 1200}, "params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "title": "分配行政组织"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "3g7WIjt6YK9GqLewML4-h", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "label": "ID", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "readOnly": true, "required": false}, "key": "8KiEML-YrmFpRdH16S-hS", "operator": null, "valueRules": null}], "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [{"children": [], "key": "KzgAYp50JYG-hRvuD2WEA", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "kEvrXOdl4cNFNyA-_QQXH", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "5XoAe2Lo1fYezOx5H-zFz", "name": "Field", "props": {"componentProps": {"fieldAlias": "purOrg", "label": "选择采购组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TB2B$ext_wq_employee_pur_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId", "extWqNccCode", "extWqSapCodeWq", "extWqNcCodeWq", "extWqK3CodeWq", "extWqComType", "extAbbr", "extEstBank", "extEstName", "extBankAcct", "genWcHeadId", "commUscc", "lglRepr", "addrId", "addrDesc"], "fieldAlias": "comOrgCode", "label": "选择所属公司组织", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "hidden": false, "label": "所属公司组织", "name": "comOrgCode", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["languageCode", "languageName"], "fieldAlias": "languageId", "label": "选择语言", "labelField": "languageName", "modelAlias": "ERP_GEN$gen_language_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "语言", "name": "languageId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "area", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "区号", "name": "area", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "tele", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "电话/分机号", "name": "tele", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "fax", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "传真/分机号", "name": "fax", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mail", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "邮箱", "name": "mail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "phone", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "手机", "name": "phone", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["addrCode", "addrName", "postCode", "counId", "<PERSON><PERSON><PERSON><PERSON>", "addrParentId", "plnSlsOrgTypeCfId", "plnSlsOrgTypeCfTestId", "plnSoaRegconsMdId"], "fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "地址", "name": "addressId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "addressDetail", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "街道", "name": "addressDetail", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["timezoneCode", "timezoneDesc", "counId", "timezoneDistrict", "timezoneFormat", "isSummerTime", "summerTimeStart", "summerTimeEnd", "status"], "fieldAlias": "timezoneId", "label": "选择时区", "labelField": "timezoneDesc", "modelAlias": "ERP_GEN$gen_timezone_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "时区", "name": "timezoneId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "postcode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "邮编", "name": "postcode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "coordinate", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "经纬度坐标", "name": "coordinate", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "vendCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "公司间开票的供应商编号", "name": "vendCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "custCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "公司间开票的客户编号", "name": "custCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "vendCode", "status", "purOrgId", "slsOrgId", "slsCustInfoId", "purVendInfoId", "orgSlsDcId", "extBelongType", "testbomHeader1Id", "extWqNccCode", "extWqNcCode", "extWqSapCode", "extWqK3Code", "extWqComInfo", "extWqErpType", "extWqErpPurOrgCode", "extWqErpPurOrgName"], "fieldAlias": "invOrgId", "label": "选择库存组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "库存组织", "name": "invOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["vendPurCode", "vendPurDimension", "typeName", "remark", "status", "partnerProcedureRef", "attachmentProcedureRef", "isInvestigation", "isApprove"], "fieldAlias": "extWqVendWarehouse", "label": "选择供应商库配置", "labelField": "typeName", "modelAlias": "ERP_GEN$gen_vend_pur_type_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "供应商库配置", "name": "extWqVendWarehouse", "required": false, "type": "OBJECT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "modelAlias": "sys_common$org_struct_md", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "采购组织", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "Zpi3cjuCJ5uqogGKMA02d", "valueRules": null}], "name": "purOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-6P9ApbGq0UKj4nwK-d_R1", "name": "Field", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentProps": {"fields": [], "labelField": []}, "editComponentType": "InputText", "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "qPuaEGQvt_4lUUGEXowgI", "valueRules": null}], "name": "purOrg.code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "8kPRTmWK18QSF9xxesrV1", "name": "Fields", "props": {}, "type": "Meta"}], "key": "z2BaDFxTPLXn2lf6E307e", "name": "TableForm", "props": {"allowRowSelect": false, "fieldName": "extWqPurOrg", "fields": [], "hideCreator": false, "label": "表格表单", "modelAlias": "TB2B$ext_wq_employee_pur_org_cf"}, "type": "Widget"}], "key": "sySzxlhusRteQSWM8O_Sc", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "vK9BIf6Z_d4pVGORjP1oy", "name": "FormGroup", "props": {"flow": {"children": [{"containerKey": "vK9BIf6Z_d4pVGORjP1oy", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_FindDataByIdService", "test": true, "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "sys_common$org_employee_md"}, "type": "Container"}], "key": "eB_G92e3c45NCsWiaKRRK", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "rqaMxENImfO6LfnHXmsyx", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["l9lEXaIwdO6fqrpgichlf"]}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_3_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "JfHoVRn3kXe9axflVyjMT", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "data", "fieldName": "data", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "valueConfig": {"action": {"selector": "", "target": "vK9BIf6Z_d4pVGORjP1oy"}, "type": "action"}}], "service": "TSRM$ORG_EMPLOYEE_MD_PUR_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["l9lEXaIwdO6fqrpgichlf"]}, {"action": "Message", "level": "success", "message": "保存成功！"}, {"action": "Refresh", "target": ["COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_3_1_1", "showCondition": {}, "type": "primary"}, "type": "Widget"}], "key": "DJnFs3AKcbZDmW2c9UIKF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "l9lEXaIwdO6fqrpgichlf", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"bodyStyle": {}, "width": 1200}, "params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "title": "维护采购组织关系"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "cNfkCjwfPbl7GaLdeQExn", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentType": "InputNumber", "label": "ID", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "VmK2RX4xSZe7vixbRddJB", "valueRules": null}], "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "koIH640bNBWkydT2BaUfL", "name": "FormField", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "员工编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "F3Cjfok3wnjkZCTYerTRp", "valueRules": null}], "name": "code", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "_UEaex4hdNFnj1NJIHwPv", "name": "FormField", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "姓名", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "M-EjqvBDm47ghv4MNTRvQ", "valueRules": null}], "name": "name", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "vodVtVicTtCyqqCimjfAc", "name": "FormField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "状态", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "H0GZzR07wbqi5ictno4iQ", "valueRules": null}], "name": "status", "required": true, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "eh8xh2ly7YfSIVSw9v2J1", "name": "FormField", "props": {"componentProps": {"fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "员工类型", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "Y1U4yuQhF3pHkQ23Rvn0p", "valueRules": null}], "name": "type", "required": true, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "hPoL_sVxeg1u4PEWVQ447", "name": "FormField", "props": {"componentProps": {"fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "手机", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "ocZULIqj5ZO2xhjMzP2Yt", "valueRules": null}], "name": "mobile", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "DKIOa3Mz8vwQNmQ_GeJm_", "name": "FormField", "props": {"componentProps": {"fieldAlias": "userId", "label": "选择用户", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "nickname", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "昵称", "name": "nickname", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "username", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户名", "name": "username", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "email", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mobile", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户手机", "name": "mobile", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "username", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "label": "用户名", "name": "username", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mobile", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "label": "用户手机", "name": "mobile", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "TSRM$user", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$user"}}], "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "username", "modelAlias": "TSRM$user", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "label": "用户", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "fzmCKU-mGSTFBQWH6KXio", "valueRules": null}], "name": "userId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}], "displayName": "绑定用户", "key": "9PvbyRxGyPL66MufXbIku", "name": "FormGroup", "props": {"flow": {"children": [{"containerKey": "9PvbyRxGyPL66MufXbIku", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_FindDataByIdService", "test": true, "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "sys_common$org_employee_md"}, "type": "Container"}], "key": "OP9G0GNb1XIjO0SrQ_n34", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "m2gv9faQxTkLdCYm_MY6K", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["C38OgmpPvWcoHvGrlQD04"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_4_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "k-TVzcY45XqrBfJX_0v2D", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "sys_common$org_employee_md", "valueConfig": {"action": {"selector": "", "target": "9PvbyRxGyPL66MufXbIku"}, "type": "action"}}], "service": "TSRM$ORG_EMPLOYEE_BIND_USER_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "操作成功！"}, {"action": "Close", "target": ["C38OgmpPvWcoHvGrlQD04"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md"}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_4_1_1", "type": "primary"}, "type": "Widget"}], "key": "No4i1RmGZGfYl2pzJH9AO", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "C38OgmpPvWcoHvGrlQD04", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"bodyStyle": {}, "width": 560}, "params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "title": "绑定用户"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "label": "新建", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否删除选中单据？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}}], "service": "sys_common$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-list"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "disabled$": "mode === \"design\" ? undefined :$context.selectedKeys?.length === 0", "isMultiple": true, "label": "删除", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3", "name": "ExportButton", "props": {"exportButtonServiceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "fields": [{"access": null, "alias": "id", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "id", "name": "ID", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "created<PERSON>y", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "created_by", "name": "创建人", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "sys_common$org_employee_md", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "updatedBy", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "updated_by", "name": "更新人", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "sys_common$org_employee_md", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "createdAt", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "created_at", "name": "创建时间", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "DATE", "intLength": null, "isSystemField": true, "length": null, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "updatedAt", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "updated_at", "name": "更新时间", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "DATE", "intLength": null, "isSystemField": true, "length": null, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "version", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "version", "name": "版本号", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "deleted", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "deleted", "name": "逻辑删除标识", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": false, "defaultValue": 0, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": true}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "userId", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "user_id", "name": "用户", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "user_id", "comment": "用户", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "OBJECT", "intLength": null, "isSystemField": false, "length": null, "relationMeta": {"currentModelAlias": "sys_common$org_employee_md", "currentModelFieldAlias": "userId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "code", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "code", "name": "员工编码", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "code", "comment": "员工编码", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "TEXT", "intLength": null, "isSystemField": false, "length": 32, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "name", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "name", "name": "姓名", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "name", "comment": "姓名", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "TEXT", "intLength": null, "isSystemField": false, "length": 64, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "status", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "status", "name": "状态", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "status", "comment": "状态", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": {"dictValues": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已禁用", "value": "DISABLED"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "intLength": null, "isSystemField": false, "length": 256, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "type", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "type", "name": "员工类型", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "type", "comment": "员工类型", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": {"dictValues": [{"label": "正式", "value": "FORMAL"}, {"label": "试用", "value": "PROBATION"}, {"label": "临时", "value": "TEMPORARY"}, {"label": "外包", "value": "EPIBOLY"}, {"label": "实习", "value": "PRACTICE"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "intLength": null, "isSystemField": false, "length": 256, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "email", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "email", "name": "邮箱", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "email", "comment": "邮箱", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "TEXT", "intLength": null, "isSystemField": false, "length": 32, "relationMeta": null, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "mobile", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "mobile", "name": "手机", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "mobile", "comment": "手机", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "TEXT", "intLength": null, "isSystemField": false, "length": 32, "relationMeta": null, "required": true, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "addressId", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "address_id", "name": "地址", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "address_id", "comment": "地址", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "OBJECT", "intLength": null, "isSystemField": false, "length": null, "relationMeta": {"currentModelAlias": "sys_common$org_employee_md", "currentModelFieldAlias": "addressId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_GEN$gen_addr_type_cf", "relationModelKey": "ERP_GEN$gen_addr_type_cf", "relationType": "LINK", "sync": false}, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "addressDetail", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "address_detail", "name": "详细地址", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "address_detail", "comment": "详细地址", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "TEXT", "intLength": null, "isSystemField": false, "length": 128, "relationMeta": null, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "entryAt", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "entry_at", "name": "入职日期", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "entry_at", "comment": "入职日期", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "DATE", "intLength": null, "isSystemField": false, "length": null, "relationMeta": null, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "resignationAt", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "resignation_at", "name": "离职日期", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "resignation_at", "comment": "离职日期", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "DATE", "intLength": null, "isSystemField": false, "length": null, "relationMeta": null, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "employeeOrgLinkList", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "employee_org_link_list", "name": "组织单元", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "employee_org_link_list", "comment": "组织单元", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "OBJECT", "intLength": null, "isSystemField": false, "length": null, "relationMeta": {"currentModelAlias": "sys_common$org_employee_md", "currentModelFieldAlias": "employeeOrgLinkList", "linkModelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "linkModelFieldAlias": "employeeId", "relationKey": null, "relationModelAlias": "TERP_MIGRATE$org_employee_org_link_cf", "relationModelKey": "TERP_MIGRATE$org_employee_org_link_cf", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "employeeRoleLinkList", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "employee_role_link_list", "name": "角色", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "employee_role_link_list", "comment": "角色", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "OBJECT", "intLength": null, "isSystemField": false, "length": null, "relationMeta": {"currentModelAlias": "sys_common$org_employee_md", "currentModelFieldAlias": "employeeRoleLinkList", "linkModelAlias": "TERP_MIGRATE$org_employee_role_link_cf", "linkModelFieldAlias": "employeeId", "relationKey": null, "relationModelAlias": "TERP_MIGRATE$org_employee_role_link_cf", "relationModelKey": "TERP_MIGRATE$org_employee_role_link_cf", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "extWqAdmOrg", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "ext_wq_adm_org", "name": "行政组织", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "ext_wq_adm_org", "comment": "行政组织", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "OBJECT", "intLength": null, "isSystemField": false, "length": null, "relationMeta": {"currentModelAlias": "sys_common$org_employee_md", "currentModelFieldAlias": "extWqAdmOrg", "linkModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "linkModelFieldAlias": "orgEmployeeMdId", "relationKey": null, "relationModelAlias": "TB2B$ext_wq_employee_adm_org_cf", "relationModelKey": "TB2B$ext_wq_employee_adm_org_cf", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "extWqPurOrg", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "ext_wq_pur_org", "name": "采购组织", "parentKey": null, "props": {"attachmentProps": null, "autoGenerated": false, "columnName": "ext_wq_pur_org", "comment": "采购组织", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "OBJECT", "intLength": null, "isSystemField": false, "length": null, "relationMeta": {"currentModelAlias": "sys_common$org_employee_md", "currentModelFieldAlias": "extWqPurOrg", "linkModelAlias": "TB2B$ext_wq_employee_pur_org_cf", "linkModelFieldAlias": "orgEmployeeMdId", "relationKey": null, "relationModelAlias": "TB2B$ext_wq_employee_pur_org_cf", "relationModelKey": "TB2B$ext_wq_employee_pur_org_cf", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}, {"access": null, "alias": "extWqSignature", "appId": 35339, "createdAt": null, "createdBy": null, "createdByName": null, "description": null, "key": "ext_wq_signature", "name": "签名图", "parentKey": null, "props": {"attachmentProps": {"multi": false}, "autoGenerated": false, "columnName": "ext_wq_signature", "comment": "签名图", "compositeKey": false, "defaultValue": null, "desensitizedRule": null, "dictPros": null, "encrypted": false, "fieldType": "ATTACHMENT", "intLength": null, "isSystemField": false, "length": 4000, "relationMeta": null, "required": false, "scale": null, "searchModelFieldConfigMeta": null, "unique": false}, "teamCode": null, "teamId": 1, "type": "DataStructField", "updatedAt": null, "updatedBy": null, "updatedByName": null}], "flow": {}, "getAliasFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor/struct-node/find-by-alias"}, "label": "导出", "modelAlias": "sys_common$org_employee_md", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_0_2", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}, "serviceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-list-TERP_MIGRATE$org_employee_md-logs", "name": "Logs", "props": {"currentModel": "ERP_GEN$org_employee_md", "label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$COMM_2B_EMPLOYEE-cH8832S9WAponR5-QDMA3", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "show"}]}, "buttonType": "default", "confirmOn": "off", "label": "查看", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_2_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_2_1", "showCondition": {"conditions": [], "key": "R3oI3UaaR8J1TIbnq9kol", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "FVsjv2byb-fec4N65weEl", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "record?.id", "type": "expression"}}], "service": "TSRM$ORG_EMPLOYEE_MD_ENABLE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "启用成功"}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_2_2", "showCondition": {"conditions": [{"conditions": [{"key": "k9SmavEUWYNuMhftOTwFA", "leftValue": {"fieldType": "Enum", "options": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已禁用", "value": "DISABLED"}], "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "DISABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"key": "q8mvUiTr-o_t9hi6oVshk", "leftValue": {"fieldType": "Enum", "options": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已禁用", "value": "DISABLED"}], "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "INACTIVE", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "key": "MBq-S-V5_n5VAos-2jgPb", "logicOperator": "OR", "type": "ConditionGroup"}], "key": "SIF-QgSoOq3zKjwscOmG5", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "6L--AWEL0JiqI-05GlUgz", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "record?.id", "type": "expression"}}], "service": "TSRM$ORG_EMPLOYEE_MD_DISABLE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "停用成功"}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_2_3", "showCondition": {"conditions": [{"conditions": [{"key": "FQj0nHYpmYDaCPiQUA_Vg", "leftValue": {"fieldType": "Enum", "options": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已禁用", "value": "DISABLED"}], "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "key": "BzH9RXGPrC1tGBh_PRaKo", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "p2Vicn__HaUV2bgMaVMPb", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1-button-3", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "sys_common$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_EMPLOYEE-list"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_2_4", "showCondition": {"conditions": [{"conditions": [{"key": "KS_-dvwVm93u-8S8OILmc", "leftValue": {"fieldType": "Enum", "options": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已禁用", "value": "DISABLED"}], "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "INACTIVE", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "key": "05gcy_ahs5Kul8Y9UDAVK", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "EmISgoNJ76SMyWQcDgmk3", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "40GwsQqSDxsC7t2MlCj-A", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"draggable": true, "page": {"key": "VWZFot32AzPGwzG9VzKVK", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"expression": "record?.id", "name": "id", "type": "expression"}], "type": "Modal"}}, "buttonKey": "button_h37uYemzovbj0cG8vUsU", "buttonType": "default", "confirmOn": "off", "label": "分配行政组织", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_2_5", "showCondition": {"conditions": [{"conditions": [{"key": "rIII9PGqDRFXGj1fd06pa", "leftValue": {"fieldType": "Enum", "options": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已禁用", "value": "DISABLED"}], "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "key": "dG_z6HbfP3KdwyXX833Iu", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "_FLnDRXZlw8NEVF3uaYHr", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "9ARdxvj7sDET_gdS0YDWZ", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"draggable": true, "page": {"key": "l9lEXaIwdO6fqrpgichlf", "name": "分配采购组织 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"expression": "record?.id", "name": "id", "type": "expression"}], "type": "Modal"}}, "buttonType": "default", "confirmOn": "off", "label": "分配采购组织", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_2_6", "showCondition": {"conditions": [{"conditions": [{"key": "UJCtZCaLnvK5VQbJMDPxT", "leftValue": {"fieldType": "Enum", "options": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已禁用", "value": "DISABLED"}], "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "key": "j6eUwOIn49lKCylBzv3H5", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "uQflA6G0K1S5wgWP74Zpa", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TJbM9Kajx4Ef_N8lzqyaJ", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "C38OgmpPvWcoHvGrlQD04", "name": "绑定用户 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"expression": "record?.id", "name": "id", "type": "expression"}], "type": "Modal"}}, "buttonType": "default", "confirmOn": "off", "label": "绑定用户", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac_z_5_2_7", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "B6EQ1wo0NYc64Wm0GTcWF", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "JURX3A9BBpnk2pmjByKt7", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "员工编码", "name": "code", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "-mPvo-99Lih9u3KA1GQHD", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "姓名", "name": "name", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "a9NcpDjbtmajGoBNIwLoU", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "状态", "name": "status", "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "BYaNImkTpZRLrDlsPFBik", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "员工类型", "name": "type", "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "c8x7pbsMO6Vavo-aho-L6", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "邮箱", "name": "email", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "Zpf0YANxm7El7x-SW7Jmg", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "手机", "name": "mobile", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "1Si5VridzaTVgOcD4G1_q", "name": "Field", "props": {"componentProps": {"fieldAlias": "addressId", "label": "选择地址", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "addrName", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "地址库名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Zr3umLrOpM4gcCOzSI_-n", "trigger": "auto", "valueRules": null}], "name": "addrName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "地址", "name": "addressId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "YhrMN_CcRC6lcEaF_J5kd", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "entryAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "入职日期", "name": "entryAt", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "CTrtwvN8uvHJFzZCIr5Al", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "resignationAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "离职日期", "name": "resignationAt", "type": "DATE", "width": 134}, "type": "Widget"}], "key": "XKG7TSBhUGT4WEFiWXRFC", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "name": "Table", "props": {"acceptFilterQuery": true, "allowClickRowSelect": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "员工编码", "name": "code", "type": "TEXT", "width": 146}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "姓名", "name": "name", "type": "TEXT", "width": 146}, {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "状态", "name": "status", "type": "SELECT", "width": 116}, {"componentProps": {"defaultValue": null, "fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "员工类型", "name": "type", "type": "SELECT", "width": 116}, {"componentProps": {"defaultValue": null, "fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "邮箱", "name": "email", "type": "TEXT", "width": 146}, {"componentProps": {"defaultValue": null, "fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "手机", "name": "mobile", "type": "TEXT", "width": 146}], "flow": {"context$": "$context", "params": [{"fieldAlias": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "required": null}, {"fieldAlias": "status", "fieldName": "status", "fieldType": "Text", "required": null}], "serviceKey": "sys_common$ORG_EMPLOYEE_PAGING_SERVICE", "type": "InvokeService"}, "label": "表格", "mode": "normal", "modelAlias": "sys_common$org_employee_md", "selectType": "multiple", "showConfigure": false, "showFilterFields": true, "showScope": "all", "showType": "normal", "tableCondition": null, "tableConditionContext$": null, "toolbar": {"search": false}}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["用户", "选择库存组织", "状态", "电话/分机号", "时区", "名称", "是否删除选中单据？", "库存组织", "公司间开票的客户编号", "停用成功", "姓名", "保存", "分配行政组织", "选择需求单位", "地址", "用户名", "ID", "创建人", "需求单位", "传真/分机号", "启用成功", "用户手机", "语言", "逻辑删除标识", "编码", "选择采购组织", "请选择", "公司间开票的供应商编号", "版本号", "NCC组织编码", "更新时间", "采购组织", "供应商库配置", "SAP组织编码", "绑定用户", "编辑", "行政组织", "选择创建人", "邮编", "维护采购组织关系", "保存成功！", "所属公司组织", "选择行政组织", "员工编码", "组织名称", "选择更新人", "手机", "分配采购组织", "表格", "操作成功！", "新建", "删除成功", "停用", "查看", "街道", "选择语言", "删除", "详细地址", "入职日期", "离职日期", "邮箱", "启用", "签名图", "用户邮箱", "组织单元", "昵称", "表格表单", "保存成功", "选择供应商库配置", "选择所属公司组织", "K3组织编码", "组织编码", "选择地址", "更新人", "角色", "选择用户", "请输入", "NC组织编码", "区号", "取消", "创建时间", "经纬度坐标", "员工类型", "选择时区", "地址库名称"], "i18nScanPaths": ["DYOMehrzeIu69NgfqW7sh.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1-button-3.props.actionConfig.endLogicOtherConfig.1.message", "CTrtwvN8uvHJFzZCIr5Al.props.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.12.label", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.4.label", "CTrtwvN8uvHJFzZCIr5Al.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.15.name", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.4.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.24.componentProps.label", "1Si5VridzaTVgOcD4G1_q.props.editComponentProps.fields.0.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.13.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.3.componentProps.placeholder", "3g7WIjt6YK9GqLewML4-h.props.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.8.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.2.componentProps.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.5.label", "kEvrXOdl4cNFNyA-_QQXH.props.label", "1Si5VridzaTVgOcD4G1_q.props.componentProps.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.0.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.1.componentProps.label", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.4.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.18.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.9.name", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.1.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.21.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.0.name", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.2.componentProps.placeholder", "vodVtVicTtCyqqCimjfAc.props.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.12.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.25.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.7.name", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.filterFields.0.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.4.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.9.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.1.componentProps.label", "c8x7pbsMO6Vavo-aho-L6.props.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.0.componentProps.placeholder", "1Si5VridzaTVgOcD4G1_q.props.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.24.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.17.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.5.label", "JfHoVRn3kXe9axflVyjMT.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.20.name", "DKIOa3Mz8vwQNmQ_GeJm_.props.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.14.componentProps.placeholder", "VWZFot32AzPGwzG9VzKVK.props.title", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.4.componentProps.placeholder", "koIH640bNBWkydT2BaUfL.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.9.label", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.0.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.5.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.13.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.11.name", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.16.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.25.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.3.componentProps.placeholder", "DKIOa3Mz8vwQNmQ_GeJm_.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1-button-1.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1-button-2.props.label", "5XoAe2Lo1fYezOx5H-zFz.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.11.label", "Zpf0YANxm7El7x-SW7Jmg.props.label", "yi_q7SfgEi2oi3RNQrPnp.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.5.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.17.name", "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1-button-3.props.label", "FVsjv2byb-fec4N65weEl.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.10.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.label", "m2gv9faQxTkLdCYm_MY6K.props.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.10.componentProps.label", "koIH640bNBWkydT2BaUfL.props.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.14.componentProps.placeholder", "-mPvo-99Lih9u3KA1GQHD.props.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.9.componentProps.placeholder", "Zpf0YANxm7El7x-SW7Jmg.props.componentProps.placeholder", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.fields.2.label", "JURX3A9BBpnk2pmjByKt7.props.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-2.props.actionConfig.endLogicOtherConfig.1.message", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.filterFields.1.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.6.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.12.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.13.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.12.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.9.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.0.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.9.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.2.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.9.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.10.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.12.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.6.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.3.name", "z2BaDFxTPLXn2lf6E307e.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.14.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.0.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.6.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.25.componentProps.placeholder", "H6d6sliVdPLSrpV5vdOWl.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-6P9ApbGq0UKj4nwK-d_R1.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.8.componentProps.placeholder", "_UEaex4hdNFnj1NJIHwPv.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.18.name", "_UEaex4hdNFnj1NJIHwPv.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.13.name", "a9NcpDjbtmajGoBNIwLoU.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.10.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.12.name", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.11.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.7.label", "BYaNImkTpZRLrDlsPFBik.props.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.2.name", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.7.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-2.props.label", "eh8xh2ly7YfSIVSw9v2J1.props.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.24.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.8.name", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.19.name", "rqaMxENImfO6LfnHXmsyx.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.componentProps.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.11.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.7.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.10.label", "6L--AWEL0JiqI-05GlUgz.props.actionConfig.endLogicOtherConfig.0.message", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.8.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.1.componentProps.placeholder", "FVsjv2byb-fec4N65weEl.props.actionConfig.endLogicOtherConfig.0.message", "cNfkCjwfPbl7GaLdeQExn.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-Sq_sLoMqfePbFpU0tjPtP.props.label", "6L--AWEL0JiqI-05GlUgz.props.label", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.fields.1.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.11.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-6P9ApbGq0UKj4nwK-d_R1.props.componentProps.placeholder", "DYOMehrzeIu69NgfqW7sh.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.22.name", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.1.componentProps.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.12.label", "40GwsQqSDxsC7t2MlCj-A.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.16.name", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.7.label", "kEvrXOdl4cNFNyA-_QQXH.props.componentProps.placeholder", "bHuMYXy9wY4y2xh9qwtRh.props.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.8.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.2.label", "l9lEXaIwdO6fqrpgichlf.props.title", "TJbM9Kajx4Ef_N8lzqyaJ.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.4.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.15.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.23.componentProps.placeholder", "BYaNImkTpZRLrDlsPFBik.props.label", "cNfkCjwfPbl7GaLdeQExn.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-Sq_sLoMqfePbFpU0tjPtP.props.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.3.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.15.componentProps.placeholder", "B6EQ1wo0NYc64Wm0GTcWF.props.componentProps.placeholder", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.fields.0.componentProps.placeholder", "@exp:COMMON_2B$COMM_2B_EMPLOYEE-list-title.props.title", "JfHoVRn3kXe9axflVyjMT.props.label", "a9NcpDjbtmajGoBNIwLoU.props.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.1.componentProps.placeholder", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.fields.3.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.14.label", "k-TVzcY45XqrBfJX_0v2D.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.1.name", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.21.name", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.2.label", "YhrMN_CcRC6lcEaF_J5kd.props.label", "9ARdxvj7sDET_gdS0YDWZ.props.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.3.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.8.componentProps.placeholder", "1Si5VridzaTVgOcD4G1_q.props.editComponentProps.fields.0.label", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.fields.3.componentProps.placeholder", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.filterFields.0.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.10.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.11.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.1.label", "k-TVzcY45XqrBfJX_0v2D.props.actionConfig.endLogicOtherConfig.0.message", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.8.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.13.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.11.componentProps.placeholder", "1Si5VridzaTVgOcD4G1_q.props.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.16.componentProps.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.5.componentProps.placeholder", "B6EQ1wo0NYc64Wm0GTcWF.props.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.22.componentProps.placeholder", "bHuMYXy9wY4y2xh9qwtRh.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.14.componentProps.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.14.name", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.13.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.4.name", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.4.componentProps.placeholder", "eh8xh2ly7YfSIVSw9v2J1.props.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.20.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.4.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.19.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.5.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.10.name", "qDJhA7D9Ru5IQTDovNyyG.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.14.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.0.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.9.componentProps.label", "bIw1CaBqQFzqPesVNHJ9x.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.5.name", "vodVtVicTtCyqqCimjfAc.props.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.14.componentProps.label", "C38OgmpPvWcoHvGrlQD04.props.title", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.16.label", "3g7WIjt6YK9GqLewML4-h.props.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.7.componentProps.placeholder", "-mPvo-99Lih9u3KA1GQHD.props.label", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.3.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.23.label", "5XoAe2Lo1fYezOx5H-zFz.props.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.6.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.3.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.3.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.2.componentProps.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.13.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.5.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.7.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.20.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3.props.fields.6.name", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.10.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.18.componentProps.label", "5XoAe2Lo1fYezOx5H-zFz.props.componentProps.label", "hPoL_sVxeg1u4PEWVQ447.props.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.2.componentProps.label", "YhrMN_CcRC6lcEaF_J5kd.props.componentProps.placeholder", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.fields.1.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.18.componentProps.placeholder", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.fields.2.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.19.componentProps.placeholder", "DKIOa3Mz8vwQNmQ_GeJm_.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.2.componentProps.placeholder", "hPoL_sVxeg1u4PEWVQ447.props.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.21.label", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-1.props.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.2.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.14.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.2.label", "H6d6sliVdPLSrpV5vdOWl.props.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-2.props.actionConfig.beforeLogicConfig.0.text", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.6.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.2.componentProps.placeholder", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.17.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.fields.5.label", "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md.props.filterFields.1.label", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.6.componentProps.placeholder", "bIw1CaBqQFzqPesVNHJ9x.props.editComponentProps.filterFields.1.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.22.label", "c8x7pbsMO6Vavo-aho-L6.props.label", "JURX3A9BBpnk2pmjByKt7.props.label", "5XoAe2Lo1fYezOx5H-zFz.props.editComponentProps.fields.1.label", "DKIOa3Mz8vwQNmQ_GeJm_.props.componentProps.label"]}, "key": "TSRM$COMM_2B_EMPLOYEE-list", "permissionKey": "TSRM$COMM_2B_EMPLOYEE-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}], "relations": [{"key": "sys_common$ORG_EMPLOYEE_PAGING_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "P8y-C3JsFmBc5wwUG_g5m", "label": "行政组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "VWZFot32AzPGwzG9VzKVK", "label": "分配行政组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "A7ijKeHWCZCd7wo3m807o", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "qDJhA7D9Ru5IQTDovNyyG", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "VWZFot32AzPGwzG9VzKVK", "label": "分配行政组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "nzXvTjbH7WiU5k3oXIzkI", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "DYOMehrzeIu69NgfqW7sh", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "VWZFot32AzPGwzG9VzKVK", "label": "分配行政组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "nzXvTjbH7WiU5k3oXIzkI", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "TSRM$ORG_EMPLOYEE_MD_ADM_SERVICE_NEW", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "vK9BIf6Z_d4pVGORjP1oy", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "l9lEXaIwdO6fqrpgichlf", "label": "维护采购组织关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "eB_G92e3c45NCsWiaKRRK", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "rqaMxENImfO6LfnHXmsyx", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "l9lEXaIwdO6fqrpgichlf", "label": "维护采购组织关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "DJnFs3AKcbZDmW2c9UIKF", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "JfHoVRn3kXe9axflVyjMT", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "l9lEXaIwdO6fqrpgichlf", "label": "维护采购组织关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "DJnFs3AKcbZDmW2c9UIKF", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "TSRM$ORG_EMPLOYEE_MD_PUR_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "9PvbyRxGyPL66MufXbIku", "label": "绑定用户", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "C38OgmpPvWcoHvGrlQD04", "label": "绑定用户", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "OP9G0GNb1XIjO0SrQ_n34", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "m2gv9faQxTkLdCYm_MY6K", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "C38OgmpPvWcoHvGrlQD04", "label": "绑定用户", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "No4i1RmGZGfYl2pzJH9AO", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "k-TVzcY45XqrBfJX_0v2D", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "C38OgmpPvWcoHvGrlQD04", "label": "绑定用户", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "No4i1RmGZGfYl2pzJH9AO", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "TSRM$ORG_EMPLOYEE_BIND_USER_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-1", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-2", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1-button-3", "label": "导出", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/trantor/struct-node/find-by-alias", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-list-TERP_MIGRATE$org_employee_md-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1-button-1", "label": "查看", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1-button-2", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "FVsjv2byb-fec4N65weEl", "label": "启用", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$ORG_EMPLOYEE_MD_ENABLE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "6L--AWEL0JiqI-05GlUgz", "label": "停用", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$ORG_EMPLOYEE_MD_DISABLE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1-button-3", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "sys_common$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "40GwsQqSDxsC7t2MlCj-A", "label": "分配行政组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "9ARdxvj7sDET_gdS0YDWZ", "label": "分配采购组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TJbM9Kajx4Ef_N8lzqyaJ", "label": "绑定用户", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "B6EQ1wo0NYc64Wm0GTcWF", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "XKG7TSBhUGT4WEFiWXRFC", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "JURX3A9BBpnk2pmjByKt7", "label": "员工编码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "XKG7TSBhUGT4WEFiWXRFC", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "-mPvo-99Lih9u3KA1GQHD", "label": "姓名", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "XKG7TSBhUGT4WEFiWXRFC", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "a9NcpDjbtmajGoBNIwLoU", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "XKG7TSBhUGT4WEFiWXRFC", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "BYaNImkTpZRLrDlsPFBik", "label": "员工类型", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "XKG7TSBhUGT4WEFiWXRFC", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "c8x7pbsMO6Vavo-aho-L6", "label": "邮箱", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "XKG7TSBhUGT4WEFiWXRFC", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "Zpf0YANxm7El7x-SW7Jmg", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "XKG7TSBhUGT4WEFiWXRFC", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "1Si5VridzaTVgOcD4G1_q", "label": "地址", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "XKG7TSBhUGT4WEFiWXRFC", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "YhrMN_CcRC6lcEaF_J5kd", "label": "入职日期", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "XKG7TSBhUGT4WEFiWXRFC", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "CTrtwvN8uvHJFzZCIr5Al", "label": "离职日期", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_EMPLOYEE-table-container-TERP_MIGRATE$org_employee_md", "label": "表格", "type": "Table"}, {"key": "XKG7TSBhUGT4WEFiWXRFC", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "cNfkCjwfPbl7GaLdeQExn", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "C38OgmpPvWcoHvGrlQD04", "label": "绑定用户", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "OP9G0GNb1XIjO0SrQ_n34", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "9PvbyRxGyPL66MufXbIku", "label": "绑定用户", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "koIH640bNBWkydT2BaUfL", "label": "员工编码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "C38OgmpPvWcoHvGrlQD04", "label": "绑定用户", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "OP9G0GNb1XIjO0SrQ_n34", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "9PvbyRxGyPL66MufXbIku", "label": "绑定用户", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "_UEaex4hdNFnj1NJIHwPv", "label": "姓名", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "C38OgmpPvWcoHvGrlQD04", "label": "绑定用户", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "OP9G0GNb1XIjO0SrQ_n34", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "9PvbyRxGyPL66MufXbIku", "label": "绑定用户", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "vodVtVicTtCyqqCimjfAc", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "C38OgmpPvWcoHvGrlQD04", "label": "绑定用户", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "OP9G0GNb1XIjO0SrQ_n34", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "9PvbyRxGyPL66MufXbIku", "label": "绑定用户", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "eh8xh2ly7YfSIVSw9v2J1", "label": "员工类型", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "C38OgmpPvWcoHvGrlQD04", "label": "绑定用户", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "OP9G0GNb1XIjO0SrQ_n34", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "9PvbyRxGyPL66MufXbIku", "label": "绑定用户", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "hPoL_sVxeg1u4PEWVQ447", "label": "手机", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "C38OgmpPvWcoHvGrlQD04", "label": "绑定用户", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "OP9G0GNb1XIjO0SrQ_n34", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "9PvbyRxGyPL66MufXbIku", "label": "绑定用户", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "DKIOa3Mz8vwQNmQ_GeJm_", "label": "用户", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "C38OgmpPvWcoHvGrlQD04", "label": "绑定用户", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "OP9G0GNb1XIjO0SrQ_n34", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "9PvbyRxGyPL66MufXbIku", "label": "绑定用户", "type": "FormGroup"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "bHuMYXy9wY4y2xh9qwtRh", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "VWZFot32AzPGwzG9VzKVK", "label": "分配行政组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "A7ijKeHWCZCd7wo3m807o", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "P8y-C3JsFmBc5wwUG_g5m", "label": "行政组织", "type": "FormGroup"}, {"key": "MULmo8eYBQ1fcfH7z4V03", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "yi_q7SfgEi2oi3RNQrPnp", "label": "表格表单", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "VWZFot32AzPGwzG9VzKVK", "label": "分配行政组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "A7ijKeHWCZCd7wo3m807o", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "P8y-C3JsFmBc5wwUG_g5m", "label": "行政组织", "type": "FormGroup"}, {"key": "MULmo8eYBQ1fcfH7z4V03", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "3g7WIjt6YK9GqLewML4-h", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "l9lEXaIwdO6fqrpgichlf", "label": "维护采购组织关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "eB_G92e3c45NCsWiaKRRK", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "vK9BIf6Z_d4pVGORjP1oy", "label": "表单组", "type": "FormGroup"}, {"key": "sySzxlhusRteQSWM8O_Sc", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "z2BaDFxTPLXn2lf6E307e", "label": "表格表单", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "l9lEXaIwdO6fqrpgichlf", "label": "维护采购组织关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "eB_G92e3c45NCsWiaKRRK", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "vK9BIf6Z_d4pVGORjP1oy", "label": "表单组", "type": "FormGroup"}, {"key": "sySzxlhusRteQSWM8O_Sc", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "H6d6sliVdPLSrpV5vdOWl", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "VWZFot32AzPGwzG9VzKVK", "label": "分配行政组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "A7ijKeHWCZCd7wo3m807o", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "P8y-C3JsFmBc5wwUG_g5m", "label": "行政组织", "type": "FormGroup"}, {"key": "MULmo8eYBQ1fcfH7z4V03", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "yi_q7SfgEi2oi3RNQrPnp", "label": "表格表单", "type": "TableForm"}, {"key": "QP5f4YFv2cFQSmODDmsxb", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "bIw1CaBqQFzqPesVNHJ9x", "label": "行政组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "VWZFot32AzPGwzG9VzKVK", "label": "分配行政组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "A7ijKeHWCZCd7wo3m807o", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "P8y-C3JsFmBc5wwUG_g5m", "label": "行政组织", "type": "FormGroup"}, {"key": "MULmo8eYBQ1fcfH7z4V03", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "yi_q7SfgEi2oi3RNQrPnp", "label": "表格表单", "type": "TableForm"}, {"key": "QP5f4YFv2cFQSmODDmsxb", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-Sq_sLoMqfePbFpU0tjPtP", "label": "组织编码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "VWZFot32AzPGwzG9VzKVK", "label": "分配行政组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "A7ijKeHWCZCd7wo3m807o", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "P8y-C3JsFmBc5wwUG_g5m", "label": "行政组织", "type": "FormGroup"}, {"key": "MULmo8eYBQ1fcfH7z4V03", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "yi_q7SfgEi2oi3RNQrPnp", "label": "表格表单", "type": "TableForm"}, {"key": "QP5f4YFv2cFQSmODDmsxb", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "kEvrXOdl4cNFNyA-_QQXH", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "l9lEXaIwdO6fqrpgichlf", "label": "维护采购组织关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "eB_G92e3c45NCsWiaKRRK", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "vK9BIf6Z_d4pVGORjP1oy", "label": "表单组", "type": "FormGroup"}, {"key": "sySzxlhusRteQSWM8O_Sc", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "z2BaDFxTPLXn2lf6E307e", "label": "表格表单", "type": "TableForm"}, {"key": "8kPRTmWK18QSF9xxesrV1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "5XoAe2Lo1fYezOx5H-zFz", "label": "采购组织", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "l9lEXaIwdO6fqrpgichlf", "label": "维护采购组织关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "eB_G92e3c45NCsWiaKRRK", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "vK9BIf6Z_d4pVGORjP1oy", "label": "表单组", "type": "FormGroup"}, {"key": "sySzxlhusRteQSWM8O_Sc", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "z2BaDFxTPLXn2lf6E307e", "label": "表格表单", "type": "TableForm"}, {"key": "8kPRTmWK18QSF9xxesrV1", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_language_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_language_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_timezone_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_timezone_type_cf"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_vend_pur_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_vend_pur_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_EMPLOYEE-6P9ApbGq0UKj4nwK-d_R1", "label": "组织编码", "path": [{"key": "COMMON_2B$COMM_2B_EMPLOYEE-list", "label": "页面", "type": "Page"}, {"key": "l9lEXaIwdO6fqrpgichlf", "label": "维护采购组织关系", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "eB_G92e3c45NCsWiaKRRK", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "vK9BIf6Z_d4pVGORjP1oy", "label": "表单组", "type": "FormGroup"}, {"key": "sySzxlhusRteQSWM8O_Sc", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "z2BaDFxTPLXn2lf6E307e", "label": "表格表单", "type": "TableForm"}, {"key": "8kPRTmWK18QSF9xxesrV1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}