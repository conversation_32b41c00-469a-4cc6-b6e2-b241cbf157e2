{"access": "Private", "key": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE", "name": "ORG_员工管理_数据保存", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5077qv1", "name": "开始", "nextNodeKey": "node_1hb5i96fg28", "preNodeKey": null, "props": {"desc": null, "globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "checkCodeModel", "fieldKey": "checkCodeModel", "fieldName": "checkCodeModel", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "checkPhoneModel", "fieldKey": "checkPhoneModel", "fieldName": "checkPhoneModel", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5i96fg29", "name": "条件", "nextNodeKey": "node_1hb5iaj3e32", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "EA4PnUyzVL33VTRWsZ6NC", "key": "EA4PnUyzVL33VTRWsZ6NC", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5iaj3e33", "name": "条件", "nextNodeKey": "node_1hb569ifr4", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "1Xfz0qBY-FvaWod5fbdcJ", "key": "1Xfz0qBY-FvaWod5fbdcJ", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "员工编码"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}, {"id": "2Mc35DYJYANcwNaJpROOF", "key": "2Mc35DYJYANcwNaJpROOF", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb569ifr4", "name": "查询数据", "nextNodeKey": "node_1hb5ijaqt35", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "Zjypi81fUVEMTNDonXfWV", "key": "Zjypi81fUVEMTNDonXfWV", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "NEQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "gJSyJLFnasvJ915gP6ZWu", "key": "gJSyJLFnasvJ915gP6ZWu", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "code", "valueName": "员工编码"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "员工编码"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkCodeModel", "valueName": "checkCodeModel"}]}, "id": "1hb5i54cn26", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hb569ifr4", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5ijaqt36", "name": "条件", "nextNodeKey": "node_1hb5j0c9439", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "1i54SJVvUXsN6ztUHHYFC", "key": "1i54SJVvUXsN6ztUHHYFC", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkCodeModel", "valueName": "checkCodeModel"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5j0c9439", "name": "查询数据", "nextNodeKey": "node_1hb5j2fij41", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "y2Ts-YoY16hwxBSTzBAZc", "key": "y2Ts-YoY16hwxBSTzBAZc", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "NEQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "0o13_cDYZ3aM6olD594rR", "key": "0o13_cDYZ3aM6olD594rR", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkPhoneModel", "valueName": "checkPhoneModel"}]}, "id": "1hb5j1t7s40", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hb5j0c9439", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5j2fij42", "name": "条件", "nextNodeKey": null, "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "JYNkT3h_BD4CeOozS-hem", "key": "JYNkT3h_BD4CeOozS-hem", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkPhoneModel", "valueName": "checkPhoneModel"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5j2fij43", "name": "条件", "nextNodeKey": "node_1hb5j3vqd44", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "e1_8VPurY6AsDuWlVW1RX", "key": "e1_8VPurY6AsDuWlVW1RX", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkPhoneModel", "valueName": "checkPhoneModel"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5j3vqd44", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "手机号重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hb5j2fij42", "node_1hb5j2fij43"], "id": null, "key": "node_1hb5j2fij41", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5ijaqt37", "name": "条件", "nextNodeKey": "node_1hb5isvd038", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "OTpDwuikaQ6X9ItKDY_Gf", "key": "OTpDwuikaQ6X9ItKDY_Gf", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkCodeModel", "valueName": "checkCodeModel"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5isvd038", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "员工编码重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hb5ijaqt36", "node_1hb5ijaqt37"], "id": null, "key": "node_1hb5ijaqt35", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5iaj3e34", "name": "条件", "nextNodeKey": null, "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "6ceC07bC50lyY0zNy2su2", "key": "6ceC07bC50lyY0zNy2su2", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "员工编码"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}, {"id": "JVWDIbfwgcnvHFXqWMM7c", "key": "JVWDIbfwgcnvHFXqWMM7c", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}], "headNodeKeys": ["node_1hb5iaj3e33", "node_1hb5iaj3e34"], "id": null, "key": "node_1hb5iaj3e32", "name": "排他分支", "nextNodeKey": "node_1hb5j78no45", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5j78no45", "name": "更新数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5i96fg30", "name": "条件", "nextNodeKey": "node_1hb5j7smo46", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "JPuT2Z1vcKqzUrSGOwMA6", "key": "JPuT2Z1vcKqzUrSGOwMA6", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5j7smo47", "name": "条件", "nextNodeKey": "node_1hb5ja14949", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "1Apd9Jd1CIczfZn4XgKbZ", "key": "1Apd9Jd1CIczfZn4XgKbZ", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "员工编码"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}, {"id": "Ie_50PsDF7pIRt9mVZVfA", "key": "Ie_50PsDF7pIRt9mVZVfA", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5ja14949", "name": "查询数据", "nextNodeKey": "node_1hb5jb91f51", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "XmXMZD7LmtluOKqfhf7Yo", "key": "XmXMZD7LmtluOKqfhf7Yo", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "code", "valueName": "员工编码"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "员工编码"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkCodeModel", "valueName": "checkCodeModel"}]}, "id": "1hb5jap4n50", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hb5ja14949", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5jb91f52", "name": "条件", "nextNodeKey": "node_1hb5jddqd55", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "8dD9vlE8MrTYyVVEngVjA", "key": "8dD9vlE8MrTYyVVEngVjA", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkCodeModel", "valueName": "checkCodeModel"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5jddqd55", "name": "查询数据", "nextNodeKey": "node_1hb5jf6hu57", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "NjJbHYJs68ntH7B7lVi8j", "key": "NjJbHYJs68ntH7B7lVi8j", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "sys_common$org_employee_md", "relatedModel": null, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkPhoneModel", "valueName": "checkPhoneModel"}]}, "id": "1hb5je5dr56", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "valueKey": "NODE_OUTPUT_node_1hb5jddqd55", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5jf6hu58", "name": "条件", "nextNodeKey": null, "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "ft9FlfL4Ww4r7hni8JdnI", "key": "ft9FlfL4Ww4r7hni8JdnI", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkPhoneModel", "valueName": "checkPhoneModel"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5jf6hu59", "name": "条件", "nextNodeKey": "node_1hb5jghlc60", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "n8CrfDDWvWTx271dK4WCw", "key": "n8CrfDDWvWTx271dK4WCw", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkPhoneModel", "valueName": "checkPhoneModel"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5jghlc60", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "手机号重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hb5jf6hu58", "node_1hb5jf6hu59"], "id": null, "key": "node_1hb5jf6hu57", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5jb91f53", "name": "条件", "nextNodeKey": "node_1hb5jcr1j54", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "pOJU-_vZkVljBx5YrSPIm", "key": "pOJU-_vZkVljBx5YrSPIm", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "checkCodeModel", "valueName": "checkCodeModel"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5jcr1j54", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "员工编码重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}], "headNodeKeys": ["node_1hb5jb91f52", "node_1hb5jb91f53"], "id": null, "key": "node_1hb5jb91f51", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5j7smp48", "name": "条件", "nextNodeKey": null, "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "VFHZOwAuNk5bUyuw-Ay0Y", "key": "VFHZOwAuNk5bUyuw-Ay0Y", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "code", "valueName": "员工编码"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}, {"id": "Ybr3YGMR6n2qr-i6K3Yxt", "key": "Ybr3YGMR6n2qr-i6K3Yxt", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "mobile", "valueName": "手机"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}], "headNodeKeys": ["node_1hb5j7smo47", "node_1hb5j7smp48"], "id": null, "key": "node_1hb5j7smo46", "name": "排他分支", "nextNodeKey": "node_1hb5jh79461", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5jh79461", "name": "新增数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "valueKey": "request", "valueName": "request"}]}, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t"}, "type": "CascadeCreateDataProperties"}, "renderType": null, "type": "CascadeCreateDataNode"}], "headNodeKeys": ["node_1hb5i96fg29", "node_1hb5i96fg30"], "id": null, "key": "node_1hb5i96fg28", "name": "排他分支", "nextNodeKey": "node_1hb5077qv2", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb5077qv2", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hb5077qv1"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$ORG_EMPLOYEE_MD_ADM_SAVE", "name": "ORG_员工管理_数据保存", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$ORG_EMPLOYEE_MD_ADM_SAVE_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}