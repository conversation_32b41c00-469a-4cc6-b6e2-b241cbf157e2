{"access": "Private", "key": "TSRM$WQ_ORG_USER_ADD_ACTION", "name": "员工管理-新增action", "props": {"bean": "ExtWqOrgEmployeeMdAction", "desc": null, "groovyScript": null, "languageType": "Java", "method": "addUser", "order": 3, "requestType": "io.terminus.tsrm.md.spi.model.org.dto.UserDTO", "responseType": "io.terminus.iam.api.response.user.User", "returnModel": null, "status": "enabled"}, "type": "Action"}