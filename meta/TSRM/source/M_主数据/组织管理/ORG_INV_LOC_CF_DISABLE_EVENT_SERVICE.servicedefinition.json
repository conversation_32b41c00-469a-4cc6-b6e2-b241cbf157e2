{"access": "Private", "description": "{}", "key": "TSRM$ORG_INV_LOC_CF_DISABLE_EVENT_SERVICE", "name": "ORG_库存地点_停用服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "TERP_MIGRATE$org_inv_loc_cf", "name": "库存地点配置表"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$ORG_INV_LOC_CF_DISABLE_EVENT_perm_ac", "relations": null, "returnModel": null, "returnModelArrayWhether": false, "states": [{"appId": 66008, "conditions": null, "fieldKey": "extWqStatus", "modelKey": "TERP_MIGRATE$org_inv_loc_cf", "module": "TERP_MIGRATE$58e364f2-2f7d-45d7-8046-be47f8799907", "moduleName": "组织", "sourceState": "ENABLED", "targetState": "DISABLED", "teamId": 35}]}, "isDeleted": null, "isEnabled": true, "modelKey": "TERP_MIGRATE$org_inv_loc_cf", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_86d4e7fa68", "name": "开始", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "modelKey": "TERP_MIGRATE$org_inv_loc_cf", "modelName": "库存地点配置表"}}], "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_a559c19fb7", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_inv_loc_cf", "modelKey": "TERP_MIGRATE$org_inv_loc_cf", "modelName": "库存地点配置表"}}], "key": "TSRM$ORG_INV_LOC_CF_DISABLE_EVENT_SERVICE", "name": "ORG_库存地点_停用服务", "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$ORG_INV_LOC_CF_DISABLE_EVENT_perm_ac", "schedulerJob": null, "stateMachine": {"stateVerify": false, "states": [{"conditionGroup": null, "fieldKey": "extWqStatus", "modelKey": "TERP_MIGRATE$org_inv_loc_cf", "sourceState": "ENABLED", "targetState": "DISABLED"}]}, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}