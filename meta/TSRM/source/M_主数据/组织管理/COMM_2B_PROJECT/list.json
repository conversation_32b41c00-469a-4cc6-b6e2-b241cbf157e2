{"access": "Private", "key": "TSRM$COMM_2B_PROJECT:list", "name": "list", "props": {"containerSelect": {"COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail": [], "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form": [], "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf": []}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "permissionKey": "TSRM$COMM_2B_PROJECT-list_perm_ac_z_0_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_obj_org_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "TERP_MIGRATE$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "COMMON_2B$COMM_2B_PROJECT-TERP_MIGRATE$org_obj_org_cf-multi-delete", "label": "批量删除", "permissionKey": "TSRM$COMM_2B_PROJECT-list_perm_ac_z_0_0_0_1_items_0"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "odZnxMpe_0NDoc1UMMklC", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "M94PYvQVUKJqxOX6rHTjS", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "组织名称", "name": "name", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "E3q9tXNcP3yJ2fiogZfPb", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "组织编码", "name": "code", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "8ChY6PkjEyw_nWGVVksrw", "name": "Field", "props": {"componentProps": {"fieldAlias": "invOrg", "label": "选择库存组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "YFMuXuE0gn0rP8A9ePL_X", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "需求单位", "name": "invOrg", "type": "OBJECT", "width": 168}, "type": "Widget"}], "key": "FBemoqXTJl4a0_HgOSgf6", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "hidden": false, "label": "项目名称", "name": "name", "type": "TEXT", "width": 146}, {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "hidden": false, "label": "项目编码", "name": "code", "type": "TEXT", "width": 146}], "flow": {"containerKey": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_obj_org_cf"}}], "serviceKey": "TERP_MIGRATE$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "表格", "mode": "simple", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showFilterFields": true, "showScope": "all", "tableCondition": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "WuLAMShEUxm4GN6GsO-v6", "name": "Status", "props": {"text$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "type$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.type"}}], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"项目组织详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_obj_org_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TERP_MIGRATE$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_PROJECT-list_perm_ac_z_0_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_PROJECT-list_perm_ac_z_0_1_1_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_obj_org_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TERP_MIGRATE$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PROJECT-detailView-view"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$COMM_2B_PROJECT-list_perm_ac_z_0_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_obj_org_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TERP_MIGRATE$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_PROJECT-detailView-view"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$COMM_2B_PROJECT-list_perm_ac_z_0_1_1_0_1_0_3", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "IS_NOT_NULL", "rightValue": null}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_PROJECT-list_perm_ac_z_0_1_1_0_1_0_4", "showCondition": {"conditions": [], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detail-TERP_MIGRATE$org_obj_org_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-code", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "项目编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-name", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "项目名称", "name": "name", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-invOrg", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "invOrg", "label": "选择库存组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "需求单位", "name": "invOrg", "type": "OBJECT"}}, {"children": [], "key": "Y3tLnyYkVaGQyNfMYxIWv", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "extWqContractor", "label": "选择施工单位", "labelField": "vendCode", "modelAlias": "TB2B$md_vend_info", "parentModelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "name", "modelAlias": "ERP_GEN$gen_vend_info_md"}, "displayComponentType": "RelationShow", "editable": false, "initialValue": null, "label": "施工单位", "name": "extWqContractor", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Dss5OMMNzMxVEBGWhnHPo", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "extWqContract", "label": "选择施工合同", "labelField": "ctCode", "modelAlias": "ERP_GEN$gen_ct_head_tr", "parentModelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_ct_head_tr", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "initialValue": null, "label": "施工合同", "name": "extWqContract", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-status", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "状态", "name": "status", "type": "SELECT"}}], "key": "ufgYrGFctj4TybePvxoyH", "name": "DetailGroupItem", "props": {"title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "XXitsWryE7gXEYiA1NaD7", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqNccCode", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "NCC组织编码", "name": "extWqNccCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "SeFd4-Ebt_YqN0I3I7j_C", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqNcCode", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "NC组织编码", "name": "extWqNcCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "rgXeOGsjZ4js4zwGh8e0m", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqSapCode", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "SAP组织编码", "name": "extWqSapCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "nfoJ9GrOOl9L09EbimiN4", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqK3Code", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "K3组织编码", "name": "extWqK3Code", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "m8Q3EeI5PT2506NcwWyRY", "name": "DetailGroupItem", "props": {"title": "ERP组织编码"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_PROJECT-UnUj3dzOdSyvevA-qnXe7", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "serviceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"项目组织\")) : \"新建项目组织\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-code", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "组织编码", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "4ExhgCCNdo5gyHujXCGSa", "operator": null, "valueRules": null}], "name": "code", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-name", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "组织名称", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "g4rQzHASdeH96N_xORpKA", "operator": null, "valueRules": null}], "name": "name", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg", "name": "FormField", "props": {"componentProps": {"fieldAlias": "invOrg", "label": "选择库存组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "name", "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId"], "fieldAlias": "comOrgId", "label": "选择所属公司组织", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "所属板块", "name": "comOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "mainField": "name", "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "所属需求公司", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "-MZNus-M1AvxU7zvZTlh1", "operator": null, "valueRules": null}], "name": "invOrg", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "g0s3MPQ9P5xu62GYnOEtP", "name": "FormField", "props": {"componentProps": {"fieldAlias": "extWqContractor", "label": "选择施工单位", "labelField": "vendCode", "modelAlias": "TB2B$md_vend_info", "parentModelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "ERP_GEN$gen_vend_info_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "vendCode", "modelAlias": "ERP_GEN$gen_vend_info_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "供应商编码", "name": "vendCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_vend_info_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "名称", "name": "name", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "vendCode", "modelAlias": "ERP_GEN$gen_vend_info_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "供应商编码", "name": "vendCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_GEN$gen_vend_info_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "名称", "name": "name", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_vend_info_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_vend_info_md"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "mainField": "name", "modelAlias": "ERP_GEN$gen_vend_info_md", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "label": "施工单位", "name": "extWqContractor", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "fB9fbOkOqIe7o4bWTuGv8", "name": "FormField", "props": {"componentProps": {"fieldAlias": "extWqContract", "label": "选择施工合同", "labelField": "ctCode", "modelAlias": "ERP_GEN$gen_ct_head_tr", "parentModelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_ct_head_tr", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_ct_head_tr"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "ctCode", "modelAlias": "ERP_GEN$gen_ct_head_tr", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "合同编号", "name": "ctCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "ctName", "modelAlias": "ERP_GEN$gen_ct_head_tr", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "合同名称", "name": "ctName", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "status", "ver", "isLatestVersion", "countryId", "timezoneId", "postcode", "coordinate", "area", "phone", "bankaccount", "bankaccountCode", "bankName", "comCorporation", "openDate", "closeDate", "enterpriseType", "assuranceCnt", "old<PERSON>ame", "socialcreditCode", "addressId", "addressDetail", "bizScope", "bizStatus", "registeredCapital", "paidinCapital", "isUnifiedCode", "bizLicenseNo", "bizLicenseScan", "orgCode", "foundDate", "taxpayersNum", "taxpayersType", "bizSector", "registrationAuthority", "nameInEn", "businessRegistrationNo", "issueDate", "staffSize", "officalWebsite", "mail", "intro", "extWqCapitalUnit", "extWqIsSignature"], "fieldAlias": "prtnA", "label": "选择合同签约甲方", "labelField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "parentModelAlias": "ERP_GEN$gen_ct_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "合同签约甲方", "name": "prtnA", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["vendCode", "name", "vendNameShort", "<PERSON><PERSON><PERSON>", "comId", "personId", "vendSource", "status", "isCust", "custId", "remark", "vendType", "invOrgId", "purComId", "approveStatus", "vendLevel", "extCreditLimit", "investigationResult", "extUpstreamSupplier", "extVendNature", "extVendAbbr"], "fieldAlias": "prtnB", "label": "选择合同签约乙方", "labelField": "vendCode", "modelAlias": "ERP_GEN$gen_vend_info_md", "parentModelAlias": "ERP_GEN$gen_ct_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "合同签约乙方", "name": "prtnB", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "ctDateSign", "modelAlias": "ERP_GEN$gen_ct_head_tr", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "签约日期", "name": "ctDateSign", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "ctDateFrom", "modelAlias": "ERP_GEN$gen_ct_head_tr", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "生效日期", "name": "ctDateFrom", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "ctDateTo", "modelAlias": "ERP_GEN$gen_ct_head_tr", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "终止日期", "name": "ctDateTo", "required": false, "type": "DATE", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "ctCode", "modelAlias": "ERP_GEN$gen_ct_head_tr", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "合同编号", "name": "ctCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "ctName", "modelAlias": "ERP_GEN$gen_ct_head_tr", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "合同名称", "name": "ctName", "required": true, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_ct_head_tr", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_ct_head_tr", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_ct_head_tr"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "ctCode", "mainField": "ctCode", "modelAlias": "ERP_GEN$gen_ct_head_tr", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "label": "施工合同", "name": "extWqContract", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "l1mIQlpfuWxNqgNX2I0-W", "name": "FormField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "initialValue": "INACTIVE", "label": "状态", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "ytgeuUqg1trSB76-AqWG3", "valueRules": null}], "name": "status", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}], "key": "2CLQ-eAYzLeRgXPZ4fMoC", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "p5ojCnr1ra2u51VChKJ1w", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqNccCode", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "label": "NCC组织编码", "name": "extWqNccCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "S0upl5n1awZK9HQ3LeaNg", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqNcCode", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "label": "NC组织编码", "name": "extWqNcCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "RkTMGEIVxrAy1P5qcN7ll", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqSapCode", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "label": "SAP组织编码", "name": "extWqSapCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Va7XNJjMhaxOSdtUdzOPo", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqK3Code", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "label": "K3组织编码", "name": "extWqK3Code", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "pwJ5Hku1bzDF-vTdHoqk3", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "ERP组织编码"}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "params$": "{ id: route.recordId }", "serviceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "modelAlias": "TERP_MIGRATE$org_obj_org_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "TERP_MIGRATE$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_PROJECT-list_perm_ac_z_0_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_obj_org_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form"}, "type": "action"}}], "service": "TERP_MIGRATE$SYS_MasterData_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_PROJECT-list_perm_ac_z_0_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_PROJECT-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_PROJECT-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_PROJECT-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_PROJECT-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "项目管理"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["选择库存组织", "状态", "名称", "停用成功", "保存", "合同签约甲方", "请输入ID", "ID", "创建人", "项目编码", "保存成功!", "需求单位", "启用成功", "请选择", "NCC组织编码", "基本信息", "更新时间", "SAP组织编码", "编辑", "终止日期", "选择创建人", "选择合同签约甲方", "确认删除吗？", "项目名称", "组织名称", "复制", "选择更新人", "表格", "系统信息", "项目组织", "新建", "删除成功", "停用", "所属板块", "签约日期", "施工单位", "批量删除", "删除", "合同签约乙方", "选择施工单位", "启用", "批量操作", "合同编号", "选择所属公司组织", "ERP组织编码", "项目组织详情", "K3组织编码", "组织编码", "主体信息", "生效日期", "确认启用吗？", "施工合同", "选择合同签约乙方", "所属需求公司", "新建项目组织", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "供应商编码", "更新人", "请输入", "NC组织编码", "取消", "创建时间", "确认停用吗？", "选择施工合同", "合同名称"], "i18nScanPaths": ["COMMON_2B$COMM_2B_PROJECT-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.componentProps.label", "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-new.props.label", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.filterFields.0.label", "ufgYrGFctj4TybePvxoyH.props.title", "COMMON_2B$COMM_2B_PROJECT-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.5.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-name.props.componentProps.placeholder", "M94PYvQVUKJqxOX6rHTjS.props.label", "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-name.props.label", "Dss5OMMNzMxVEBGWhnHPo.props.label", "S0upl5n1awZK9HQ3LeaNg.props.label", "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-updatedAt.props.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.fields.1.componentProps.placeholder", "g0s3MPQ9P5xu62GYnOEtP.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.fields.0.label", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "8ChY6PkjEyw_nWGVVksrw.props.componentProps.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-updatedBy.props.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.fields.2.componentProps.label", "RkTMGEIVxrAy1P5qcN7ll.props.label", "g0s3MPQ9P5xu62GYnOEtP.props.label", "g0s3MPQ9P5xu62GYnOEtP.props.componentProps.placeholder", "g0s3MPQ9P5xu62GYnOEtP.props.editComponentProps.filterFields.1.label", "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-createdBy.props.label", "S0upl5n1awZK9HQ3LeaNg.props.componentProps.placeholder", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.0.componentProps.placeholder", "Dss5OMMNzMxVEBGWhnHPo.props.componentProps.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.filterFields.1.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.fields.3.componentProps.placeholder", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf.props.filterFields.1.label", "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-code.props.componentProps.placeholder", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.filterFields.1.componentProps.placeholder", "p5ojCnr1ra2u51VChKJ1w.props.componentProps.placeholder", "g0s3MPQ9P5xu62GYnOEtP.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf.props.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.fields.2.label", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf.props.label", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.3.componentProps.label", "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-updatedBy.props.componentProps.label", "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-batch.props.items.0.actionConfig.endLogicOtherConfig.1.message", "8ChY6PkjEyw_nWGVVksrw.props.editComponentProps.fields.0.componentProps.placeholder", "XXitsWryE7gXEYiA1NaD7.props.componentProps.placeholder", "g0s3MPQ9P5xu62GYnOEtP.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_PROJECT-detailView-actions-edit.props.label", "RkTMGEIVxrAy1P5qcN7ll.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf.props.filterFields.0.label", "8ChY6PkjEyw_nWGVVksrw.props.componentProps.placeholder", "g0s3MPQ9P5xu62GYnOEtP.props.editComponentProps.fields.0.componentProps.placeholder", "M94PYvQVUKJqxOX6rHTjS.props.componentProps.placeholder", "Va7XNJjMhaxOSdtUdzOPo.props.label", "odZnxMpe_0NDoc1UMMklC.props.componentProps.placeholder", "E3q9tXNcP3yJ2fiogZfPb.props.componentProps.placeholder", "Va7XNJjMhaxOSdtUdzOPo.props.componentProps.placeholder", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.4.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-id.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-actions-enable.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-status.props.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.filterFields.1.componentProps.placeholder", "Y3tLnyYkVaGQyNfMYxIWv.props.label", "rgXeOGsjZ4js4zwGh8e0m.props.label", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.3.label", "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-actions-enable.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-id.props.rules.0.message", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-code.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-batch.props.label", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.fields.0.componentProps.placeholder", "p5ojCnr1ra2u51VChKJ1w.props.label", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.6.label", "COMMON_2B$COMM_2B_PROJECT-editView-action-cancel.props.label", "COMMON_2B$COMM_2B_PROJECT-detailView-actions-disable.props.actionConfig.beforeLogicConfig.0.text", "m8Q3EeI5PT2506NcwWyRY.props.title", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.6.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-code.props.label", "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_PROJECT-detailView-actions-copy.props.label", "SeFd4-Ebt_YqN0I3I7j_C.props.label", "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-createdAt.props.label", "nfoJ9GrOOl9L09EbimiN4.props.label", "@exp:COMMON_2B$COMM_2B_PROJECT-editView-page-title.props.title", "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "fB9fbOkOqIe7o4bWTuGv8.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-id.props.label", "COMMON_2B$COMM_2B_PROJECT-editView-action-save.props.label", "g0s3MPQ9P5xu62GYnOEtP.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-actions-delete.props.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-name.props.label", "@exp:COMMON_2B$COMM_2B_PROJECT-detailView-page-title.props.title", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.filterFields.1.label", "Y3tLnyYkVaGQyNfMYxIWv.props.componentProps.placeholder", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.2.componentProps.label", "E3q9tXNcP3yJ2fiogZfPb.props.label", "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf.props.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "g0s3MPQ9P5xu62GYnOEtP.props.editComponentProps.fields.1.label", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.1.componentProps.placeholder", "pwJ5Hku1bzDF-vTdHoqk3.props.title", "fB9fbOkOqIe7o4bWTuGv8.props.componentProps.label", "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-status.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-actions-disable.props.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-name.props.componentProps.placeholder", "odZnxMpe_0NDoc1UMMklC.props.label", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.5.label", "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-createdBy.props.componentProps.label", "8ChY6PkjEyw_nWGVVksrw.props.label", "nfoJ9GrOOl9L09EbimiN4.props.componentProps.placeholder", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs.props.items.1.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.editComponentProps.fields.3.label", "COMMON_2B$COMM_2B_PROJECT-detailView-actions-disable.props.actionConfig.endLogicOtherConfig.1.message", "Dss5OMMNzMxVEBGWhnHPo.props.componentProps.placeholder", "fB9fbOkOqIe7o4bWTuGv8.props.label", "8ChY6PkjEyw_nWGVVksrw.props.editComponentProps.fields.0.label", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.filterFields.0.componentProps.placeholder", "l1mIQlpfuWxNqgNX2I0-W.props.label", "g0s3MPQ9P5xu62GYnOEtP.props.editComponentProps.filterFields.0.componentProps.placeholder", "XXitsWryE7gXEYiA1NaD7.props.label", "COMMON_2B$COMM_2B_PROJECT-detailView-actions-enable.props.label", "l1mIQlpfuWxNqgNX2I0-W.props.componentProps.placeholder", "WuLAMShEUxm4GN6GsO-v6.props.text$", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-code.props.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.componentProps.placeholder", "fB9fbOkOqIe7o4bWTuGv8.props.editComponentProps.fields.4.label", "Y3tLnyYkVaGQyNfMYxIWv.props.componentProps.label", "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-batch.props.items.0.label", "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "rgXeOGsjZ4js4zwGh8e0m.props.componentProps.placeholder", "2CLQ-eAYzLeRgXPZ4fMoC.props.title", "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg.props.componentProps.label", "SeFd4-Ebt_YqN0I3I7j_C.props.componentProps.placeholder", "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "g0s3MPQ9P5xu62GYnOEtP.props.componentProps.label"]}, "key": "TSRM$COMM_2B_PROJECT-list", "permissionKey": "TSRM$COMM_2B_PROJECT-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-new", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-batch/items/COMMON_2B$COMM_2B_PROJECT-TERP_MIGRATE$org_obj_org_cf-multi-delete", "label": "批量删除", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "TERP_MIGRATE$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "odZnxMpe_0NDoc1UMMklC", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf", "label": "表格", "type": "Table"}, {"key": "FBemoqXTJl4a0_HgOSgf6", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "M94PYvQVUKJqxOX6rHTjS", "label": "组织名称", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf", "label": "表格", "type": "Table"}, {"key": "FBemoqXTJl4a0_HgOSgf6", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "E3q9tXNcP3yJ2fiogZfPb", "label": "组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf", "label": "表格", "type": "Table"}, {"key": "FBemoqXTJl4a0_HgOSgf6", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "8ChY6PkjEyw_nWGVVksrw", "label": "需求单位", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-list-TERP_MIGRATE$org_obj_org_cf", "label": "表格", "type": "Table"}, {"key": "FBemoqXTJl4a0_HgOSgf6", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_MasterData_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions-disable", "label": "停用", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_MasterData_DisableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions-enable", "label": "启用", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TERP_MIGRATE$SYS_MasterData_EnableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_obj_org_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detail-TERP_MIGRATE$org_obj_org_cf-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-UnUj3dzOdSyvevA-qnXe7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-UnUj3dzOdSyvevA-qnXe7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-UnUj3dzOdSyvevA-qnXe7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-UnUj3dzOdSyvevA-qnXe7", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-code", "label": "组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "2CLQ-eAYzLeRgXPZ4fMoC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-name", "label": "组织名称", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "2CLQ-eAYzLeRgXPZ4fMoC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-invOrg", "label": "所属需求公司", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "2CLQ-eAYzLeRgXPZ4fMoC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "g0s3MPQ9P5xu62GYnOEtP", "label": "施工单位", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "2CLQ-eAYzLeRgXPZ4fMoC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$md_vend_info"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$md_vend_info"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_vend_info_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "fB9fbOkOqIe7o4bWTuGv8", "label": "施工合同", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "2CLQ-eAYzLeRgXPZ4fMoC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_ct_head_tr"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_vend_info_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_vend_info_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_ct_head_tr"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "l1mIQlpfuWxNqgNX2I0-W", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "2CLQ-eAYzLeRgXPZ4fMoC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-field-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "2CLQ-eAYzLeRgXPZ4fMoC", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "p5ojCnr1ra2u51VChKJ1w", "label": "NCC组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "pwJ5Hku1bzDF-vTdHoqk3", "label": "ERP组织编码", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "S0upl5n1awZK9HQ3LeaNg", "label": "NC组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "pwJ5Hku1bzDF-vTdHoqk3", "label": "ERP组织编码", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "RkTMGEIVxrAy1P5qcN7ll", "label": "SAP组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "pwJ5Hku1bzDF-vTdHoqk3", "label": "ERP组织编码", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "Va7XNJjMhaxOSdtUdzOPo", "label": "K3组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-T-QqCcFk44L9luOnJefom", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-editView-TERP_MIGRATE$org_obj_org_cf-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}, {"key": "pwJ5Hku1bzDF-vTdHoqk3", "label": "ERP组织编码", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-code", "label": "项目编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "ufgYrGFctj4TybePvxoyH", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-name", "label": "项目名称", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "ufgYrGFctj4TybePvxoyH", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-invOrg", "label": "需求单位", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "ufgYrGFctj4TybePvxoyH", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "Y3tLnyYkVaGQyNfMYxIWv", "label": "施工单位", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "ufgYrGFctj4TybePvxoyH", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$md_vend_info"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$md_vend_info"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "Dss5OMMNzMxVEBGWhnHPo", "label": "施工合同", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "ufgYrGFctj4TybePvxoyH", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_ct_head_tr"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-field-status", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "ufgYrGFctj4TybePvxoyH", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "XXitsWryE7gXEYiA1NaD7", "label": "NCC组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "m8Q3EeI5PT2506NcwWyRY", "label": "ERP组织编码", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "SeFd4-Ebt_YqN0I3I7j_C", "label": "NC组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "m8Q3EeI5PT2506NcwWyRY", "label": "ERP组织编码", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "rgXeOGsjZ4js4zwGh8e0m", "label": "SAP组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "m8Q3EeI5PT2506NcwWyRY", "label": "ERP组织编码", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "nfoJ9GrOOl9L09EbimiN4", "label": "K3组织编码", "path": [{"key": "COMMON_2B$COMM_2B_PROJECT-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_PROJECT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_PROJECT-Wav-YUf6MQY5uCnj4Blb3", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_PROJECT-detailView-TERP_MIGRATE$org_obj_org_cf-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}, {"key": "m8Q3EeI5PT2506NcwWyRY", "label": "ERP组织编码", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}