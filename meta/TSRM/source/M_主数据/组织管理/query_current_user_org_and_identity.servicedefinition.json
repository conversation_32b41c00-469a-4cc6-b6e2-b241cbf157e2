{"access": "Private", "description": "{}", "key": "TSRM$query_current_user_org_and_identity", "name": "ORG_根据当前用户查询用户所在的主组织及身份", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1ifedu3ql4", "name": "开始", "nextNodeKey": "node_1ifedu72t6", "props": {"globalVariable": null, "input": null, "output": [{"elements": [{"element": {"elements": [{"fieldAlias": "orgName", "fieldKey": "orgName", "fieldName": "orgName", "fieldType": "Text", "id": null}, {"fieldAlias": "orgCode", "fieldKey": "orgCode", "fieldName": "orgCode", "fieldType": "Text", "id": null}, {"fieldAlias": "identityName", "fieldKey": "identityName", "fieldName": "identityName", "fieldType": "Text", "id": null}, {"fieldAlias": "orgId", "fieldKey": "orgId", "fieldName": "orgId", "fieldType": "Number", "id": null}, {"fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "id": null}], "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null}, "fieldAlias": "orgList", "fieldKey": "orgList", "fieldName": "orgList", "fieldType": "Array", "id": null}, {"fieldAlias": "identityName", "fieldKey": "identityName", "fieldName": "identityName", "fieldType": "Text", "id": null}, {"elements": [{"fieldAlias": "orgId", "fieldKey": "orgId", "fieldName": "orgId", "fieldType": "Number", "id": null}, {"fieldAlias": "orgCode", "fieldKey": "orgCode", "fieldName": "orgCode", "fieldType": "Text", "id": null}, {"fieldAlias": "orgName", "fieldKey": "orgName", "fieldName": "orgName", "fieldType": "Text", "id": null}, {"fieldAlias": "identityName", "fieldKey": "identityName", "fieldName": "identityName", "fieldType": "Text", "id": null}, {"fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "id": null}], "fieldAlias": "org", "fieldKey": "org", "fieldName": "org", "fieldType": "Object", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1ifedu72t6", "name": "查询数据", "nextNodeKey": "node_1ifee308t7", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "GToL8h8dWBFcQTFFME9jg", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": "sys_common$user", "modelKey": "sys_common$user", "modelName": "sys_common$user"}, "valueKey": "userId", "valueName": "用户"}, {"fieldType": null, "modelAlias": "sys_common$user", "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "SYS", "valueName": "系统变量"}, {"fieldType": null, "valueKey": "CurrentUserId", "valueName": "当前登录人id"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "sjwo3i4E4gIQ1Kq7IH6-N", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "r2DJhik8CvvKZvg2PoF51", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": null, "dataType": "MODEL", "desensitized": true, "dynamicCondition": null, "maximum": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_employee_md", "queryFields": [{"fieldKey": "code"}, {"fieldKey": "name"}, {"fieldKey": "status"}, {"fieldKey": "employeeOrgLinkList", "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_employee_org_link_cf", "queryFields": [{"fieldKey": "isMainOrg"}, {"fieldKey": "identityId", "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_identity_cf", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "status"}, {"fieldKey": "name"}, {"fieldKey": "code"}]}}, {"fieldKey": "employeeId", "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_employee_md", "queryFields": [{"fieldKey": "code"}, {"fieldKey": "name"}, {"fieldKey": "status"}]}}, {"fieldKey": "orgUnitId", "queryModelFields": {"allFields": false, "modelKey": "sys_common$org_struct_md", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "orgCode"}, {"fieldKey": "orgName"}]}}]}}, {"fieldKey": "userId", "queryModelFields": {"allFields": false, "modelKey": "sys_common$user", "queryFields": [{"fieldKey": "id"}]}}]}, "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "员工信息表"}, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"conditionGroup": null, "fieldKey": "employeeOrgLinkList", "modelKey": "sys_common$org_employee_org_link_cf", "sortOrders": null, "subQueryRelatedModels": [{"conditionGroup": null, "fieldKey": "identityId", "modelKey": "sys_common$org_identity_cf", "sortOrders": null, "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "employeeId", "modelKey": "sys_common$org_employee_md", "sortOrders": null, "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "orgUnitId", "modelKey": "sys_common$org_struct_md", "sortOrders": null, "subQueryRelatedModels": []}]}, {"conditionGroup": null, "fieldKey": "userId", "modelKey": "sys_common$user", "sortOrders": null, "subQueryRelatedModels": []}], "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"desc": null, "id": null, "key": "node_1ifee308t7", "name": "调用扩展服务", "nextNodeKey": "node_1ifedu3ql5", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "GLOBAL.(sys_common$org_employee_md)NODE_OUTPUT_node_1ifedu72t6", "implementation": "TSRM$EMPLOYEE_CONVERT_ORG_AND_IDENTITY_ACTION", "implementationName": "员工模型转成组织和身份", "newAction": true, "output": [{"elements": [{"fieldAlias": "success", "fieldKey": "success", "fieldName": "success", "fieldType": "Boolean", "id": null}, {"elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}, {"elements": [], "fieldAlias": "err", "fieldKey": "err", "fieldName": "err", "fieldType": "Object", "id": null}, {"elements": [], "fieldAlias": "info", "fieldKey": "info", "fieldName": "info", "fieldType": "Object", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "ActionResponse", "fieldType": "Object", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1ifh49ien1", "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1ifee308t7", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_1ifedu3ql5", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": null, "key": "TSRM$query_current_user_org_and_identity", "name": "ORG_根据当前用户查询用户所在的主组织及身份", "output": [{"elements": [{"element": {"elements": [{"fieldAlias": "orgName", "fieldKey": "orgName", "fieldName": "orgName", "fieldType": "Text", "id": null}, {"fieldAlias": "orgCode", "fieldKey": "orgCode", "fieldName": "orgCode", "fieldType": "Text", "id": null}, {"fieldAlias": "identityName", "fieldKey": "identityName", "fieldName": "identityName", "fieldType": "Text", "id": null}, {"fieldAlias": "orgId", "fieldKey": "orgId", "fieldName": "orgId", "fieldType": "Number", "id": null}, {"fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "id": null}], "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null}, "fieldAlias": "orgList", "fieldKey": "orgList", "fieldName": "orgList", "fieldType": "Array", "id": null}, {"fieldAlias": "identityName", "fieldKey": "identityName", "fieldName": "identityName", "fieldType": "Text", "id": null}, {"elements": [{"fieldAlias": "orgId", "fieldKey": "orgId", "fieldName": "orgId", "fieldType": "Number", "id": null}, {"fieldAlias": "orgCode", "fieldKey": "orgCode", "fieldName": "orgCode", "fieldType": "Text", "id": null}, {"fieldAlias": "orgName", "fieldKey": "orgName", "fieldName": "orgName", "fieldType": "Text", "id": null}, {"fieldAlias": "identityName", "fieldKey": "identityName", "fieldName": "identityName", "fieldType": "Text", "id": null}, {"fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "id": null}], "fieldAlias": "org", "fieldKey": "org", "fieldName": "org", "fieldType": "Object", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$query_current_user_org_and_identity_service_perm_ac_53dc1c", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}