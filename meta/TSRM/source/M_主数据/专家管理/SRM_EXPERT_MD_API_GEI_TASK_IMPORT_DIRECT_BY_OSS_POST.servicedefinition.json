{"access": "Private", "key": "TSRM$SRM_EXPERT_MD_API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": "SRM专家库-导入导出任务管理接口-通过OSS提交导入任务", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_c8e1367704", "name": "开始", "nextNodeKey": "node_1i3i2ils71", "props": {"globalVariable": null, "input": [{"fieldAlias": "appCode", "fieldKey": "appCode", "fieldName": "appCode", "fieldType": "Text", "id": null}, {"fieldAlias": "errStrategy", "fieldKey": "errStrategy", "fieldName": "errStrategy", "fieldType": "Text", "id": null}, {"fieldAlias": "templateId", "fieldKey": "templateId", "fieldName": "templateId", "fieldType": "Number", "id": null}, {"elements": null, "fieldAlias": "headerSelectiveRecord", "fieldKey": "headerSelectiveRecord", "fieldName": "headerSelectiveRecord", "fieldType": "Object", "id": null}, {"fieldAlias": "relationFieldConvert", "fieldKey": "relationFieldConvert", "fieldName": "relationFieldConvert", "fieldType": "Boolean", "id": null}, {"fieldAlias": "fileKey", "fieldKey": "fileKey", "fieldName": "fileKey", "fieldType": "Text", "id": null}, {"fieldAlias": "fileName", "fieldKey": "fileName", "fieldName": "fileName", "fieldType": "Text", "id": null}, {"fieldAlias": "mergeData", "fieldKey": "mergeData", "fieldName": "mergeData", "fieldType": "Boolean", "id": null}, {"fieldAlias": "modelMainField", "fieldKey": "modelMainField", "fieldName": "modelMainField", "fieldType": "Text", "id": null}, {"element": null, "fieldAlias": "multiSheetConfig", "fieldKey": "multiSheetConfig", "fieldName": "multiSheetConfig", "fieldType": "Array", "id": null}, {"fieldAlias": "namespace", "fieldKey": "namespace", "fieldName": "namespace", "fieldType": "Text", "id": null}, {"elements": null, "fieldAlias": "processConfig", "fieldKey": "processConfig", "fieldName": "processConfig", "fieldType": "Object", "id": null}, {"fieldAlias": "repeatDataStrategy", "fieldKey": "repeatDataStrategy", "fieldName": "repeatDataStrategy", "fieldType": "Text", "id": null}, {"fieldAlias": "selfRelationModel", "fieldKey": "selfRelationModel", "fieldName": "selfRelationModel", "fieldType": "Boolean", "id": null}, {"fieldAlias": "selfRelationModelField", "fieldKey": "selfRelationModelField", "fieldName": "selfRelationModelField", "fieldType": "Text", "id": null}, {"fieldAlias": "service", "fieldKey": "service", "fieldName": "service", "fieldType": "Text", "id": null}, {"elements": null, "fieldAlias": "sheetConfig", "fieldKey": "sheetConfig", "fieldName": "sheetConfig", "fieldType": "Object", "id": null}, {"fieldAlias": "sliceSize", "fieldKey": "sliceSize", "fieldName": "sliceSize", "fieldType": "Number", "id": null}, {"fieldAlias": "taskCode", "fieldKey": "taskCode", "fieldName": "taskCode", "fieldType": "Text", "id": null}, {"fieldAlias": "taskName", "fieldKey": "taskName", "fieldName": "taskName", "fieldType": "Text", "id": null}], "output": [{"elements": [{"fieldAlias": "mainTaskId", "fieldKey": "mainTaskId", "fieldName": "mainTaskId", "fieldType": "Text", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1i3i2ils71", "name": "赋值", "nextNodeKey": "node_5cee14a05b", "props": {"assignments": [{"field": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "service", "valueName": "service"}]}, "id": "1i3i2in532", "operator": "EQ", "value": {"constValue": "TSRM$SRM_EXPERT_MD_GEI_IMPORT_SERVICE", "fieldType": "Text", "id": null, "type": "ConstValue"}}], "type": "AssignProperties"}, "type": "AssignNode"}, {"desc": null, "id": null, "key": "node_5cee14a05b", "name": "Http服务", "nextNodeKey": "node_ec66ec9c5c", "props": {"body": null, "bodyType": "VALUE", "headers": null, "httpType": null, "inputMapping": [{"id": null, "key": "appCode", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "appCode", "valueName": "appCode"}]}}, {"id": null, "key": "errStrategy", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "errStrategy", "valueName": "errStrategy"}]}}, {"id": null, "key": "fileKey", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "fileKey", "valueName": "fileKey"}]}}, {"id": null, "key": "fileName", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "fileName", "valueName": "fileName"}]}}, {"id": null, "key": "mergeData", "required": null, "value": {"fieldType": "Boolean", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "mergeData", "valueName": "mergeData"}]}}, {"id": null, "key": "modelMainField", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "modelMainField", "valueName": "modelMainField"}]}}, {"id": null, "key": "multiSheetConfig", "required": null, "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "multiSheetConfig", "valueName": "multiSheetConfig"}]}}, {"id": null, "key": "namespace", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "namespace", "valueName": "namespace"}]}}, {"id": null, "key": "processConfig", "required": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "processConfig", "valueName": "processConfig"}]}}, {"id": null, "key": "repeatDataStrategy", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "repeatDataStrategy", "valueName": "repeatDataStrategy"}]}}, {"id": null, "key": "templateId", "required": null, "value": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "templateId", "valueName": "templateId"}]}}, {"id": null, "key": "headerSelectiveRecord", "required": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "headerSelectiveRecord", "valueName": "headerSelectiveRecord"}]}}, {"id": null, "key": "relationFieldConvert", "required": null, "value": {"fieldType": "Boolean", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "relationFieldConvert", "valueName": "relationFieldConvert"}]}}, {"id": null, "key": "selfRelationModel", "required": null, "value": {"fieldType": "Boolean", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "selfRelationModel", "valueName": "selfRelationModel"}]}}, {"id": null, "key": "selfRelationModelField", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "selfRelationModelField", "valueName": "selfRelationModelField"}]}}, {"id": null, "key": "service", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "service", "valueName": "service"}]}}, {"id": null, "key": "sheetConfig", "required": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "sheetConfig", "valueName": "sheetConfig"}]}}, {"id": null, "key": "sliceSize", "required": null, "value": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "sliceSize", "valueName": "sliceSize"}]}}, {"id": null, "key": "taskCode", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "taskCode", "valueName": "taskCode"}]}}, {"id": null, "key": "taskName", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "taskName", "valueName": "taskName"}]}}], "jsonBody": null, "method": "POST", "output": [{"elements": [{"elements": [{"elements": [{"fieldAlias": "mainTaskId", "fieldKey": "mainTaskId", "fieldName": "mainTaskId", "fieldType": "Text", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": false}, {"fieldAlias": "success", "fieldKey": "success", "fieldName": "success", "fieldType": "Boolean", "id": null, "required": false}, {"elements": [{"fieldAlias": "code", "fieldKey": "code", "fieldName": "code", "fieldType": "Text", "id": null, "required": false}, {"fieldAlias": "msg", "fieldKey": "msg", "fieldName": "msg", "fieldType": "Text", "id": null, "required": false}, {"fieldAlias": "suggest", "fieldKey": "suggest", "fieldName": "suggest", "fieldType": "Text", "id": null, "required": false}], "fieldAlias": "err", "fieldKey": "err", "fieldName": "err", "fieldType": "Object", "id": null, "required": false}], "fieldAlias": "response", "fieldKey": "response", "fieldName": "response", "fieldType": "Object", "id": null, "required": false}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pathVariables": [{"id": null, "key": "GEI_SERVER_ENDPOINT", "required": null, "value": {"fieldType": null, "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "MODULE", "valueName": "模块变量"}, {"fieldType": null, "valueKey": "GEI_SERVER_ENDPOINT", "valueName": "GEI_SERVER_ENDPOINT"}]}}], "serviceName": "导入导出任务管理接口-通过OSS提交导入任务(/api/gei/task/import-direct-by-oss#POST)", "stream": false, "type": "HttpServiceProperties", "url": "http://middleware-server:8080/api/gei/task/import-direct-by-oss"}, "type": "HttpServiceNode"}, {"children": [{"children": [{"desc": null, "id": null, "key": "node_ce0aa03fdf", "name": "赋值", "props": {"assignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "node_5ac03c4f77", "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_5cee14a05b", "valueName": "[Http服务]节点出参"}, {"fieldType": null, "valueKey": "response", "valueName": "response"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}], "desc": null, "id": null, "key": "node_d84ff47f59", "name": "条件", "nextNodeKey": "node_ce0aa03fdf", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "node_30f1544d35", "key": "node_30f1544d35", "leftValue": {"fieldType": "Boolean", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_5cee14a05b", "valueName": "[Http服务]节点出参"}, {"fieldType": null, "valueKey": "response", "valueName": "response"}, {"fieldType": null, "valueKey": "success", "valueName": "success"}]}, "operator": "EQ", "rightValue": {"constValue": "true", "fieldType": "Boolean", "id": null, "scope": null, "type": "VarValue", "valueType": "CONST", "varValue": null}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ExclusiveConditionProperties"}, "type": "ExclusiveConditionNode"}, {"children": [{"desc": null, "id": null, "key": "node_3176d295ec", "name": "异常错误", "props": {"errorCode": "sys_common$errorCommon", "errorMsg": "执行发生错误${msg}", "link": "MetaLink$ErrorCode$sys_common$errorCommon", "placeholderMapping": [{"id": null, "key": "msg", "required": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_5cee14a05b", "valueName": "[Http服务]节点出参"}, {"fieldType": null, "valueKey": "response", "valueName": "response"}, {"fieldType": null, "valueKey": "err", "valueName": "err"}, {"fieldType": null, "valueKey": "msg", "valueName": "msg"}]}}], "type": "ErrorProperties"}, "type": "ErrorNode"}], "desc": null, "id": null, "key": "node_d180a42cd4", "name": "条件", "nextNodeKey": "node_3176d295ec", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "node_03a9ffa061", "key": "node_03a9ffa061", "leftValue": {"fieldType": "Boolean", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_5cee14a05b", "valueName": "[Http服务]节点出参"}, {"fieldType": null, "valueKey": "response", "valueName": "response"}, {"fieldType": null, "valueKey": "success", "valueName": "success"}]}, "operator": "EQ", "rightValue": {"constValue": "false", "fieldType": "Boolean", "id": null, "scope": null, "type": "VarValue", "valueType": "CONST", "varValue": null}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ExclusiveConditionProperties"}, "type": "ExclusiveConditionNode"}], "desc": null, "id": null, "key": "node_ec66ec9c5c", "name": "排他分支", "nextNodeKey": "node_e62e1027ff", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"desc": null, "id": null, "key": "node_e62e1027ff", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_c8e1367704"], "id": null, "input": [{"fieldAlias": "appCode", "fieldKey": "appCode", "fieldName": "appCode", "fieldType": "Text", "id": null}, {"fieldAlias": "errStrategy", "fieldKey": "errStrategy", "fieldName": "errStrategy", "fieldType": "Text", "id": null}, {"fieldAlias": "templateId", "fieldKey": "templateId", "fieldName": "templateId", "fieldType": "Number", "id": null}, {"elements": null, "fieldAlias": "headerSelectiveRecord", "fieldKey": "headerSelectiveRecord", "fieldName": "headerSelectiveRecord", "fieldType": "Object", "id": null}, {"fieldAlias": "relationFieldConvert", "fieldKey": "relationFieldConvert", "fieldName": "relationFieldConvert", "fieldType": "Boolean", "id": null}, {"fieldAlias": "fileKey", "fieldKey": "fileKey", "fieldName": "fileKey", "fieldType": "Text", "id": null}, {"fieldAlias": "fileName", "fieldKey": "fileName", "fieldName": "fileName", "fieldType": "Text", "id": null}, {"fieldAlias": "mergeData", "fieldKey": "mergeData", "fieldName": "mergeData", "fieldType": "Boolean", "id": null}, {"fieldAlias": "modelMainField", "fieldKey": "modelMainField", "fieldName": "modelMainField", "fieldType": "Text", "id": null}, {"element": null, "fieldAlias": "multiSheetConfig", "fieldKey": "multiSheetConfig", "fieldName": "multiSheetConfig", "fieldType": "Array", "id": null}, {"fieldAlias": "namespace", "fieldKey": "namespace", "fieldName": "namespace", "fieldType": "Text", "id": null}, {"elements": null, "fieldAlias": "processConfig", "fieldKey": "processConfig", "fieldName": "processConfig", "fieldType": "Object", "id": null}, {"fieldAlias": "repeatDataStrategy", "fieldKey": "repeatDataStrategy", "fieldName": "repeatDataStrategy", "fieldType": "Text", "id": null}, {"fieldAlias": "selfRelationModel", "fieldKey": "selfRelationModel", "fieldName": "selfRelationModel", "fieldType": "Boolean", "id": null}, {"fieldAlias": "selfRelationModelField", "fieldKey": "selfRelationModelField", "fieldName": "selfRelationModelField", "fieldType": "Text", "id": null}, {"fieldAlias": "service", "fieldKey": "service", "fieldName": "service", "fieldType": "Text", "id": null}, {"elements": null, "fieldAlias": "sheetConfig", "fieldKey": "sheetConfig", "fieldName": "sheetConfig", "fieldType": "Object", "id": null}, {"fieldAlias": "sliceSize", "fieldKey": "sliceSize", "fieldName": "sliceSize", "fieldType": "Number", "id": null}, {"fieldAlias": "taskCode", "fieldKey": "taskCode", "fieldName": "taskCode", "fieldType": "Text", "id": null}, {"fieldAlias": "taskName", "fieldKey": "taskName", "fieldName": "taskName", "fieldType": "Text", "id": null}], "key": "TSRM$SRM_EXPERT_MD_API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": "SRM专家库-导入导出任务管理接口-通过OSS提交导入任务", "output": [{"elements": [{"fieldAlias": "mainTaskId", "fieldKey": "mainTaskId", "fieldName": "mainTaskId", "fieldType": "Text", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$SRM_EXPERT_MD_CREATE_PERMISSION", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}