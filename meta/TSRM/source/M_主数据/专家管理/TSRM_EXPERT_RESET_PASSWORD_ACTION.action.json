{"access": "Private", "key": "TSRM$TSRM_EXPERT_RESET_PASSWORD_ACTION", "name": "用户管理-专家重置密码", "props": {"bean": "TsrmUserAction", "desc": null, "groovyScript": null, "languageType": "Java", "method": "expertManageResetPassword", "order": 10, "requestType": "io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertMdDTO", "responseType": "java.lang.Void", "returnModel": null, "status": "enabled"}, "type": "Action"}