{"access": "Private", "key": "TSRM$TSRM_EXPERT_CREATE_WITH_USER_ACTION", "name": "专家管理-新建专家同步创建用户", "props": {"bean": "TsrmExpertAction", "desc": null, "groovyScript": null, "languageType": "Java", "method": "expertCreateWithUser", "order": 10, "requestType": "io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertMdDTO", "responseType": "java.lang.Void", "returnModel": null, "status": "enabled"}, "type": "Action"}