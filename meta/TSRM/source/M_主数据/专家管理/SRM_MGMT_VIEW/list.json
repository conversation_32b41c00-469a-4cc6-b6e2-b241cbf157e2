{"access": "Private", "key": "TSRM$SRM_MGMT_VIEW:list", "name": "list", "props": {"containerSelect": {"TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md": [{"conditionGroup": null, "field": "expertName", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "recommType", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "contactPhone", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertLevel", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "status", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "user", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}]}, "content": {"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-dvh56J7TGoHSMy3Yr0rs6", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-uL1RQD_GxVpvLEfabRsfd", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-7ualmsj2knIPHYqQapbBe", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-T0J8OBC6KSRKbaT_H8Iwi", "name": "ActionsGroup", "props": {"params": []}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-list-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-list-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "SRM专家库列表"}, "type": "Meta"}, {"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-batch-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "label": "新建", "permissionKey": "TSRM$SRM_MGMT_VIEW:list_perm_ac_z_2_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-batch-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否删除选中单据？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$srm_expert_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}}], "service": "TSRM$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md"}, {"action": "Message", "level": "success", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "disabled$": "mode === \"design\" ? false : $context.selectedKeys?.length === 0", "isMultiple": true, "label": "删除", "permissionKey": "TSRM$SRM_MGMT_VIEW:list_perm_ac_z_2_0_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-batch-actions-1-button-3", "name": "ExportButton", "props": {"defaultExportFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}, "exportActions": [{"enabled": true, "type": "custom"}], "exportButtonServiceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "exportFlow": {"modelAlias": "TSRM$srm_expert_md", "serviceKey": "TSRM$SRM_EXPERT_MD_API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}, "getAliasFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor/struct-node/find-by-alias"}, "label": "导出", "permissionKey": "TSRM$SRM_MGMT_VIEW:list_perm_ac_z_2_0_0_2", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}, "serviceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "templatePagingFlow": {"serviceKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "type": "InvokeService"}}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-zfdsCy3XCBjtH20hFMboM", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-87qrZeAW_oj6aCz8ED5d-", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-6j5nlSY1Ow85aI8mozloT", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-batch-actions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-record-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "show"}]}, "buttonType": "default", "confirmOn": "off", "label": "查看", "permissionKey": "TSRM$SRM_MGMT_VIEW:list_perm_ac_z_2_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-record-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$SRM_MGMT_VIEW:list_perm_ac_z_2_1_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-record-actions-1-button-3", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "TSRM$srm_expert_md", "valueConfig": {"expression": "record", "type": "expression"}}], "service": "TSRM$SRM_DELETE_EXPERT_SERVICE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": ["TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md"]}, {"action": "Message", "level": "success", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$SRM_MGMT_VIEW:list_perm_ac_z_2_1_2", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-1rQMpu4YyAXHexbAJVGSB", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "TSRM$srm_expert_md", "valueConfig": {"expression": "record", "type": "expression"}}], "service": "TSRM$SRM_ENABLE_EXPERT_SERVICE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": ["TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$SRM_MGMT_VIEW:list_perm_ac_z_2_1_3", "showCondition": {"conditions": [{"conditions": [{"id": "xGB6Vi8SIMr-XsXB7cHs0", "leftValue": {"fieldType": "Enum", "scope": "row", "title": "状态(status)", "type": "VarValue", "val": "status", "value": "TSRM$srm_expert_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "DRAFT", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "B862khyI1LcYsjmCCN4mJ", "leftValue": {"fieldType": "Enum", "scope": "row", "title": "状态(status)", "type": "VarValue", "val": "status", "value": "TSRM$srm_expert_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "DISABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "L3iy0G_NeG_EiAIuq2vjf", "logicOperator": "OR", "type": "ConditionGroup"}], "id": "AMTFC9OziKjmRE_MNCmtf", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-8XK1wi8T8rBpNZXhV0B-I", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "TSRM$srm_expert_md", "valueConfig": {"expression": "record", "type": "expression"}}], "service": "TSRM$SRM_DISABLE_EXPERT_SERVICE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": ["TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$SRM_MGMT_VIEW:list_perm_ac_z_2_1_4", "showCondition": {"conditions": [{"conditions": [{"id": "dioaK607ylYnb1XyvttO1", "leftValue": {"fieldType": "Enum", "scope": "row", "title": "状态(status)", "type": "VarValue", "val": "status", "value": "TSRM$srm_expert_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "F6ClHffbLwokqjcrBUmP7", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "w8pIS7ReHYpdDxH-pMgAG", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-ox0CYgGTuYKq2Zg8CrU8p", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "actionId": "KhSKq1-0BrEm48i75A6Lg", "id": "KhSKq1-0BrEm48i75A6Lg-0", "text": "是否确认重置密码？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "TSRM$srm_expert_md", "valueConfig": {"expression": "record", "type": "expression"}}], "service": "TSRM$SRM_EXPERT_RESERT_PASSWORD"}, "endLogicOtherConfig": [{"action": "Message", "actionId": "mtO2KggpYV6fElkvShbJr", "id": "mtO2KggpYV6fElkvShbJr-0", "level": "success", "message": "密码重置成功！"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "重置密码", "showCondition": {"conditions": [], "id": "nx1EfmdStEbSVl0KfJSy6", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-aexF6bcWkKFmqxD3bhlpF", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "actionId": "iR6Th4BAvWCbT5qsO_kH9", "id": "iR6Th4BAvWCbT5qsO_kH9-0", "openViewConfig": {"page": {"key": "TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6", "name": "绑定角色 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"expression": "record.user.id", "name": "userId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "绑定角色", "permissionKey": "TSRM$SRM_MGMT_VIEW-aexF6bcWkKFmqxD3bhlpF_perm_ac", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-record-actions-1", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-list-TSRM$srm_expert_md-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-Y59zUAeUsK2ym0AQKcliU", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-toolbar-actions-1", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-TL1imSrFajep9Aj8AEsEV", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-jtEwsUfckxXwmj-rdMYor", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-DWAgu8en85NXTwbd4av80", "name": "Field", "props": {"componentProps": {"fieldAlias": "expertName", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": false, "label": "专家姓名", "name": "expertName", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-qvX6PDj86R6-vvYpQCsrG", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "recommType", "modelAlias": "TSRM$srm_expert_md", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": false, "label": "来源方式", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Ha-vgZ-K7cBQlPyrVVnOx", "trigger": "auto", "valueRules": null}], "modelAlias": "TSRM$srm_expert_md", "name": "recommType", "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-YZJJgjWXBCqYcCFvKCrbx", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "contactPhone", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": false, "label": "手机号", "modelAlias": "TSRM$srm_expert_md", "name": "contactPhone", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-x-WMg2oCAdLjPiTvy-Tby", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "expertLevel", "modelAlias": "TSRM$srm_expert_md", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "labelField": ["name"], "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": false, "label": "专家等级", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "2v6KeShmbil3c_W9pq_jg", "trigger": "auto", "valueRules": null}], "modelAlias": "sys_common$org_employee_md", "name": "expertLevel", "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-328OWWsGd_pI2XnZBrl9d", "name": "Field", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": false, "label": "状态", "name": "status", "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-q90MT8WvPzEWY3hcpVAFv", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "user", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "0YfUtY4KqPpv5Y8KbSqYj", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "editComponentType": "RelationSelect", "label": "用户", "lookup": [{"fieldRules": {"hidden": true, "required": false}, "key": "kwzzFFUC9FTj1rUjaG1qJ", "trigger": "auto", "valueRules": null}], "modelAlias": "TSRM$user", "name": "user", "type": "OBJECT", "width": 168}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-MDT3gW0E7z6gxTq7Zws5k", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "name": "Table", "props": {"acceptFilterQuery": true, "allowClickRowSelect": true, "allowRowSelect": true, "enableSolution": true, "filterFields": [{"componentProps": {"fieldAlias": "expertName", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": false, "label": "专家姓名", "name": "expertName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "recommType", "modelAlias": "TSRM$srm_expert_md", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md", "options": [{"_row_id_": "SELF_A", "enabled": true, "isDefault": false, "label": "个人申请", "value": "SELF_A"}, {"_row_id_": "COMPANY_C", "enabled": true, "isDefault": false, "label": "单位推荐", "value": "COMPANY_C"}, {"_row_id_": "SELF_C", "enabled": true, "isDefault": false, "label": "个人推荐", "value": "SELF_C"}, {"_row_id_": "2652sAM", "enabled": true, "isDefault": false, "label": "内部员工", "value": "EMPLOYEE"}]}, "editComponentType": "Select", "hidden": false, "label": "来源方式", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "9PQd5aIujS0OoR7rSflVF", "trigger": "auto", "valueRules": null}], "name": "recommType", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"fieldAlias": "contactPhone", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "InputText", "filterType": "fuzzy", "hidden": false, "label": "手机号", "name": "contactPhone", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "expertLevel", "modelAlias": "TSRM$srm_expert_md", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md", "options": [{"_row_id_": "JUNIOR", "enabled": true, "isDefault": false, "label": "初级", "value": "JUNIOR"}, {"_row_id_": "3JXXcOU", "enabled": true, "isDefault": false, "label": "中级", "value": "INTER"}, {"_row_id_": "QNnBOLN", "enabled": true, "isDefault": false, "label": "高级", "value": "SENIOR"}, {"_row_id_": "ELDER", "enabled": true, "isDefault": false, "label": "资深", "value": "ELDER"}]}, "editComponentType": "Select", "hidden": false, "label": "专家等级", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "A9qHsX6ZtqXNNzpjhlDYK", "trigger": "auto", "valueRules": null}], "name": "expertLevel", "required": false, "type": "SELECT", "width": 120}], "flow": {"containerKey": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "context$": "$context", "modelAlias": "TSRM$srm_expert_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$srm_expert_md"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "SRM专家库管理", "mode": "normal", "modelAlias": "TSRM$srm_expert_md", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "selectType": "multiple", "showConfigure": false, "showFilterFields": true, "showScope": "all", "showType": "normal", "subTableConfig": {}, "tableCondition": null, "tableConditionContext$": null, "toolbar": {"search": false}}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-giTrJGxYEOCEsoGTg8v89", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "roleIds", "fieldName": "roleIds", "fieldType": "Array", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "type": "expression"}}, {"fieldAlias": "userId", "fieldName": "userId", "fieldType": "Number", "valueConfig": {"expression": "params.userId", "type": "expression"}}], "service": "sys_common$API_TRANTOR_PORTAL_ROLE_RELATION_USER_FLUSH_POST"}, "endLogicOtherConfig": [{"action": "Close", "actionId": "4_HQTPsZA102BJRZ6UFKa", "id": "4_HQTPsZA102BJRZ6UFKa-0", "target": ["TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6"]}, {"action": "RefreshTab", "actionId": "EKxMH2-ZRIyc0Pqb8ykH7", "id": "EKxMH2-ZRIyc0Pqb8ykH7-1", "light": false, "target": ["current"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-h9qIlzcLxYB3m0HZYf-NV", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-PV5XQglRXYzmCsV9bI4R7", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-TqlF9TbKFPUcRaLUwov_E", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-u1EtaNpX23upNIR3UAtD9", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-BwDx2vup-LG4OFa46yenL", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-8U5-SBHIcUMEdi55Qw_AX", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-Xlb8dleHD-dmcgaGnbyHH", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-aQIu0ic44g1breS-MHYeR", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-ZMlawsEG2-bkgMJYbMtnU", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-2J7eHbQnt1N9Q8YLcUeiI", "name": "Field", "props": {"componentProps": {"fieldAlias": "name", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "角色名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "gs0DMKRaU3L5fAFAyf5Pa", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-po8Xu9y2IJWKGhd8BfFH_", "name": "Field", "props": {"componentProps": {"fieldAlias": "key", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "角色标识", "name": "key", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-qsCqjtIWjJ-0qnrcaDxev", "name": "Field", "props": {"componentProps": {"fieldAlias": "desc", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "角色描述", "name": "desc", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-2SMz0eTo-Ximw5JRz3AL9", "name": "Field", "props": {"componentProps": {"fieldAlias": "sourceApplicationId", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "所属门户", "name": "sourceApplicationId", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-zxmeJfvWRw2LD3G8DC-zN", "name": "Field", "props": {"align": "right", "componentProps": {"fieldAlias": "id", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {"precisionDisplayType": "fill-round"}, "displayComponentType": "Number", "editComponentProps": {}, "label": "id", "lookup": [{"fieldRules": {"hidden": true, "required": false}, "key": "XAwdVr5u7vi7QWUy2NYn7", "trigger": "auto", "valueRules": null}], "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-ClsrRau64zpuA5U1uXpN7", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9", "name": "Table", "props": {"allowRowSelect": true, "enableSolution": true, "filterFields": [{"componentProps": {"fieldAlias": "name", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "label": "角色名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "gs0DMKRaU3L5fAFAyf5Pa", "trigger": "auto", "valueRules": null}], "name": "name", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "key", "parentModelAlias": "TSRM$tsrm_emp_ro_temp_tr", "placeholder": "请输入"}, "displayComponentProps": {}, "editComponentProps": {}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "角色标识", "name": "key", "type": "TEXT", "width": 146}], "flow": {"context$": "$context", "params": [{"fieldAlias": "pageable", "fieldName": "分页设置", "fieldType": "Pageable"}], "serviceKey": "TSRM$SRM_SEARCH_ROLES_SERVICE", "type": "InvokeService"}, "label": "SRM专家库管理", "mode": "normal", "modelAlias": "TSRM$tsrm_emp_ro_temp_tr", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "selectType": "multiple", "showType": "normal", "subTableConfig": {}}, "type": "Container"}], "key": "TSRM$SRM_MGMT_VIEW-QZXTDze-FKgx_Fp3v5JRf", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-mHOP0qHr0Bpdt7hsrrClS", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": true, "layout": "modal", "layoutProps": {"bodyStyle": {}, "dragMode": [], "maskClosable": false, "width": 720, "widthType": "middle"}, "params": [{"label": "用户id", "value": "userId"}], "title": "绑定角色"}, "type": "Container"}], "key": "TSRM$SRM_MGMT_VIEW-list", "name": "Page", "props": {"collectionService": {"createBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/save"}, "deleteBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/delete"}, "getCurrentSceneBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK"}}, "params": [], "showFooter": false, "showHeader": false}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["用户", "状态", "删除成功", "停用", "新建", "绑定角色", "查看", "重置密码", "是否删除选中单据？", "删除", "保存", "启用", "SRM专家库管理", "用户名", "id", "角色标识", "专家等级", "请选择", "来源方式", "SRM专家库列表", "编辑", "密码重置成功！", "手机号", "请输入", "角色名称", "所属门户", "专家姓名", "确认删除吗？", "是否确认重置密码？", "角色描述"], "i18nScanPaths": ["TSRM$SRM_MGMT_VIEW-328OWWsGd_pI2XnZBrl9d.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md.props.label", "TSRM$SRM_MGMT_VIEW-x-WMg2oCAdLjPiTvy-Tby.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md.props.filterFields.3.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md.props.filterFields.1.label", "TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6.props.title", "TSRM$SRM_MGMT_VIEW-x-WMg2oCAdLjPiTvy-Tby.props.label", "TSRM$SRM_MGMT_VIEW-2J7eHbQnt1N9Q8YLcUeiI.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md.props.filterFields.2.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-DWAgu8en85NXTwbd4av80.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-YZJJgjWXBCqYcCFvKCrbx.props.label", "TSRM$SRM_MGMT_VIEW-ox0CYgGTuYKq2Zg8CrU8p.props.actionConfig.beforeLogicConfig.0.text", "TSRM$SRM_MGMT_VIEW-record-actions-1-button-3.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$SRM_MGMT_VIEW-po8Xu9y2IJWKGhd8BfFH_.props.label", "TSRM$SRM_MGMT_VIEW-batch-actions-1-button-2.props.actionConfig.endLogicOtherConfig.1.message", "TSRM$SRM_MGMT_VIEW-328OWWsGd_pI2XnZBrl9d.props.label", "TSRM$SRM_MGMT_VIEW-batch-actions-1-button-2.props.actionConfig.beforeLogicConfig.0.text", "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md.props.filterFields.1.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-1rQMpu4YyAXHexbAJVGSB.props.label", "TSRM$SRM_MGMT_VIEW-2SMz0eTo-Ximw5JRz3AL9.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9.props.filterFields.1.label", "TSRM$SRM_MGMT_VIEW-giTrJGxYEOCEsoGTg8v89.props.label", "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md.props.filterFields.2.label", "TSRM$SRM_MGMT_VIEW-zxmeJfvWRw2LD3G8DC-zN.props.label", "TSRM$SRM_MGMT_VIEW-8XK1wi8T8rBpNZXhV0B-I.props.label", "TSRM$SRM_MGMT_VIEW-record-actions-1-button-1.props.label", "TSRM$SRM_MGMT_VIEW-qvX6PDj86R6-vvYpQCsrG.props.label", "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9.props.filterFields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-2J7eHbQnt1N9Q8YLcUeiI.props.label", "TSRM$SRM_MGMT_VIEW-record-actions-1-button-2.props.label", "TSRM$SRM_MGMT_VIEW-record-actions-1-button-3.props.label", "TSRM$SRM_MGMT_VIEW-q90MT8WvPzEWY3hcpVAFv.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-record-actions-1-button-3.props.actionConfig.beforeLogicConfig.0.text", "TSRM$SRM_MGMT_VIEW-q90MT8WvPzEWY3hcpVAFv.props.componentProps.placeholder", "@exp:TSRM$SRM_MGMT_VIEW-list-title.props.title", "TSRM$SRM_MGMT_VIEW-ox0CYgGTuYKq2Zg8CrU8p.props.label", "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md.props.filterFields.3.label", "TSRM$SRM_MGMT_VIEW-q90MT8WvPzEWY3hcpVAFv.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-qvX6PDj86R6-vvYpQCsrG.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md.props.filterFields.0.label", "TSRM$SRM_MGMT_VIEW-YZJJgjWXBCqYcCFvKCrbx.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-po8Xu9y2IJWKGhd8BfFH_.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-2SMz0eTo-Ximw5JRz3AL9.props.label", "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9.props.filterFields.0.label", "TSRM$SRM_MGMT_VIEW-batch-actions-1-button-2.props.label", "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md.props.filterFields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-qsCqjtIWjJ-0qnrcaDxev.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9.props.filterFields.1.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-batch-actions-1-button-1.props.label", "TSRM$SRM_MGMT_VIEW-ox0CYgGTuYKq2Zg8CrU8p.props.actionConfig.endLogicOtherConfig.0.message", "TSRM$SRM_MGMT_VIEW-DWAgu8en85NXTwbd4av80.props.label", "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9.props.label", "TSRM$SRM_MGMT_VIEW-aexF6bcWkKFmqxD3bhlpF.props.label", "TSRM$SRM_MGMT_VIEW-q90MT8WvPzEWY3hcpVAFv.props.label", "TSRM$SRM_MGMT_VIEW-qsCqjtIWjJ-0qnrcaDxev.props.label", "TSRM$SRM_MGMT_VIEW-zxmeJfvWRw2LD3G8DC-zN.props.componentProps.placeholder"]}, "key": "TSRM$SRM_MGMT_VIEW:list", "name": "list", "permissionKey": "TSRM$SRM_MGMT_VIEW:list_perm_ac", "resources": [{"description": null, "key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-record-actions-1-button-1", "label": "查看", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-record-actions-1-button-2", "label": "编辑", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-record-actions-1-button-3", "label": "删除", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$SRM_DELETE_EXPERT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-1rQMpu4YyAXHexbAJVGSB", "label": "启用", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$SRM_ENABLE_EXPERT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-8XK1wi8T8rBpNZXhV0B-I", "label": "停用", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$SRM_DISABLE_EXPERT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-ox0CYgGTuYKq2Zg8CrU8p", "label": "重置密码", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$SRM_EXPERT_RESERT_PASSWORD", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-aexF6bcWkKFmqxD3bhlpF", "label": "绑定角色", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-DWAgu8en85NXTwbd4av80", "label": "专家姓名", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-MDT3gW0E7z6gxTq7Zws5k", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-qvX6PDj86R6-vvYpQCsrG", "label": "来源方式", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-MDT3gW0E7z6gxTq7Zws5k", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-YZJJgjWXBCqYcCFvKCrbx", "label": "手机号", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-MDT3gW0E7z6gxTq7Zws5k", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-x-WMg2oCAdLjPiTvy-Tby", "label": "专家等级", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-MDT3gW0E7z6gxTq7Zws5k", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-328OWWsGd_pI2XnZBrl9d", "label": "状态", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-MDT3gW0E7z6gxTq7Zws5k", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-q90MT8WvPzEWY3hcpVAFv", "label": "用户", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-MDT3gW0E7z6gxTq7Zws5k", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9", "label": "SRM专家库管理", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$SRM_MGMT_VIEW-QZXTDze-FKgx_Fp3v5JRf", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "TSRM$SRM_SEARCH_ROLES_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-batch-actions-1-button-1", "label": "新建", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-batch-actions-1", "label": "按钮组", "type": "BatchActions"}, {"key": "TSRM$SRM_MGMT_VIEW-zfdsCy3XCBjtH20hFMboM", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-batch-actions-1-button-2", "label": "删除", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-batch-actions-1", "label": "按钮组", "type": "BatchActions"}, {"key": "TSRM$SRM_MGMT_VIEW-zfdsCy3XCBjtH20hFMboM", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "TSRM$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-batch-actions-1-button-3", "label": "导出", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-batch-actions-1", "label": "按钮组", "type": "BatchActions"}, {"key": "TSRM$SRM_MGMT_VIEW-zfdsCy3XCBjtH20hFMboM", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "TSRM$SRM_EXPERT_MD_API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "Service"}, {"key": "/api/trantor/struct-node/find-by-alias", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-list-TSRM$srm_expert_md-logs", "label": "日志", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-table-container-TSRM$srm_expert_md", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-toolbar-actions-1", "label": "按钮组", "type": "ToolbarActions"}, {"key": "TSRM$SRM_MGMT_VIEW-Y59zUAeUsK2ym0AQKcliU", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-2J7eHbQnt1N9Q8YLcUeiI", "label": "角色名称", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$SRM_MGMT_VIEW-QZXTDze-FKgx_Fp3v5JRf", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-ClsrRau64zpuA5U1uXpN7", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-po8Xu9y2IJWKGhd8BfFH_", "label": "角色标识", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$SRM_MGMT_VIEW-QZXTDze-FKgx_Fp3v5JRf", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-ClsrRau64zpuA5U1uXpN7", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-qsCqjtIWjJ-0qnrcaDxev", "label": "角色描述", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$SRM_MGMT_VIEW-QZXTDze-FKgx_Fp3v5JRf", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-ClsrRau64zpuA5U1uXpN7", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-2SMz0eTo-Ximw5JRz3AL9", "label": "所属门户", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$SRM_MGMT_VIEW-QZXTDze-FKgx_Fp3v5JRf", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-ClsrRau64zpuA5U1uXpN7", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-zxmeJfvWRw2LD3G8DC-zN", "label": "id", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$SRM_MGMT_VIEW-QZXTDze-FKgx_Fp3v5JRf", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-ClsrRau64zpuA5U1uXpN7", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-giTrJGxYEOCEsoGTg8v89", "label": "保存", "path": [{"key": "TSRM$SRM_MGMT_VIEW-list", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-XXP1xeltmNEzBhhkLVBb6", "label": "绑定角色", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$SRM_MGMT_VIEW-QZXTDze-FKgx_Fp3v5JRf", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "TSRM$SRM_MGMT_VIEW-Wji-twJ7oPXyVEwIm9TL9", "label": "SRM专家库管理", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-u1EtaNpX23upNIR3UAtD9", "label": "按钮组", "type": "BatchActions"}, {"key": "TSRM$SRM_MGMT_VIEW-h9qIlzcLxYB3m0HZYf-NV", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "sys_common$API_TRANTOR_PORTAL_ROLE_RELATION_USER_FLUSH_POST", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}], "title": "list", "type": "LIST"}, "type": "View"}