{"access": "Private", "key": "TSRM$SRM_MGMT_VIEW:detail", "name": "detail", "props": {"containerSelect": {"TSRM$SRM_MGMT_VIEW-detailView-detail": [{"conditionGroup": null, "field": "expertCode", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertName", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertType", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "employeeId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "name", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "expertCertType", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertCert<PERSON>um", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "contactPhone", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "contactEmail", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertLevel", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "recommType", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "recommPartyName", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "country", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "coun<PERSON><PERSON>", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "addressId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "addrName", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "addressDetail", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertComName", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertComCreditCode", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "attachment", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "remark", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "status", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "created<PERSON>y", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "createdAt", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "updatedBy", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "updatedAt", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "busiScope", "selectFields": [{"conditionGroup": null, "field": "busiScope", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "updatedAt", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "matCate", "selectFields": [{"conditionGroup": null, "field": "matCateId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "matCateName", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "matCateCode", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "updatedAt", "selectFields": null, "sortOrders": null}], "sortOrders": null}]}, "content": {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-detail-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "SRM专家库详情"}, "type": "Meta"}, {"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-detail-page-header-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { copyId: route.recordId } })"}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$SRM_MGMT_VIEW:detail_perm_ac_z_1_0_0_0", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detail-page-header-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$SRM_MGMT_VIEW:detail_perm_ac_z_1_0_0_1", "showCondition": {}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detail-TSRM$srm_expert_md-logs", "name": "Logs", "props": {"currentModel": "TSRM$user", "label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-FKXo6l19Hd6fxpRQzikPz", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-j6EF5pEW7odpcIgjezlOc", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-8ZYIkQn-Lez2l2KMAs6NU", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-JGDcGfYImih6ESFQu-g4p", "name": "ActionsGroup", "props": {"params": []}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-detail-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCode", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "expertCode", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "专家编号", "name": "expertCode", "type": "TEXT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertName", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "expertName", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "专家姓名", "name": "expertName", "type": "TEXT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertType", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "expertType", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "专家类型", "name": "expertType", "type": "SELECT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-employeeId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "employeeId", "label": "选择关联员工", "labelField": "name", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_employee_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "sys_common$org_employee_md"}, "editable": false, "label": "关联员工", "name": "employeeId", "type": "OBJECT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCertType", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "expertCertType", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "专家证件类型", "name": "expertCertType", "type": "SELECT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCert<PERSON>um", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "expertCert<PERSON>um", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "专家证件号", "name": "expertCert<PERSON>um", "type": "TEXT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-contactPhone", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "contactPhone", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "联系电话", "name": "contactPhone", "type": "TEXT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-contactEmail", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "contactEmail", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "联系邮箱", "name": "contactEmail", "type": "TEXT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertLevel", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "expertLevel", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "专家等级", "name": "expertLevel", "type": "SELECT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-recommType", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "recommType", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "来源方式", "name": "recommType", "type": "SELECT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-recommPartyName", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "recommPartyName", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "推荐方名称", "name": "recommPartyName", "type": "TEXT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-country", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "country", "label": "选择所在国家/地区", "labelField": "coun<PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_coun_type_cf", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"containerKey": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-country", "context$": "$context", "modelAlias": "ERP_GEN$gen_coun_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_coun_type_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_coun_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "ERP_GEN$gen_coun_type_cf"}, "editable": false, "label": "所在国家/地区", "name": "country", "type": "OBJECT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-y3AH6LFiMvVCf8z-VN4BL", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressId", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"containerKey": "TSRM$SRM_MGMT_VIEW-y3AH6LFiMvVCf8z-VN4BL", "context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_addr_type_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "reverseConstructFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "ERP_GEN$gen_addr_type_cf"}, "editable": false, "label": "地址", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "OI9g8Hhnf2uz1iesPwL2v", "trigger": "auto", "valueRules": null}], "name": "addressId", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-d6TaGKy_Xl6U6hked7F6N", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressDetail", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {}, "editable": false, "label": "详细地址", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "G4Hys2dOm80ZxN2YLN0Lm", "trigger": "auto", "valueRules": null}], "name": "addressDetail", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertComName", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "expertComName", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "现任公司名称", "name": "expertComName", "type": "TEXT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertComCreditCode", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "expertComCreditCode", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "现任公司统一社会信用代码", "name": "expertComCreditCode", "type": "TEXT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-attachment", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "attachment", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请上传"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "附件", "name": "attachment", "type": "ATTACHMENT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-remark", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "remark", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "备注", "name": "remark", "type": "MULTI_TEXT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-status", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "状态", "name": "status", "type": "SELECT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "TSRM$user"}, "editable": false, "label": "创建人", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false}, "key": "TIIZJq8B5gxsCY9N-BQ3F", "valueRules": null}], "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Date", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "创建时间", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false}, "key": "HAbW7qdo6tKcF-6-xV1op", "valueRules": null}], "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "TSRM$user"}, "editable": false, "label": "更新人", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false}, "key": "Dw6dYKNdOOvW5Wejjlkzm", "valueRules": null}], "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Date", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editable": false, "label": "更新时间", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false}, "key": "gSccOPIQHpb2xzuEztCSA", "valueRules": null}], "name": "updatedAt", "type": "DATE"}}], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-mxXubr5EtgLTWNkD1GZAz", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-lEyKAMRBTs1XlmLFYgPbv", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-2I9GVBtdIenMHXWsYXFEi", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-jsuh2bYsYxQYoP0RN6Ojg", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-MgkWc3B3C9nHHaDhdR2Cw", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-LeRnCx4lYJDutOAvhUiFW", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-45NkyGSPhKaj8IhSr7_KO", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-1yokdhlyhVaK-A81MPJrp", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-7UyWYTa9WjsO8DoBINuH8", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-2fgoybNKGQBTKbbAqZ2ai", "name": "Field", "props": {"componentProps": {"fieldAlias": "expertId", "label": "选择业务专家", "labelField": "expertName", "modelAlias": "TSRM$srm_expert_md", "parentModelAlias": "TSRM$srm_expert_busi_scope_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$srm_expert_md", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "expertName", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "专家姓名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "XVXoE_vCqDzIUP_DcG8Mn", "trigger": "auto", "valueRules": null}], "name": "expertName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$srm_expert_md", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$srm_expert_md", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "RelationSelect", "hidden": true, "label": "业务专家", "name": "expertId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-UkMr_PF_Mft5XGixDTjC5", "name": "Field", "props": {"componentProps": {"fieldAlias": "busiScope", "modelAlias": "TSRM$srm_expert_busi_scope_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_busi_scope_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_busi_scope_md"}, "hidden": false, "label": "业务范围", "name": "busiScope", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-TX8g94bO4NXMME9qKBQvx", "name": "Field", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "TSRM$srm_expert_busi_scope_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_busi_scope_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_busi_scope_md"}, "hidden": false, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-0jCRXNH8m7Ir093dIcyMV", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-G9oB9jKZP__x_faU1JCjG", "name": "Table", "props": {"enableSolution": false, "fieldName": "busiScope", "flow": {"context$": "$context", "name": "busiScope", "type": "RelationData"}, "label": "表格", "mode": "simple", "modelAlias": "TSRM$srm_expert_busi_scope_md"}, "type": "Container"}], "key": "TSRM$SRM_MGMT_VIEW-RRQrlcqzlGd_93MzcapSA", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-LnPgVpWcWBMWWO39zn2gK", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW--Odg8_ZOjt1wkcUNgrRtW", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-vT4pVDNEIhV00DNki-3YH", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-jh_ohJnwXMdQC282OxeS_", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-KG9mX4n0VzK4uNkqF0ktW", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-_QdRta8-Xo0CvdfHZaDZY", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-jXb2X8DN3LAZmZPOP_Och", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-A3UYgRkm4B30gMyGFtvr1", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-O4BTIT3AByFvPPc6M_qrw", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-lLGwYNbPed9hF0TOKCJVI", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-fD1RhDOyrwyUria2de-Zd", "name": "Field", "props": {"componentProps": {"fieldAlias": "expertId", "label": "选择寻源专家", "labelField": "expertName", "modelAlias": "TSRM$srm_expert_md", "parentModelAlias": "TSRM$srm_expert_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$srm_expert_md", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "expertName", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "专家姓名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Kl2n0d3N9zYYTS434oJG8", "trigger": "auto", "valueRules": null}], "name": "expertName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$srm_expert_md", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$srm_expert_md", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "RelationSelect", "hidden": true, "label": "寻源专家", "name": "expertId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-TiWVhO4f3-Ich1IazSxpq", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "matCateId", "label": "选择物料分类id", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "TSRM$srm_expert_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": ["matCateName"], "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCateName", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "类目名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "YOlsmGbpejK-e6twoq9wy", "trigger": "auto", "valueRules": null}], "name": "matCateName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "editComponentType": "RelationSelect", "hidden": false, "label": "分类名称", "modelAlias": "ERP_GEN$gen_mat_cate_md", "name": "matCateId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-kfXA9aA96iygQDJ-b7XL3", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "matCateCode", "modelAlias": "TSRM$srm_expert_mat_cate_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_mat_cate_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_mat_cate_md"}, "hidden": false, "label": "分类编码", "modelAlias": "TSRM$srm_expert_mat_cate_md", "name": "matCateCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-YNGBCwEXJZ6RNVVsrFBFR", "name": "Field", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "TSRM$srm_expert_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_mat_cate_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_mat_cate_md"}, "hidden": false, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-aTIyyAny_T_WvPN7jmfwx", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-tejP17h8dGCtksKh5HZ9L", "name": "Table", "props": {"enableSolution": false, "fieldName": "matCate", "flow": {"context$": "$context", "name": "matCate", "type": "RelationData"}, "label": "表格", "mode": "simple", "modelAlias": "TSRM$srm_expert_mat_cate_md"}, "type": "Container"}], "key": "TSRM$SRM_MGMT_VIEW-eKHjO2uTtnzpvxk4-gCTW", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-xW8yxG6qiYafuj5wIl_qy", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-2z3OJXtDNIHg9fgY4tW1A", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-a7ZI13iDAwlh2FRAKkYLi", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-Rz3RwVH_TSjBNzim-_5ws", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-djdzdw34pOPUM3HyVDTbE", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-8l_5Zss8_CKXRwmFcx3Pe", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-6-0nzkDPJm1QGGUTHc15m", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-8GQeqp9ABP9VJlYXEfKgy", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-Ei1Wpuiz5dP1lW6xl7aTq", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-cNsff4hxiDpleoubAqyWb", "name": "TableTitleActions", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-dkSL80dhPr1fro0dgaT9h", "name": "Table", "props": {"enableSolution": false, "label": "表格", "mode": "simple"}, "type": "Container"}], "key": "TSRM$SRM_MGMT_VIEW-XgfFUPplyghB2C1tRP2l-", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-RCClmyoTQFrfovvwnihuu", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-ATwRnvCheIVp-vip3fIti", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-u15-_oOIMV1M4GXLTPDuU", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-eF0V9-f4FN41H6yK7FO9e", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-k6OgJH09IbErtK7FBb1S2", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-yEzrEpkyInBSPyNJWRpPK", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-S2jYNGWvcCemVEiecepYP", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-2CIcfo6P6cnoSJE7hL6KR", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-7QifELxHetJO6jty6Ojn7", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-pqZ3SluoLk7lPPJxqHTX7", "name": "TableTitleActions", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-xl3IwfXktGmOb2_kV7AHz", "name": "Table", "props": {"enableSolution": false, "label": "表格", "mode": "simple"}, "type": "Container"}], "key": "TSRM$SRM_MGMT_VIEW-9sLM6Eu-rorxw2L0xvGfP", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-ESq_EWQ7oL7dPi6X5YGiZ", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "name": "Tabs", "props": {"items": [{"key": "tGLSU_asnZi_h3-QTweII", "label": "业务范围"}, {"key": "3JEqh4jNq-uUqr8QWHhKi", "label": "负责品类"}, {"key": "d-MDPfK3wlKsENXsrfkI1", "label": "评标记录"}, {"key": "huPV-FHqqR_2nAFSXywLJ", "label": "供应商考评记录"}], "lookup": []}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": "业务信息"}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "name": "Detail", "props": {"flow": {"containerKey": "TSRM$SRM_MGMT_VIEW-detailView-detail", "context$": "$context", "modelAlias": "TSRM$srm_expert_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$srm_expert_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "layout": "horizontal", "modelAlias": "TSRM$srm_expert_md", "saveType": "service"}, "type": "Container"}], "key": "TSRM$SRM_MGMT_VIEW-detail", "name": "Page", "props": {"actionConfigs": [], "backResourceTabProcessConfig": "retain", "collectionService": {"createBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/save"}, "deleteBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/delete"}, "getCurrentSceneBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK"}}, "params": [{"label": "详情ID", "value": "recordId"}], "showBack": true, "showFooter": false, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["寻源专家", "专家编号", "选择更新人", "复制", "选择业务专家", "专家证件类型", "表格", "状态", "评标记录", "类目名称", "备注", "业务专家", "分类编码", "详细地址", "专家类型", "联系电话", "联系邮箱", "地址", "请上传", "推荐方名称", "创建人", "业务范围", "分类名称", "附件", "专家证件号", "专家等级", "请选择", "来源方式", "基本信息", "更新时间", "供应商考评记录", "更新人", "选择关联员工", "所在国家/地区", "编辑", "选择物料分类id", "业务信息", "请输入", "选择创建人", "选择寻源专家", "创建时间", "选择所在国家/地区", "专家姓名", "关联员工", "负责品类", "现任公司名称", "SRM专家库详情", "现任公司统一社会信用代码"], "i18nScanPaths": ["TSRM$SRM_MGMT_VIEW-fD1RhDOyrwyUria2de-Zd.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertType.props.label", "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt.props.label", "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-fD1RhDOyrwyUria2de-Zd.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-UkMr_PF_Mft5XGixDTjC5.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertComName.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertComCreditCode.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-country.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-employeeId.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertComName.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-2fgoybNKGQBTKbbAqZ2ai.props.label", "TSRM$SRM_MGMT_VIEW-TiWVhO4f3-Ich1IazSxpq.props.label", "TSRM$SRM_MGMT_VIEW-kfXA9aA96iygQDJ-b7XL3.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-employeeId.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-fD1RhDOyrwyUria2de-Zd.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-contactPhone.props.componentProps.placeholder", "@exp:TSRM$SRM_MGMT_VIEW-detail-page-title.props.title", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCertNum.props.label", "TSRM$SRM_MGMT_VIEW-fD1RhDOyrwyUria2de-Zd.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-status.props.label", "TSRM$SRM_MGMT_VIEW-TiWVhO4f3-Ich1IazSxpq.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-recommType.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-remark.props.label", "TSRM$SRM_MGMT_VIEW-detail-page-header-edit.props.label", "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV.props.items.3.label", "TSRM$SRM_MGMT_VIEW-TiWVhO4f3-Ich1IazSxpq.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-UkMr_PF_Mft5XGixDTjC5.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCertType.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-country.props.label", "TSRM$SRM_MGMT_VIEW-y3AH6LFiMvVCf8z-VN4BL.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-country.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-TiWVhO4f3-Ich1IazSxpq.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-recommPartyName.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy.props.label", "TSRM$SRM_MGMT_VIEW-2fgoybNKGQBTKbbAqZ2ai.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV.props.items.0.label", "TSRM$SRM_MGMT_VIEW-2fgoybNKGQBTKbbAqZ2ai.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertLevel.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertComCreditCode.props.label", "TSRM$SRM_MGMT_VIEW-tejP17h8dGCtksKh5HZ9L.props.label", "TSRM$SRM_MGMT_VIEW-2fgoybNKGQBTKbbAqZ2ai.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-recommPartyName.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-remark.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq.props.title", "TSRM$SRM_MGMT_VIEW-YNGBCwEXJZ6RNVVsrFBFR.props.label", "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-employeeId.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-kfXA9aA96iygQDJ-b7XL3.props.label", "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV.props.items.2.label", "TSRM$SRM_MGMT_VIEW-fD1RhDOyrwyUria2de-Zd.props.label", "TSRM$SRM_MGMT_VIEW-TiWVhO4f3-Ich1IazSxpq.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-recommType.props.label", "TSRM$SRM_MGMT_VIEW-d6TaGKy_Xl6U6hked7F6N.props.label", "TSRM$SRM_MGMT_VIEW-dkSL80dhPr1fro0dgaT9h.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-contactEmail.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-attachment.props.label", "TSRM$SRM_MGMT_VIEW-G9oB9jKZP__x_faU1JCjG.props.label", "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout.props.title", "TSRM$SRM_MGMT_VIEW-d6TaGKy_Xl6U6hked7F6N.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertLevel.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy.props.label", "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detail-page-header-copy.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertType.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV.props.items.1.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCode.props.label", "TSRM$SRM_MGMT_VIEW-YNGBCwEXJZ6RNVVsrFBFR.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-status.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertName.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-contactEmail.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCertNum.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-y3AH6LFiMvVCf8z-VN4BL.props.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-contactPhone.props.label", "TSRM$SRM_MGMT_VIEW-2fgoybNKGQBTKbbAqZ2ai.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertName.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCertType.props.label", "TSRM$SRM_MGMT_VIEW-TX8g94bO4NXMME9qKBQvx.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCode.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-attachment.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-TX8g94bO4NXMME9qKBQvx.props.label", "TSRM$SRM_MGMT_VIEW-xl3IwfXktGmOb2_kV7AHz.props.label"]}, "key": "TSRM$SRM_MGMT_VIEW:detail", "name": "detail", "permissionKey": "TSRM$SRM_MGMT_VIEW:detail_perm_ac", "resources": [{"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCode", "label": "专家编号", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertName", "label": "专家姓名", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertType", "label": "专家类型", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-employeeId", "label": "关联员工", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCertType", "label": "专家证件类型", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertCert<PERSON>um", "label": "专家证件号", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-contactPhone", "label": "联系电话", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-contactEmail", "label": "联系邮箱", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertLevel", "label": "专家等级", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-recommType", "label": "来源方式", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-recommPartyName", "label": "推荐方名称", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-country", "label": "所在国家/地区", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-y3AH6LFiMvVCf8z-VN4BL", "label": "地址", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_ReverseConstructTreeService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-d6TaGKy_Xl6U6hked7F6N", "label": "详细地址", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertComName", "label": "现任公司名称", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-expertComCreditCode", "label": "现任公司统一社会信用代码", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-attachment", "label": "附件", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-remark", "label": "备注", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout-For-DetailField-status", "label": "状态", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-createdBy", "label": "创建人", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt", "label": "创建时间", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedBy", "label": "更新人", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail-Layout", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detail-page-header-copy", "label": "复制", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$SRM_MGMT_VIEW-JGDcGfYImih6ESFQu-g4p", "label": "按钮组", "type": "ActionsGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-FKXo6l19Hd6fxpRQzikPz", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detail-page-header-edit", "label": "编辑", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$SRM_MGMT_VIEW-JGDcGfYImih6ESFQu-g4p", "label": "按钮组", "type": "ActionsGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-FKXo6l19Hd6fxpRQzikPz", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-detail-TSRM$srm_expert_md-logs", "label": "日志", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TSRM$SRM_MGMT_VIEW-JGDcGfYImih6ESFQu-g4p", "label": "按钮组", "type": "ActionsGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-FKXo6l19Hd6fxpRQzikPz", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-G9oB9jKZP__x_faU1JCjG", "label": "表格", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-LnPgVpWcWBMWWO39zn2gK", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-RRQrlcqzlGd_93MzcapSA", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-tejP17h8dGCtksKh5HZ9L", "label": "表格", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-xW8yxG6qiYafuj5wIl_qy", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-eKHjO2uTtnzpvxk4-gCTW", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-dkSL80dhPr1fro0dgaT9h", "label": "表格", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-RCClmyoTQFrfovvwnihuu", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-XgfFUPplyghB2C1tRP2l-", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-xl3IwfXktGmOb2_kV7AHz", "label": "表格", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-ESq_EWQ7oL7dPi6X5YGiZ", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-9sLM6Eu-rorxw2L0xvGfP", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-2fgoybNKGQBTKbbAqZ2ai", "label": "业务专家", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-LnPgVpWcWBMWWO39zn2gK", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-RRQrlcqzlGd_93MzcapSA", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$SRM_MGMT_VIEW-G9oB9jKZP__x_faU1JCjG", "label": "表格", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-0jCRXNH8m7Ir093dIcyMV", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-UkMr_PF_Mft5XGixDTjC5", "label": "业务范围", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-LnPgVpWcWBMWWO39zn2gK", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-RRQrlcqzlGd_93MzcapSA", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$SRM_MGMT_VIEW-G9oB9jKZP__x_faU1JCjG", "label": "表格", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-0jCRXNH8m7Ir093dIcyMV", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-TX8g94bO4NXMME9qKBQvx", "label": "更新时间", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-LnPgVpWcWBMWWO39zn2gK", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-RRQrlcqzlGd_93MzcapSA", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$SRM_MGMT_VIEW-G9oB9jKZP__x_faU1JCjG", "label": "表格", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-0jCRXNH8m7Ir093dIcyMV", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-fD1RhDOyrwyUria2de-Zd", "label": "寻源专家", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-xW8yxG6qiYafuj5wIl_qy", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-eKHjO2uTtnzpvxk4-gCTW", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$SRM_MGMT_VIEW-tejP17h8dGCtksKh5HZ9L", "label": "表格", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-aTIyyAny_T_WvPN7jmfwx", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-TiWVhO4f3-Ich1IazSxpq", "label": "分类名称", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-xW8yxG6qiYafuj5wIl_qy", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-eKHjO2uTtnzpvxk4-gCTW", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$SRM_MGMT_VIEW-tejP17h8dGCtksKh5HZ9L", "label": "表格", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-aTIyyAny_T_WvPN7jmfwx", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-kfXA9aA96iygQDJ-b7XL3", "label": "分类编码", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-xW8yxG6qiYafuj5wIl_qy", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-eKHjO2uTtnzpvxk4-gCTW", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$SRM_MGMT_VIEW-tejP17h8dGCtksKh5HZ9L", "label": "表格", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-aTIyyAny_T_WvPN7jmfwx", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-YNGBCwEXJZ6RNVVsrFBFR", "label": "更新时间", "path": [{"key": "TSRM$SRM_MGMT_VIEW-detail", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "TSRM$SRM_MGMT_VIEW-OIcbR_225FqBLmWHKJ0Xq", "label": "业务信息", "type": "DetailGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-MGLFrxylxIBB-fprZ3pkV", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-xW8yxG6qiYafuj5wIl_qy", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-eKHjO2uTtnzpvxk4-gCTW", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "TSRM$SRM_MGMT_VIEW-tejP17h8dGCtksKh5HZ9L", "label": "表格", "type": "Table"}, {"key": "TSRM$SRM_MGMT_VIEW-aTIyyAny_T_WvPN7jmfwx", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "detail", "type": "DETAIL"}, "type": "View"}