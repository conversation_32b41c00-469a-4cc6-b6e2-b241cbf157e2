{"access": "Private", "key": "TSRM$SRM_MGMT_VIEW:edit", "name": "edit", "props": {"containerSelect": {"TSRM$SRM_MGMT_VIEW--wieTU0P-8ANi_3q5AI6n": [], "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md": [{"conditionGroup": null, "field": "expertCode", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertName", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "recommType", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertType", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "employeeId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "name", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "expertCertType", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertCert<PERSON>um", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "contactPhone", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "contactEmail", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertLevel", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "recommPartyName", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "addressId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "addrName", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "addressDetail", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertComName", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "expertComCreditCode", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "attachment", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "remark", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "user", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "status", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "version", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "busiScope", "selectFields": [{"conditionGroup": null, "field": "busiScope", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "matCate", "selectFields": [{"conditionGroup": null, "field": "matCateId", "selectFields": [{"conditionGroup": null, "field": "id", "selectFields": null, "sortOrders": null}, {"conditionGroup": null, "field": "matCateName", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"conditionGroup": null, "field": "matCateCode", "selectFields": null, "sortOrders": null}], "sortOrders": null}]}, "content": {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-editView-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" : \"创建\"}}SRM专家库", "useExpression": "true"}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCode", "name": "FormField", "props": {"componentProps": {"fieldAlias": "expertCode", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "InputText", "hidden": false, "label": "专家编号", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "YUZ5b3K1cZ5R3iDZZ7kys", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "专家类型(expertType)", "type": "VarValue", "val": "expertType", "value": "TSRM$srm_expert_md.expertType", "valueType": "VAR", "varVal": "expertType", "varValue": [{"valueKey": "expertType", "valueName": "expertType"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "NMQEdDZxX8EefeJLgwBK3", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "hvYziHiyiV3tKgJhfqiT_", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": true, "required": true}, "key": "H0bxjt92ZZJtwVm9J94_p", "operator": "SERVICE", "valueRules": {"scope": null, "serviceParams": {"entryNewParams": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "value": "TSRM$srm_expert_md"}}, {"elements": [{"fieldAlias": "formParams", "fieldName": "formParams", "fieldType": "Object", "valueConfig": {"action": {"name": "getData", "target": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md"}, "type": "action"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "outputParams": {"expression": "data", "serviceKey": "TSRM$SYS_InvokeCodeRuleService", "type": "expression"}}, "type": "SERVICE", "val": "TSRM$SYS_InvokeCodeRuleService", "value": "TSRM$SYS_InvokeCodeRuleService"}}, {"fieldRules": {"hidden": false, "readOnly": true, "required": true}, "key": "YsR-gtdFZqAZGVuloYHTi", "trigger": "auto", "valueRules": null}], "name": "expertCode", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "expertName", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "InputText", "hidden": false, "label": "专家姓名", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "Fwg7ryE3iwsS10-OUQFd7", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "buzWKktvcgsvAcuMs7B7t", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "eyBWxtzMfXFBtgeRC0Xe9", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "idY9tJtZDDdATmAw6tSH9", "operator": "FIELD", "trigger": "onchange", "valueRules": {"scope": "form", "type": "FIELD", "val": "employeeId.name", "value": "TSRM$srm_expert_md.employeeId.name", "valueType": "model"}}, {"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "NnI0orrmrw-hr20xV0V3x", "valueRules": null}], "name": "expertName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-recommType", "name": "FormField", "props": {"componentProps": {"fieldAlias": "recommType", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "Select", "hidden": false, "label": "来源方式", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "vjfxI15GJt1tx_m8f3AWY", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "flFpVcGRB-y3AKpaets3u", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "LF_0DsV5fi7Rt9dzoIdon", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "YOmRSz4RplnQVv504eLWU", "operator": "FIELD", "trigger": "auto", "valueRules": {"scope": "", "type": "FIELD", "val": "'EMPLOYEE'", "value": "'EMPLOYEE'", "valueType": "expression"}}, {"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "2Olz5JmLXcAmBmj8aGJNg", "trigger": "auto", "valueRules": null}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "CPTKz6yAfg61FaVihLpSj", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID(id)", "type": "VarValue", "val": "id", "value": "TSRM$srm_expert_md.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "R18kK40bQfJ97gdLfwXly", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "EtpY1m1qpAwUNwAKvNfAs", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "GTQNMrh0YNpffc1Zs01L9", "operator": null, "trigger": "auto", "valueRules": null}], "name": "recommType", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertType", "name": "FormField", "props": {"componentProps": {"fieldAlias": "expertType", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "Select", "hidden": false, "label": "专家类型", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "qrAwc6ZjgZkCvf0bxKNxk", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "来源方式(recommType)", "type": "VarValue", "val": "recommType", "value": "TSRM$srm_expert_md.recommType", "valueType": "VAR", "varVal": "recommType", "varValue": [{"valueKey": "recommType", "valueName": "recommType"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "d04JBgHDTjE1S-47DrXnw", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "来源方式(recommType)", "type": "VarValue", "val": "recommType", "value": "TSRM$srm_expert_md.recommType", "valueType": "VAR", "varVal": "recommType", "varValue": [{"valueKey": "recommType", "valueName": "recommType"}]}, "operator": "EQ", "rightValue": {"constValue": "EMPLOYEE", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "5qfNqKqTTrmDcZgeXz5dB", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "CMxU9JqGjdZIY4XLF42t3", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": true, "required": true}, "key": "kgQA6EtIlXwIOHOTsJeil", "operator": "FIELD", "valueRules": {"scope": "", "type": "FIELD", "val": "'INNER'", "value": "'INNER'", "valueType": "expression"}}, {"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "_zmpN9zpwRomd4i9GQLFM", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "来源方式(recommType)", "type": "VarValue", "val": "recommType", "value": "TSRM$srm_expert_md.recommType", "valueType": "VAR", "varVal": "recommType", "varValue": [{"valueKey": "recommType", "valueName": "recommType"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "Io7MXhqW0N7aj64IeEy5O", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "来源方式(recommType)", "type": "VarValue", "val": "recommType", "value": "TSRM$srm_expert_md.recommType", "valueType": "VAR", "varVal": "recommType", "varValue": [{"valueKey": "recommType", "valueName": "recommType"}]}, "operator": "IN", "rightValues": [{"constValue": "SELF_A", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, {"constValue": "COMPANY_C", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, {"constValue": "SELF_C", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}], "type": "ConditionLeaf"}], "id": "ylV3YmBHSuKRImJphR_19", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "KkSs0y7e3UTywK1O3Yj1O", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "CdxvmSIEkSUWOd1X7sjgB", "operator": "FIELD", "trigger": "auto", "valueRules": {"scope": "", "type": "FIELD", "val": "'OUTER'", "value": "'OUTER'", "valueType": "expression"}}, {"fieldRules": {"hidden": false, "readOnly": true, "required": true}, "key": "GppcPJA3PT-AJRKb_wYDl", "trigger": "auto", "valueRules": null}], "name": "expertType", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "employeeId", "label": "选择关联员工", "labelField": "name", "modelAlias": "sys_common$org_employee_md", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"containerKey": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_employee_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"buttonConfig": {}, "fields": [{"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "工号", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "姓名", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "手机", "name": "mobile", "required": true, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "工号", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "姓名", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "手机", "name": "mobile", "required": true, "type": "TEXT", "width": 120}], "findFlow": {"containerKey": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "选择关联员工", "labelField": "name", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_employee_md", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "关联员工", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "keo6FTt_MyqT42AYnBsDg", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "来源方式(recommType)", "type": "VarValue", "val": "recommType", "value": "TSRM$srm_expert_md.recommType", "valueType": "VAR", "varVal": "recommType", "varValue": [{"valueKey": "recommType", "valueName": "recommType"}]}, "operator": "EQ", "rightValue": {"constValue": "EMPLOYEE", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "OQ1s9epyAB-AcpSnABxxR", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "P6dLDCYOoknQthLYrDZyH", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "yPyiwd5Ou3naGDOwjgvqY", "operator": null, "trigger": "auto", "valueRules": null}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "4mGLOi4sjNvU4-S-IxU3F", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "来源方式(recommType)", "type": "VarValue", "val": "recommType", "value": "TSRM$srm_expert_md.recommType", "valueType": "VAR", "varVal": "recommType", "varValue": [{"valueKey": "recommType", "valueName": "recommType"}]}, "operator": "NEQ", "rightValue": {"constValue": "EMPLOYEE", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "lQRU3P3VF5J6GNFKHAZv1", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "BqTcSRPotTINz8b3Duh01", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "AHtaTUMWxbBrzPSKkdps0", "operator": null, "trigger": "auto", "valueRules": null}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "0J7Qym6rHQXiqgqtic9QH", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID(id)", "type": "VarValue", "val": "id", "value": "TSRM$srm_expert_md.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "zcca8XoPcCeWAv9V9XbjB", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "1BE9moReuQ6FA8e5OOLOo", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "84p3WoGhymOY6q9wjnZ7R", "operator": null, "trigger": "auto", "valueRules": null}], "name": "employeeId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCertType", "name": "FormField", "props": {"componentProps": {"fieldAlias": "expertCertType", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md", "options": [{"enabled": true, "isDefault": false, "label": "中国居民身份证", "value": "CN"}, {"enabled": true, "isDefault": false, "label": "港澳居民来往内地通行证,", "value": "HK"}, {"enabled": true, "isDefault": false, "label": "台湾居民来往大陆通行证", "value": "TW"}, {"enabled": true, "isDefault": false, "label": "护照", "value": "PP"}, {"enabled": true, "isDefault": false, "label": "外国人永久居留身份证", "value": "PRC"}, {"enabled": true, "isDefault": false, "label": "港澳台居民居住证", "value": "RPHK"}]}, "editComponentType": "Select", "hidden": false, "label": "专家证件类型", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "_XVM_mX1lfz2AdWujYMru", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "wapjNvZ0cazNgOie8R4gN", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID(id)", "type": "VarValue", "val": "employeeId.id", "value": "TSRM$srm_expert_md.employeeId.id", "valueType": "VAR", "varVal": "employeeId.id", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "7iIwIlz-91kgQhfC-4sYe", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "o0IDJhHEXVnosT4dnd7JN", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "readOnly": true, "required": false}, "key": "nmYq2HkQdHwEjdXdp3hZg", "operator": "FIELD", "trigger": "auto", "valueRules": {"scope": "", "type": "FIELD", "val": "'CN'", "value": "'CN'", "valueType": "expression"}}], "name": "expertCertType", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCertNum", "name": "FormField", "props": {"componentProps": {"fieldAlias": "expertCert<PERSON>um", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "InputText", "hidden": false, "label": "专家证件号", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "dbYEs_HxAhXcq8I4tvFzN", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "b8IARBX4AD0JbiOcKTPmd", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID(id)", "type": "VarValue", "val": "employeeId.id", "value": "TSRM$srm_expert_md.employeeId.id", "valueType": "VAR", "varVal": "employeeId.id", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "pImlKIIczpWRmqwk7kD_x", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "c_k6PnMrM5NjMGOWQ-R33", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "readOnly": true, "required": false}, "key": "fBjiCXZRbMBbD8zFjxKs-", "operator": "FIELD", "trigger": "auto", "valueRules": {"scope": "form", "type": "FIELD", "val": "employeeId.idCard", "value": "TSRM$srm_expert_md.employeeId.idCard", "valueType": "model"}}, {"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "ISw0kxu79dszJhe_s6Emh", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "GGGbU-Zf0uoto9UtvT4gM", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "CN7xQr5OeF2YHHEGjfSlW", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "QXHgYRH0LCKg0lEzagxV2", "operator": "clear", "trigger": "onchange", "valueRules": null}], "name": "expertCert<PERSON>um", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-contactPhone", "name": "FormField", "props": {"componentProps": {"fieldAlias": "contactPhone", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "InputText", "hidden": false, "label": "联系电话", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "qIfetZ3zq5moPR3PGQIey", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "n6Z0vU1x56Ket_d_pPvDv", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID(id)", "type": "VarValue", "val": "employeeId.id", "value": "TSRM$srm_expert_md.employeeId.id", "valueType": "VAR", "varVal": "employeeId.id", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "ubXd9v42oDX0V8I1uDo0O", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "kPaUjNaMj6yoOrwMWcQeD", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "readOnly": true, "required": false}, "key": "3m9-biF8Jhk-HUhK_KjcT", "operator": "FIELD", "trigger": "onchange", "valueRules": {"scope": "form", "type": "FIELD", "val": "employeeId.mobile", "value": "TSRM$srm_expert_md.employeeId.mobile", "valueType": "model"}}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "2xRpqlfaLrwuAcgfeNlZe", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "专家类型(expertType)", "type": "VarValue", "val": "expertType", "value": "TSRM$srm_expert_md.expertType", "valueType": "VAR", "varVal": "expertType", "varValue": [{"valueKey": "expertType", "valueName": "expertType"}]}, "operator": "EQ", "rightValue": {"constValue": "OUTER", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "yaRRAfmn0RjZVsUZ9jWYt", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "EaJgLeD1K67YHg1uZQSuj", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "d1Y1BbK2LYzmd182MBRdX", "operator": null, "trigger": "auto", "valueRules": null}, {"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "QpDdvnyi2Js_o7CqKupkE", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "rYOdZ1Il2RPULnASI7Nwq", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "b2suUZ-DBLluf9bFPRfem", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "hqcle8Bbae9ZJGRP3Yt9s", "operator": "clear", "trigger": "onchange", "valueRules": null}], "name": "contactPhone", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-contactEmail", "name": "FormField", "props": {"componentProps": {"fieldAlias": "contactEmail", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "InputText", "hidden": false, "label": "联系邮箱", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "IRHo63hjMc1rf6OoKFB-9", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "fSLSEtSq4poh56RnS7h85", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID(id)", "type": "VarValue", "val": "employeeId.id", "value": "TSRM$srm_expert_md.employeeId.id", "valueType": "VAR", "varVal": "employeeId.id", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "iQ-w75U4c2yl1N8hh3eyz", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "8j6CYbagvEIdNUR0pe9fl", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "readOnly": true, "required": false}, "key": "4f-X0h3ovsqmjz4L0PW07", "operator": "FIELD", "trigger": "onchange", "valueRules": {"scope": "form", "type": "FIELD", "val": "employeeId.email", "value": "TSRM$srm_expert_md.employeeId.email", "valueType": "model"}}, {"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "8k387Ogosybh_FK5xuhgf", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "gYZoyGfMJu42PzYjqy1Nw", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "XD6JOqHxDUL7e7g1BHYhx", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "YN3KP6e4YW9SVRH1mxSi-", "operator": "clear", "trigger": "onchange", "valueRules": null}], "name": "contactEmail", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertLevel", "name": "FormField", "props": {"componentProps": {"fieldAlias": "expertLevel", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "Select", "hidden": false, "label": "专家等级", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "BuH4Bm9VFoJMQOf4zRcob", "valueRules": null}], "name": "expertLevel", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-recommPartyName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "recommPartyName", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "InputText", "hidden": false, "label": "推荐方名称", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "FrqZZo1scd10kJVJ9jn2n", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "来源方式(recommType)", "type": "VarValue", "val": "recommType", "value": "TSRM$srm_expert_md.recommType", "valueType": "VAR", "varVal": "recommType", "varValue": [{"valueKey": "recommType", "valueName": "recommType"}]}, "operator": "NOT_IN", "rightValues": [{"constValue": "COMPANY_C", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, {"constValue": "SELF_C", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}], "type": "ConditionLeaf"}], "id": "i95NLLNtyZ8tykobCcogP", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "XJO-jl8lg8Fv7yLVda84w", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "-eVQPeQh_7Av0PJh5tDIo", "operator": null, "trigger": "auto", "valueRules": null}], "name": "recommPartyName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-0q7i3RMeaLQo3BTmaZBoj", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressId", "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"containerKey": "TSRM$SRM_MGMT_VIEW-0q7i3RMeaLQo3BTmaZBoj", "context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_addr_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "ERP_GEN$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "addrName", "modelAlias": "ERP_GEN$gen_addr_type_cf", "reverseConstructFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "TSRM$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"allowCopy": false, "buttonConfig": {}, "fields": [{"componentProps": {"fieldAlias": "addrName", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "isRelationColumn": true, "label": "地址库名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "q1Cxxg9g72De6q0OcYbw-", "trigger": "auto", "valueRules": null}], "name": "addrName", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "addrName", "parentModelAlias": "ERP_GEN$gen_addr_type_cf", "placeholder": "请输入"}, "label": "地址库名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "08zdCqhxE4_B9pKiuZTlJ", "trigger": "auto", "valueRules": null}], "name": "addrName", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "TSRM$SRM_MGMT_VIEW-0q7i3RMeaLQo3BTmaZBoj", "context$": "$context", "modelAlias": "ERP_GEN$gen_addr_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_addr_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "ERP_GEN$SYS_FindTreeChildrenDataService", "type": "InvokeSystemService"}, "labelField": ["addrName"], "leafOnly": false, "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_addr_type_cf", "openMode": "focus", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "reverseConstructFlow": {"containerKey": "", "context$": "$context", "modelAlias": "TSRM$srm_expert_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$srm_expert_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "ERP_GEN$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "SelfRelation", "label": "地址", "lookup": [{"conditionGroup": {"conditions": [{"conditions": [{"id": "iZa8A7MCn6r3PfN00FbJv", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "6uUeLArDGzzRGBvsu0ee7", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID(id)", "type": "VarValue", "val": "employeeId.id", "value": "TSRM$srm_expert_md.employeeId.id", "valueType": "VAR", "varVal": "employeeId.id", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "4p7pbfGI_D6lJJImhXc_T", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "18lUtYyazF6b0YvzNm0i4", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": true, "readOnly": true, "required": false}, "key": "_atl8v7uiJ18C-zR0sVdz", "trigger": "auto", "valueRules": null}], "name": "addressId", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-DkaicHfy_vnZV6GKCIoSy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressDetail", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {}, "editComponentType": "InputText", "label": "详细地址", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "laikmkZFsV8t76n_W7hJs", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "MbWG_98HLc5s-L2jS5TQk", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID(id)", "type": "VarValue", "val": "employeeId.id", "value": "TSRM$srm_expert_md.employeeId.id", "valueType": "VAR", "varVal": "employeeId.id", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "BeJqgfHUwnK575sYoebH6", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "kp70ptsMm55IuIJJiNBj2", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": true, "readOnly": true, "required": false}, "key": "B4V_c6JCf0CQQeR5Y-y5U", "operator": "FIELD", "trigger": "auto", "valueRules": {"scope": "form", "type": "FIELD", "val": "employeeId.addressDetail", "value": "TSRM$srm_expert_md.employeeId.addressDetail", "valueType": "model"}}, {"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "YLppU9B2GjSCB0cU7_VBQ", "leftValue": {"fieldType": "Model", "scope": "form", "title": "关联员工(employeeId)", "type": "VarValue", "val": "employeeId", "value": "TSRM$srm_expert_md.employeeId", "valueType": "VAR", "varVal": "employeeId", "varValue": [{"valueKey": "employeeId", "valueName": "employeeId"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "W53lH-zzoPFQAkpyS6AHV", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "jlV8oSA4wb1iDg-JD9w7M", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "YTuWC4DQluevc2QVKa8N1", "operator": "clear", "trigger": "onchange", "valueRules": null}], "name": "addressDetail", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertComName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "expertComName", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "InputText", "hidden": false, "label": "现任公司名称", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "VDRyTBXhrO7ul3go4c2-i", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "来源方式(recommType)", "type": "VarValue", "val": "recommType", "value": "TSRM$srm_expert_md.recommType", "valueType": "VAR", "varVal": "recommType", "varValue": [{"valueKey": "recommType", "valueName": "recommType"}]}, "operator": "EQ", "rightValue": {"constValue": "EMPLOYEE", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "Oa3BButT-VLsZ_nInmtwm", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "si89K9DUhR5ogK-D2R2j8", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "OOZJr1iF_xMo5eGiiJezJ", "operator": null, "trigger": "auto", "valueRules": null}], "name": "expertComName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertComCreditCode", "name": "FormField", "props": {"componentProps": {"fieldAlias": "expertComCreditCode", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "InputText", "hidden": false, "label": "现任公司统一社会信用代码", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "w86pKyoHC9f1KfcQZ6JJK", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "来源方式(recommType)", "type": "VarValue", "val": "recommType", "value": "TSRM$srm_expert_md.recommType", "valueType": "VAR", "varVal": "recommType", "varValue": [{"valueKey": "recommType", "valueName": "recommType"}]}, "operator": "EQ", "rightValue": {"constValue": "EMPLOYEE", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "wa5mjYH_QiMoCDLghm4sl", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "AQxrQ3kHFCLJUa61EvY7X", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "ubdHWVGPxcPXsCCW5ySBR", "operator": null, "trigger": "auto", "valueRules": null}], "name": "expertComCreditCode", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-attachment", "name": "FormField", "props": {"componentProps": {"fieldAlias": "attachment", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请上传"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": false, "label": "附件", "name": "attachment", "rules": [], "type": "ATTACHMENT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-remark", "name": "FormField", "props": {"colSize": 2, "componentProps": {"fieldAlias": "remark", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md", "rows": 3}, "editComponentType": "TextArea", "hidden": false, "label": "备注", "name": "remark", "rules": [], "type": "MULTI_TEXT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-UngviWBxlP12gRj1MuP5j", "name": "FormField", "props": {"componentProps": {"fieldAlias": "user", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"buttonConfig": {}, "fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "isRelationColumn": true, "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "lW9SBGzau8sQvWWEHg-nz", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "lW9SBGzau8sQvWWEHg-nz", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "TSRM$user", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$user"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "选择用户", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "TSRM$user", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "showScope": "all", "tableConditionContext$": null}, "editComponentType": "RelationSelect", "label": "用户", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "3VAfdDHIvEc-_R-L9qnLg", "trigger": "auto", "valueRules": null}], "name": "user", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-status", "name": "FormField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "Select", "hidden": false, "initialValue": "DRAFT", "label": "状态", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "0i9B0c7QVQVVPEhmItgxZ", "valueRules": null}], "name": "status", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "uYCSrVi7LVVfsX93W8lWW", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "editComponentType": "RelationSelect", "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "IvVeJs466NqE61N7sFyHB", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "editComponentType": "RelationSelect", "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-originOrgId", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_md"}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "rules": [], "type": "NUMBER"}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-HfZncSNDQRn8ni_TLVtlO", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-oQyCP_Sl0DIoQGLuDCMOS", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-IMB2ztCd1nZi4FdAGKcXd", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj", "name": "Field", "props": {"componentProps": {"fieldAlias": "expertId", "label": "选择业务专家", "labelField": "expertName", "modelAlias": "TSRM$srm_expert_md", "parentModelAlias": "TSRM$srm_expert_busi_scope_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"containerKey": "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj", "context$": "$context", "modelAlias": "TSRM$srm_expert_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$srm_expert_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": ["expertName"], "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "expertName", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "isRelationColumn": true, "label": "专家姓名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "zeVd7Hrz9i-4BNWW0UX13", "trigger": "auto", "valueRules": null}], "name": "expertName", "type": "TEXT"}], "filterFields": [{"componentProps": {"fieldAlias": "expertName", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "label": "专家姓名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "T5p2b8GPc2_2k3eGlU-OJ", "trigger": "auto", "valueRules": null}], "name": "expertName", "type": "TEXT", "width": 120}], "findFlow": {"containerKey": "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj", "context$": "$context", "modelAlias": "TSRM$srm_expert_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$srm_expert_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "TSRM$srm_expert_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$srm_expert_md"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "选择业务专家", "labelField": ["expertName"], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "TSRM$srm_expert_md", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": true, "label": "业务专家", "name": "expertId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-thDrjzRcVwl3RQXauVLqA", "name": "Field", "props": {"componentProps": {"fieldAlias": "busiScope", "modelAlias": "TSRM$srm_expert_busi_scope_md", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_busi_scope_md"}, "editComponentProps": {"modelAlias": "TSRM$srm_expert_busi_scope_md"}, "hidden": false, "label": "业务范围", "name": "busiScope", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-CQCH6NrOY_l12Bk7HXcBR", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-4R35az-kUhZ37dmYTNNTE", "name": "TableForm", "props": {"creatorPosition": "bottom", "fieldName": "busiScope", "fields": [], "hideCreator": false, "hideDefaultDelete": false, "label": "表格表单", "modelAlias": "TSRM$srm_expert_busi_scope_md", "serialNumberColTitle": "序号", "showSerialNumber": true, "showType": "normal"}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-HxDF7Q_rEVJbFm7rj0vHl", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-8vMXzkt6Xe9rH_yoA4CmU", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW--wieTU0P-8ANi_3q5AI6n", "name": "ModalRelationSelect", "props": {"fields": [{"componentProps": {"fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "label": "类目编码", "name": "matCateCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "label": "类目名称", "name": "matCateName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["counId", "taxType", "taxClassId", "taxClassDesc", "matSlsId", "genVendFinMdId", "genCustFinMdId"], "fieldAlias": "genCounClassMatTaxItemCfId", "label": "选择物料税", "labelField": "taxType", "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "物料税", "name": "genCounClassMatTaxItemCfId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "qualificationsGroupId", "placeholder": "请输入"}, "hidden": true, "label": "关联资质组", "name": "qualificationsGroupId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "isLimitPoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "采购订单是否限制资质", "name": "isLimitPoQualifications", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "isLimitSoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "销售订单是否限制资质", "name": "isLimitSoQualifications", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "isLimitCtQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "合同是否限制资质", "name": "isLimitCtQualifications", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "path", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "label": "类目路径", "name": "path", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "wqMatType", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "物料类型", "name": "wqMatType", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "label": "选择父类目", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "父类目", "name": "mat<PERSON>ate<PERSON><PERSON>nt", "required": false, "type": "SELFRELATION", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "required": true, "type": "NUMBER", "width": 120}], "fillReplaceType": "merge", "fillType": "field", "filterFields": [{"componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "类目名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "22-_qokDoLqq74Wea4ajt", "valueRules": null}], "name": "matCateName", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "类目编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "X3QfztlAVwo28VjsuVhpS", "valueRules": null}], "name": "matCateCode", "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "mode": "multiple", "modelAlias": "ERP_GEN$gen_mat_cate_md", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "relationAlias": "matCateId", "showFilterFields": true, "showScope": "all", "tableAlias": "matCate", "tableCondition": null, "tableConditionContext$": null, "title": "批量添加"}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-A4gs83o4n4wreL48Ywg8z", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-22KX56G_Ekq-QIJXqZCHh", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-aHxjGsfVUR9VC_tS9uNn1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-VV9dxzZU3dBSBeCCsBN6Z", "name": "Field", "props": {"componentProps": {"fieldAlias": "expertId", "label": "选择寻源专家", "labelField": "expertName", "modelAlias": "TSRM$srm_expert_md", "parentModelAlias": "TSRM$srm_expert_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$srm_expert_md", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$srm_expert_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "expertName", "parentModelAlias": "TSRM$srm_expert_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "专家姓名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "XoYu8dodMxh9Rzerdg4cw", "trigger": "auto", "valueRules": null}], "name": "expertName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$srm_expert_md", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$srm_expert_md", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$srm_expert_md"}, "editComponentType": "RelationSelect", "hidden": true, "label": "寻源专家", "name": "expertId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE", "name": "Field", "props": {"componentProps": {"fieldAlias": "matCateId", "label": "选择物料分类id", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "TSRM$srm_expert_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "类目编码", "name": "matCateCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "类目名称", "name": "matCateName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["counId", "taxType", "taxClassId", "taxClassDesc", "matSlsId", "genVendFinMdId", "genCustFinMdId"], "fieldAlias": "genCounClassMatTaxItemCfId", "label": "选择物料税", "labelField": "taxType", "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "物料税", "name": "genCounClassMatTaxItemCfId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "qualificationsGroupId", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "关联资质组", "name": "qualificationsGroupId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "isLimitPoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "采购订单是否限制资质", "name": "isLimitPoQualifications", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "isLimitSoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "销售订单是否限制资质", "name": "isLimitSoQualifications", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "isLimitCtQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "合同是否限制资质", "name": "isLimitCtQualifications", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "path", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "类目路径", "name": "path", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "wqMatType", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "物料类型", "name": "wqMatType", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "label": "选择父类目", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "父类目", "name": "mat<PERSON>ate<PERSON><PERSON>nt", "required": false, "type": "SELFRELATION", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "所属组织", "name": "originOrgId", "required": true, "type": "NUMBER", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "label": "类目编码", "name": "matCateCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "label": "类目名称", "name": "matCateName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["counId", "taxType", "taxClassId", "taxClassDesc", "matSlsId", "genVendFinMdId", "genCustFinMdId"], "fieldAlias": "genCounClassMatTaxItemCfId", "label": "选择物料税", "labelField": "taxType", "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "物料税", "name": "genCounClassMatTaxItemCfId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "qualificationsGroupId", "placeholder": "请输入"}, "hidden": true, "label": "关联资质组", "name": "qualificationsGroupId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "isLimitPoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "采购订单是否限制资质", "name": "isLimitPoQualifications", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "isLimitSoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "销售订单是否限制资质", "name": "isLimitSoQualifications", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "isLimitCtQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "合同是否限制资质", "name": "isLimitCtQualifications", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "path", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "label": "类目路径", "name": "path", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "wqMatType", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "物料类型", "name": "wqMatType", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "label": "选择父类目", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "label": "父类目", "name": "mat<PERSON>ate<PERSON><PERSON>nt", "required": false, "type": "SELFRELATION", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TSRM$SYS_FindDataByIdService", "searchServiceKey": "TSRM$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "required": true, "type": "NUMBER", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "matCateName", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_mat_cate_md", "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "品类名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Ot6kcVYrNK6oP49KIdrQa", "valueRules": null}], "name": "matCateId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-rIwLb0oJk3Au8SIRyU4vD", "name": "Field", "props": {"componentProps": {"fieldAlias": "matCateCode", "modelAlias": "TSRM$srm_expert_mat_cate_md", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "TSRM$srm_expert_mat_cate_md"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "TSRM$srm_expert_mat_cate_md"}, "editComponentType": "InputText", "hidden": false, "label": "品类编码", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "OwH4ZnIw_0OhxxSItgUa-", "operator": "FIELD", "trigger": "onchange", "valueRules": {"scope": "row", "type": "FIELD", "val": "matCateId.matCateCode", "value": "TSRM$srm_expert_mat_cate_md.matCateId.matCateCode", "valueType": "model"}}], "name": "matCateCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-3QyIiFaqDTnCx8FiuSMEr", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-qHvihXfDdSDN4bRCuqwbj", "name": "TableForm", "props": {"fieldName": "matCate", "fields": [], "hideCreator": true, "isCollapseOption": false, "label": "表格表单", "maxHeight": 414, "modelAlias": "TSRM$srm_expert_mat_cate_md", "pagination": {}, "showType": "normal", "subTableConfig": {}}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-9tBXyNodWsLVAmhE3m5lg", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "TSRM$SRM_MGMT_VIEW-Bdj8o5LB_6cccjTYc5WlH", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH", "name": "Tabs", "props": {"items": [{"key": "rALPoyOa06vgiulQc1xxf", "label": "业务范围"}, {"key": "eiBgfnMs2adkiJ3pFkmya", "label": "负责品类"}], "lookup": []}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-0UbN8RJsiDSGoPRgku-q8", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "业务信息"}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "context$": "$context", "modelAlias": "TSRM$srm_expert_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$srm_expert_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TSRM$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "context$": "$context", "modelAlias": "TSRM$srm_expert_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TSRM$srm_expert_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route?.query?.copyId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TSRM$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "TSRM$srm_expert_md"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "TSRM$SRM_MGMT_VIEW-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$SRM_MGMT_VIEW:edit_perm_ac_z_2_0_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "TSRM$srm_expert_md", "valueConfig": {"action": {"name": "getData", "target": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md"}, "type": "action"}}], "service": "TSRM$SRM_EXPERT_SAVE_SERVICE"}, "endLogicOtherConfig": [{"action": "Message", "message": "保存成功！"}, {"action": "OpenView", "openViewConfig": {"page": {"key": "TSRM$SRM_MGMT_VIEW:list", "name": "list", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": "TSRM$SYS_SaveDataService", "type": "expression"}], "refresh": true, "type": "NewPage"}}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$SRM_MGMT_VIEW:edit_perm_ac_z_2_0_0_1", "showCondition": {}, "type": "primary"}, "type": "Widget"}], "key": "TSRM$SRM_MGMT_VIEW-W2eTOEKq588mpgNJKUF2N", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-0mOW0z2I26vcM0fgbGolB", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "TSRM$SRM_MGMT_VIEW-DoZTV_ysyH_E2UHSRoMQC", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-3jbo91Hs8_BrKaCDnHY8a", "name": "ActionsGroup", "props": {"params": []}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TSRM$SRM_MGMT_VIEW-editView", "name": "Page", "props": {"actionConfig": {}, "backResourceTabProcessConfig": "retain", "collectionService": {"createBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/save"}, "deleteBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/delete"}, "getCurrentSceneBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK"}}, "params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "i18nConfig": {"i18nKeySet": ["专家编号", "用户", "专家证件类型", "物料税", "状态", "请输入版本号", "是否叶子节点", "姓名", "保存", "请输入ID", "联系电话", "联系邮箱", "地址", "请上传", "用户名", "SRM专家库", "ID", "创建人", "请输入更新时间", "业务范围", "附件", "专家证件号", "逻辑删除标识", "专家等级", "请选择", "来源方式", "版本号", "基本信息", "更新时间", "编辑", "选择物料分类id", "创建", "所属组织", "父类目", "选择创建人", "选择寻源专家", "工号", "品类名称", "请输入逻辑删除标识", "专家姓名", "保存成功！", "现任公司名称", "寻源专家", "选择更新人", "选择业务专家", "关联资质组", "手机", "类目名称", "备注", "业务专家", "合同是否限制资质", "请输入创建时间", "详细地址", "物料类型", "邮箱", "专家类型", "表格表单", "采购订单是否限制资质", "选择物料税", "类目编码", "推荐方名称", "选择父类目", "类目路径", "销售订单是否限制资质", "品类编码", "更新人", "选择关联员工", "业务信息", "请输入", "取消", "创建时间", "关联员工", "负责品类", "地址库名称", "现任公司统一社会信用代码"], "i18nScanPaths": ["TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.11.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.14.componentProps.label", "TSRM$SRM_MGMT_VIEW-UngviWBxlP12gRj1MuP5j.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.12.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-version.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-recommType.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertLevel.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdBy.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.19.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.5.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.2.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-recommType.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedAt.props.rules.0.message", "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.filterFields.1.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.filterFields.2.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.5.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.18.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.2.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertComCreditCode.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-originOrgId.props.label", "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.16.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-VV9dxzZU3dBSBeCCsBN6Z.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-contactEmail.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-contactPhone.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCertNum.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-contactPhone.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-version.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.16.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.4.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.15.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-0q7i3RMeaLQo3BTmaZBoj.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertType.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.8.label", "TSRM$SRM_MGMT_VIEW-rIwLb0oJk3Au8SIRyU4vD.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedAt.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.17.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-contactEmail.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertName.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.11.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-attachment.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedBy.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.13.componentProps.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.fields.2.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.7.label", "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH.props.items.1.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.9.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.9.componentProps.placeholder", "@exp:TSRM$SRM_MGMT_VIEW-editView-title.props.title", "TSRM$SRM_MGMT_VIEW-UngviWBxlP12gRj1MuP5j.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCertType.props.label", "TSRM$SRM_MGMT_VIEW-UngviWBxlP12gRj1MuP5j.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedBy.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.filterFields.3.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.15.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.1.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdAt.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.6.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-id.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-id.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-status.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.9.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-remark.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.18.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.16.label", "TSRM$SRM_MGMT_VIEW-0q7i3RMeaLQo3BTmaZBoj.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedBy.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.15.label", "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH.props.items.0.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.18.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-0q7i3RMeaLQo3BTmaZBoj.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-id.props.rules.0.message", "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdBy.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.5.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.9.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.16.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-UngviWBxlP12gRj1MuP5j.props.editComponentProps.filterFields.0.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-status.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.8.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.8.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.19.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertLevel.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.14.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.4.componentProps.label", "TSRM$SRM_MGMT_VIEW-VV9dxzZU3dBSBeCCsBN6Z.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.7.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.6.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-originOrgId.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.11.componentProps.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.7.label", "TSRM$SRM_MGMT_VIEW-editView-footer-cancel.props.label", "TSRM$SRM_MGMT_VIEW-0q7i3RMeaLQo3BTmaZBoj.props.editComponentProps.filterFields.0.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertComName.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.17.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.13.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.17.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCertNum.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.17.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.filterFields.2.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.18.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-deleted.props.rules.0.message", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.6.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertType.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertComName.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.filterFields.3.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-VV9dxzZU3dBSBeCCsBN6Z.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.14.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-rIwLb0oJk3Au8SIRyU4vD.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.13.componentProps.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.7.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-deleted.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-recommPartyName.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.2.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.fields.1.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.10.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-remark.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-recommPartyName.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.4.componentProps.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.14.componentProps.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertName.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.3.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.8.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md.props.title", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.19.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.11.componentProps.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.15.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedBy.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.10.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-0q7i3RMeaLQo3BTmaZBoj.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.13.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCertType.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-UngviWBxlP12gRj1MuP5j.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.14.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-DkaicHfy_vnZV6GKCIoSy.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.fields.3.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdAt.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertComCreditCode.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.3.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-deleted.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.14.label", "TSRM$SRM_MGMT_VIEW-0UbN8RJsiDSGoPRgku-q8.props.title", "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.10.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-attachment.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.4.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.4.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-VV9dxzZU3dBSBeCCsBN6Z.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.19.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.filterFields.1.label", "TSRM$SRM_MGMT_VIEW-DkaicHfy_vnZV6GKCIoSy.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.6.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.11.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.3.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.3.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCode.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj.props.editComponentProps.filterFields.0.label", "TSRM$SRM_MGMT_VIEW-qHvihXfDdSDN4bRCuqwbj.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.13.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-version.props.rules.0.message", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.10.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.13.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-4R35az-kUhZ37dmYTNNTE.props.label", "TSRM$SRM_MGMT_VIEW-0q7i3RMeaLQo3BTmaZBoj.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.1.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.11.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-thDrjzRcVwl3RQXauVLqA.props.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.fields.1.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-UngviWBxlP12gRj1MuP5j.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.fields.2.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdAt.props.rules.0.message", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.componentProps.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.1.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-editView-footer-save.props.actionConfig.endLogicOtherConfig.0.message", "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj.props.editComponentProps.filterFields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.5.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.2.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdBy.props.editComponentProps.fields.0.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.12.label", "TSRM$SRM_MGMT_VIEW-thDrjzRcVwl3RQXauVLqA.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-editView-footer-save.props.label", "TSRM$SRM_MGMT_VIEW-VV9dxzZU3dBSBeCCsBN6Z.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.fields.3.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCode.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedAt.props.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdBy.props.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.0.label", "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId.props.editComponentProps.filterFields.0.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.filterFields.12.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.1.componentProps.placeholder", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.4.label", "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE.props.editComponentProps.fields.12.componentProps.placeholder"]}, "key": "TSRM$SRM_MGMT_VIEW:edit", "name": "edit", "permissionKey": "TSRM$SRM_MGMT_VIEW:edit_perm_ac", "resources": [{"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}, {"key": "TSRM$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCode", "label": "专家编号", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_InvokeCodeRuleService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertName", "label": "专家姓名", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-recommType", "label": "来源方式", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertType", "label": "专家类型", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-employeeId", "label": "关联员工", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_employee_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCertType", "label": "专家证件类型", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertCertNum", "label": "专家证件号", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-contactPhone", "label": "联系电话", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-contactEmail", "label": "联系邮箱", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertLevel", "label": "专家等级", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-recommPartyName", "label": "推荐方名称", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-0q7i3RMeaLQo3BTmaZBoj", "label": "地址", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_ReverseConstructTreeService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "TSRM$SYS_ReverseConstructTreeService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindTreeChildrenDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_addr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_ReverseConstructTreeService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-DkaicHfy_vnZV6GKCIoSy", "label": "详细地址", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertComName", "label": "现任公司名称", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-expertComCreditCode", "label": "现任公司统一社会信用代码", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-attachment", "label": "附件", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-remark", "label": "备注", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-UngviWBxlP12gRj1MuP5j", "label": "用户", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-status", "label": "状态", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-id", "label": "ID", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdBy", "label": "创建人", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedBy", "label": "更新人", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-createdAt", "label": "创建时间", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-updatedAt", "label": "更新时间", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-version", "label": "版本号", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-deleted", "label": "逻辑删除标识", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md-for-widget-originOrgId", "label": "所属组织", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-container-TSRM$srm_expert_md", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-editView-footer-cancel", "label": "取消", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TSRM$SRM_MGMT_VIEW-3jbo91Hs8_BrKaCDnHY8a", "label": "按钮组", "type": "ActionsGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-W2eTOEKq588mpgNJKUF2N", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-editView-footer-save", "label": "保存", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TSRM$SRM_MGMT_VIEW-3jbo91Hs8_BrKaCDnHY8a", "label": "按钮组", "type": "ActionsGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-W2eTOEKq588mpgNJKUF2N", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "TSRM$SRM_EXPERT_SAVE_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-4R35az-kUhZ37dmYTNNTE", "label": "表格表单", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-0UbN8RJsiDSGoPRgku-q8", "label": "业务信息", "type": "FormGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-8vMXzkt6Xe9rH_yoA4CmU", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-HxDF7Q_rEVJbFm7rj0vHl", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-qHvihXfDdSDN4bRCuqwbj", "label": "表格表单", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-0UbN8RJsiDSGoPRgku-q8", "label": "业务信息", "type": "FormGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-Bdj8o5LB_6cccjTYc5WlH", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-9tBXyNodWsLVAmhE3m5lg", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-768u5x4oe3dSedvXihHSj", "label": "业务专家", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-0UbN8RJsiDSGoPRgku-q8", "label": "业务信息", "type": "FormGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-8vMXzkt6Xe9rH_yoA4CmU", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-HxDF7Q_rEVJbFm7rj0vHl", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$SRM_MGMT_VIEW-4R35az-kUhZ37dmYTNNTE", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$SRM_MGMT_VIEW-CQCH6NrOY_l12Bk7HXcBR", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-thDrjzRcVwl3RQXauVLqA", "label": "业务范围", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-0UbN8RJsiDSGoPRgku-q8", "label": "业务信息", "type": "FormGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-8vMXzkt6Xe9rH_yoA4CmU", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-HxDF7Q_rEVJbFm7rj0vHl", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$SRM_MGMT_VIEW-4R35az-kUhZ37dmYTNNTE", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$SRM_MGMT_VIEW-CQCH6NrOY_l12Bk7HXcBR", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-VV9dxzZU3dBSBeCCsBN6Z", "label": "寻源专家", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-0UbN8RJsiDSGoPRgku-q8", "label": "业务信息", "type": "FormGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-Bdj8o5LB_6cccjTYc5WlH", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-9tBXyNodWsLVAmhE3m5lg", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$SRM_MGMT_VIEW-qHvihXfDdSDN4bRCuqwbj", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$SRM_MGMT_VIEW-3QyIiFaqDTnCx8FiuSMEr", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$srm_expert_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-iF0dhAd2rb9kaYUifYVeE", "label": "品类名称", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-0UbN8RJsiDSGoPRgku-q8", "label": "业务信息", "type": "FormGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-Bdj8o5LB_6cccjTYc5WlH", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-9tBXyNodWsLVAmhE3m5lg", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$SRM_MGMT_VIEW-qHvihXfDdSDN4bRCuqwbj", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$SRM_MGMT_VIEW-3QyIiFaqDTnCx8FiuSMEr", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW-rIwLb0oJk3Au8SIRyU4vD", "label": "品类编码", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-0UbN8RJsiDSGoPRgku-q8", "label": "业务信息", "type": "FormGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-Bdj8o5LB_6cccjTYc5WlH", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-9tBXyNodWsLVAmhE3m5lg", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$SRM_MGMT_VIEW-qHvihXfDdSDN4bRCuqwbj", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$SRM_MGMT_VIEW-3QyIiFaqDTnCx8FiuSMEr", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TSRM$SRM_MGMT_VIEW--wieTU0P-8ANi_3q5AI6n", "label": "选择关联关系", "path": [{"key": "TSRM$SRM_MGMT_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "TSRM$SRM_MGMT_VIEW-total-config-TSRM$srm_expert_md", "label": "表单组", "type": "FormGroup"}, {"key": "TSRM$SRM_MGMT_VIEW-0UbN8RJsiDSGoPRgku-q8", "label": "业务信息", "type": "FormGroupItem"}, {"key": "TSRM$SRM_MGMT_VIEW-oEIcqJsmChS3rb0k0ymAH", "label": "页签", "type": "Tabs"}, {"key": "TSRM$SRM_MGMT_VIEW-Bdj8o5LB_6cccjTYc5WlH", "label": "页签项", "type": "TabItem"}, {"key": "TSRM$SRM_MGMT_VIEW-9tBXyNodWsLVAmhE3m5lg", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "TSRM$SRM_MGMT_VIEW-qHvihXfDdSDN4bRCuqwbj", "label": "表格表单", "type": "TableForm"}, {"key": "TSRM$SRM_MGMT_VIEW-aHxjGsfVUR9VC_tS9uNn1", "label": "按钮组", "type": "BatchActions"}, {"key": "TSRM$SRM_MGMT_VIEW-A4gs83o4n4wreL48Ywg8z", "label": "按钮组项目", "type": "ActionsGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}], "title": "edit", "type": "FORM"}, "type": "View"}