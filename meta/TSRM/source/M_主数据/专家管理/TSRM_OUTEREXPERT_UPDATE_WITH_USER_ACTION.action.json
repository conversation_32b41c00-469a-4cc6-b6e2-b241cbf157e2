{"access": "Private", "key": "TSRM$TSRM_OUTEREXPERT_UPDATE_WITH_USER_ACTION", "name": "专家管理-更新外部专家同步更新用户", "props": {"bean": "TsrmExpertAction", "desc": null, "groovyScript": null, "languageType": "Java", "method": "outerExpertUpdateWithUser", "order": 10, "requestType": "io.terminus.tsrm.md.spi.model.expert.dto.SrmExpertMdDTO", "responseType": "java.lang.Void", "returnModel": null, "status": "enabled"}, "type": "Action"}