{"access": "Private", "key": "TSRM$SRM_EXPERT_MD_GEI_EXPORT_SERVICE", "name": "SRM专家库标准导出服务", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1hvm9kn791", "name": "开始", "props": {"globalVariable": null, "input": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null}, {"elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, {"element": null, "fieldAlias": "selectFields", "fieldKey": "selectFields", "fieldName": "selectFields", "fieldType": "Array", "id": null}], "output": [{"elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1hvm9kqja3", "name": "标准导出", "props": {"implementation": "standardExportService", "implementationMethod": "exportData", "implementationType": "spring", "input": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null}, {"elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, {"element": null, "fieldAlias": "selectFields", "fieldKey": "selectFields", "fieldName": "selectFields", "fieldType": "Array", "id": null}], "inputMapping": [{"field": {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null}, "id": null, "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "<PERSON><PERSON><PERSON>", "valueName": "<PERSON><PERSON><PERSON>"}]}}, {"field": {"elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}]}}, {"field": {"element": null, "fieldAlias": "selectFields", "fieldKey": "selectFields", "fieldName": "selectFields", "fieldType": "Array", "id": null}, "id": null, "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "selectFields", "valueName": "selectFields"}]}}], "output": [{"elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Paging", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1i0561eie26", "operator": "EQ", "value": {"fieldType": "Paging", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hvm9kqja3", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "type": "GenericProperties"}, "type": "GenericNode"}, {"desc": null, "id": null, "key": "node_1hvm9kn792", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null}, {"elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, {"element": null, "fieldAlias": "selectFields", "fieldKey": "selectFields", "fieldName": "selectFields", "fieldType": "Array", "id": null}], "key": "TSRM$SRM_EXPERT_MD_GEI_EXPORT_SERVICE", "name": "SRM专家库标准导出服务", "output": [{"elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": null, "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}