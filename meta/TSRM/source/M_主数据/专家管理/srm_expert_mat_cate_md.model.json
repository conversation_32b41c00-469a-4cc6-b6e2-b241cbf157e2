{"access": "Private", "description": "SRM专家可负责物料类目范围", "key": "TSRM$srm_expert_mat_cate_md", "name": "SRM专家可负责物料类目范围", "props": {"alias": "TSRM$srm_expert_mat_cate_md", "children": [{"alias": "expertId", "appId": 185718, "ext": false, "key": "expert_id", "name": "寻源专家", "props": {"autoGenerated": false, "columnName": "expert_id", "comment": "寻源专家", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_mat_cate_md", "currentModelFieldAlias": "expertId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$srm_expert_md", "relationModelKey": "TSRM$srm_expert_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "matCateId", "appId": 185718, "ext": false, "key": "mat_cate_id", "name": "物料分类id", "props": {"autoGenerated": false, "columnName": "mat_cate_id", "comment": "物料分类id", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_mat_cate_md", "currentModelFieldAlias": "matCateId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_GEN$gen_mat_cate_md", "relationModelKey": "ERP_GEN$gen_mat_cate_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "matCateCode", "appId": 185718, "ext": false, "key": "mat_cate_code", "name": "物料分类code", "props": {"autoGenerated": false, "columnName": "mat_cate_code", "comment": "物料分类code", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 64, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "id", "appId": 185718, "ext": false, "key": "id", "name": "ID", "props": {"autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "created<PERSON>y", "appId": 185718, "ext": false, "key": "created_by", "name": "创建人", "props": {"autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_mat_cate_md", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "updatedBy", "appId": 185718, "ext": false, "key": "updated_by", "name": "更新人", "props": {"autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_mat_cate_md", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "createdAt", "appId": 185718, "ext": false, "key": "created_at", "name": "创建时间", "props": {"autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "updatedAt", "appId": 185718, "ext": false, "key": "updated_at", "name": "更新时间", "props": {"autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "version", "appId": 185718, "ext": false, "key": "version", "name": "版本号", "props": {"autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "deleted", "appId": 185718, "ext": false, "key": "deleted", "name": "逻辑删除标识", "props": {"autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "originOrgId", "appId": 185718, "ext": false, "key": "origin_org_id", "name": "所属组织", "props": {"autoGenerated": false, "columnName": "origin_org_id", "comment": "所属组织", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": false, "unique": true}, "teamId": 22, "type": "DataStructField"}], "desc": null, "props": {"config": {"persist": false, "self": false, "selfRelationFieldAlias": null, "system": false}, "mainField": "mat_cate_code", "mainFieldAlias": "matCateCode", "originOrgIdEnabled": true, "physicalDelete": false, "searchModel": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "tableName": "srm_expert_mat_cate_md", "type": "PERSIST"}}, "type": "Model", "validations": [{"access": "Private", "key": "TSRM$srm_expert_mat_cate_md:b05e3cbc-f564-4fb9-954a-88ac1148df76", "name": "字段必填&长度校验", "props": {"defaultValidation": true, "fieldValidations": [{"constraints": [{"constraintType": "SIZE", "max": 64, "min": 0}], "field": "matCateCode", "fieldType": "TEXT", "message": "[默认校验规则]物料分类code的长度不能超过:64", "regexp": null, "required": false}], "links": null}, "type": "Validation"}]}