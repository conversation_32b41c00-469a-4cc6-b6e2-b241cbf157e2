{"access": "Private", "description": "{}", "key": "TSRM$SRM_DISABLE_EXPERT_SERVICE", "name": "停用专家", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1igbbt9f813", "name": "开始", "nextNodeKey": "node_1igbbtsmb15", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}}], "output": null, "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1igbbtsmb15", "name": "调用扩展服务", "nextNodeKey": "node_1igbbt9f814", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(TSRM$srm_expert_md)request", "implementation": "TSRM$TSRM_EXPERT_DISABLE_ACTION", "implementationName": "专家管理-停用专家", "newAction": true, "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_1igbbt9f814", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}}], "key": "TSRM$SRM_DISABLE_EXPERT_SERVICE", "name": "停用专家", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$SRM_DISABLE_EXPERT_SERVICE_service_perm_ac_214afb", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}