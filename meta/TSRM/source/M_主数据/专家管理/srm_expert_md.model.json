{"access": "Private", "description": "SRM_专家库", "key": "TSRM$srm_expert_md", "name": "SRM专家库", "props": {"alias": "TSRM$srm_expert_md", "children": [{"alias": "expertCode", "key": "expert_code", "name": "专家编号", "props": {"autoGenerated": false, "columnName": "expert_code", "comment": "专家编号", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 64, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "expertName", "key": "expert_name", "name": "专家姓名", "props": {"autoGenerated": false, "columnName": "expert_name", "comment": "专家姓名", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 64, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "expertType", "key": "expert_type", "name": "专家类型", "props": {"autoGenerated": false, "columnName": "expert_type", "comment": "专家类型", "compositeKey": false, "dictPros": {"dictValues": [{"label": "内部专家", "value": "INNER"}, {"label": "外部专家", "value": "OUTER"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "employeeId", "key": "employee_id", "name": "关联员工", "props": {"autoGenerated": false, "columnName": "employee_id", "comment": "关联员工", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_md", "currentModelFieldAlias": "employeeId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "sys_common$org_employee_md", "relationModelKey": "sys_common$org_employee_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "expertCertType", "key": "expert_cert_type", "name": "专家证件类型", "props": {"autoGenerated": false, "columnName": "expert_cert_type", "comment": "专家证件类型", "compositeKey": false, "dictPros": {"dictValues": [{"label": "中国居民身份证", "value": "CN"}, {"label": "港澳居民来往内地通行证,", "value": "HK"}, {"label": "台湾居民来往大陆通行证", "value": "TW"}, {"label": "护照", "value": "PP"}, {"label": "外国人永久居留身份证", "value": "PRC"}, {"label": "港澳台居民居住证", "value": "RPHK"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "expertCert<PERSON>um", "key": "expert_cert_num", "name": "专家证件号", "props": {"autoGenerated": false, "columnName": "expert_cert_num", "comment": "专家证件号", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 64, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "contactPhone", "key": "contact_phone", "name": "联系电话", "props": {"autoGenerated": false, "columnName": "contact_phone", "comment": "联系电话", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 64, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "contactEmail", "key": "contact_email", "name": "联系邮箱", "props": {"autoGenerated": false, "columnName": "contact_email", "comment": "联系邮箱", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 64, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "expertLevel", "key": "expert_level", "name": "专家等级", "props": {"autoGenerated": false, "columnName": "expert_level", "comment": "专家等级", "compositeKey": false, "dictPros": {"dictValues": [{"_row_id_": "JUNIOR", "label": "初级", "value": "JUNIOR"}, {"_row_id_": "3JXXcOU", "label": "中级", "value": "INTER"}, {"_row_id_": "QNnBOLN", "label": "高级", "value": "SENIOR"}, {"_row_id_": "ELDER", "label": "资深", "value": "ELDER"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "recommType", "key": "recomm_type", "name": "来源方式", "props": {"autoGenerated": false, "columnName": "recomm_type", "comment": "来源方式", "compositeKey": false, "dictPros": {"dictValues": [{"_row_id_": "SELF_A", "label": "个人申请", "value": "SELF_A"}, {"_row_id_": "COMPANY_C", "label": "单位推荐", "value": "COMPANY_C"}, {"_row_id_": "SELF_C", "label": "个人推荐", "value": "SELF_C"}, {"_row_id_": "2652sAM", "label": "内部员工", "value": "EMPLOYEE"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "recommPartyName", "key": "recomm_party_name", "name": "推荐方名称", "props": {"autoGenerated": false, "columnName": "recomm_party_name", "comment": "推荐方名称", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 128, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "country", "key": "country", "name": "所在国家/地区", "props": {"autoGenerated": false, "columnName": "country", "comment": "所在国家/地区", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_md", "currentModelFieldAlias": "country", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_GEN$gen_coun_type_cf", "relationModelKey": "ERP_GEN$gen_coun_type_cf", "relationType": "LINK", "sync": false}, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "expertComName", "key": "expert_com_name", "name": "现任公司名称", "props": {"autoGenerated": false, "columnName": "expert_com_name", "comment": "现任公司名称", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 128, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "expertComCreditCode", "key": "expert_com_credit_code", "name": "现任公司统一社会信用代码", "props": {"autoGenerated": false, "columnName": "expert_com_credit_code", "comment": "现任公司统一社会信用代码", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 64, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "attachment", "key": "attachment", "name": "附件", "props": {"attachmentProps": {"multi": false}, "autoGenerated": false, "columnName": "attachment", "comment": "附件", "compositeKey": false, "encrypted": false, "fieldType": "ATTACHMENT", "isSystemField": false, "length": 4000, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "remark", "key": "remark", "name": "备注", "props": {"autoGenerated": false, "columnName": "remark", "comment": "备注", "compositeKey": false, "encrypted": false, "fieldType": "MULTI_TEXT", "isSystemField": false, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "status", "key": "status", "name": "状态", "props": {"autoGenerated": false, "columnName": "status", "comment": "状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "启用", "value": "ENABLED"}, {"label": "停用", "value": "DISABLED"}, {"label": "待启用", "value": "DRAFT"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "busiScope", "key": "busi_scope", "name": "负责业务范围", "props": {"autoGenerated": false, "columnName": "busi_scope", "comment": "负责业务范围", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_md", "currentModelFieldAlias": "busiScope", "linkModelAlias": "TSRM$srm_expert_busi_scope_md", "linkModelFieldAlias": "expertId", "relationKey": null, "relationModelAlias": "TSRM$srm_expert_busi_scope_md", "relationModelKey": "TSRM$srm_expert_busi_scope_md", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "matCate", "key": "mat_cate", "name": "负责物料类目", "props": {"autoGenerated": false, "columnName": "mat_cate", "comment": "负责物料类目", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_md", "currentModelFieldAlias": "matCate", "linkModelAlias": "TSRM$srm_expert_mat_cate_md", "linkModelFieldAlias": "expertId", "relationKey": null, "relationModelAlias": "TSRM$srm_expert_mat_cate_md", "relationModelKey": "TSRM$srm_expert_mat_cate_md", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "addressDetail", "key": "address_detail", "name": "详细地址", "props": {"autoGenerated": false, "columnName": "address_detail", "comment": "详细地址", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "addressId", "key": "address_id", "name": "地址", "props": {"autoGenerated": false, "columnName": "address_id", "comment": "地址", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_md", "currentModelFieldAlias": "addressId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_GEN$gen_addr_type_cf", "relationModelKey": "ERP_GEN$gen_addr_type_cf", "relationType": "LINK", "sync": false}, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "user", "key": "user", "name": "用户", "props": {"autoGenerated": false, "columnName": "user", "comment": "用户", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_md", "currentModelFieldAlias": "user", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "id", "key": "id", "name": "ID", "props": {"autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "type": "DataStructField"}, {"alias": "created<PERSON>y", "key": "created_by", "name": "创建人", "props": {"autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_md", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "type": "DataStructField"}, {"alias": "updatedBy", "key": "updated_by", "name": "更新人", "props": {"autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_md", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "type": "DataStructField"}, {"alias": "createdAt", "key": "created_at", "name": "创建时间", "props": {"autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "type": "DataStructField"}, {"alias": "updatedAt", "key": "updated_at", "name": "更新时间", "props": {"autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "type": "DataStructField"}, {"alias": "version", "key": "version", "name": "版本号", "props": {"autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "type": "DataStructField"}, {"alias": "deleted", "key": "deleted", "name": "逻辑删除标识", "props": {"autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "type": "DataStructField"}, {"alias": "originOrgId", "key": "origin_org_id", "name": "所属组织", "props": {"autoGenerated": false, "columnName": "origin_org_id", "comment": "所属组织", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": false, "unique": true}, "type": "DataStructField"}], "desc": null, "props": {"config": {"persist": false, "self": false, "selfRelationFieldAlias": null, "system": false}, "mainField": "expert_name", "mainFieldAlias": "expertName", "orderNumberEnabled": false, "originOrgIdEnabled": true, "physicalDelete": false, "searchModel": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "tableName": "srm_expert_md", "type": "PERSIST"}}, "type": "Model", "validations": [{"access": "Private", "key": "TSRM$srm_expert_md:d47fef19-2dc8-4c52-911b-3a87ecd4e9a2", "name": "字段必填&长度校验", "props": {"defaultValidation": true, "fieldValidations": [{"constraints": [{"constraintType": "SIZE", "max": 64, "min": 0}], "field": "expertCode", "fieldType": "TEXT", "message": "[默认校验规则]专家编号的长度不能超过:64", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 64, "min": 0}], "field": "expertName", "fieldType": "TEXT", "message": "[默认校验规则]专家姓名的长度不能超过:64", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 256, "min": 0}], "field": "expertType", "fieldType": "ENUM", "message": "[默认校验规则]专家类型的长度不能超过:256", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 64, "min": 0}], "field": "srcChannel", "fieldType": "TEXT", "message": "[默认校验规则]来源渠道的长度不能超过:64", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 256, "min": 0}], "field": "expertCertType", "fieldType": "ENUM", "message": "[默认校验规则]专家证件类型的长度不能超过:256", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 64, "min": 0}], "field": "expertCert<PERSON>um", "fieldType": "TEXT", "message": "[默认校验规则]专家证件号的长度不能超过:64", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 64, "min": 0}], "field": "contactPhone", "fieldType": "TEXT", "message": "[默认校验规则]联系电话的长度不能超过:64", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 64, "min": 0}], "field": "contactEmail", "fieldType": "TEXT", "message": "[默认校验规则]联系邮箱的长度不能超过:64", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 256, "min": 0}], "field": "expertLevel", "fieldType": "ENUM", "message": "[默认校验规则]专家等级的长度不能超过:256", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 256, "min": 0}], "field": "recommType", "fieldType": "ENUM", "message": "[默认校验规则]入库方式的长度不能超过:256", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 128, "min": 0}], "field": "recommPartyName", "fieldType": "TEXT", "message": "[默认校验规则]推荐方名称的长度不能超过:128", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 128, "min": 0}], "field": "expertComName", "fieldType": "TEXT", "message": "[默认校验规则]现任公司名称的长度不能超过:128", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 64, "min": 0}], "field": "expertComCreditCode", "fieldType": "TEXT", "message": "[默认校验规则]现任公司统一社会信用代码的长度不能超过:64", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 4000, "min": 0}], "field": "attachment", "fieldType": "ATTACHMENT", "message": "[默认校验规则]附件的长度不能超过:4000", "regexp": null, "required": false}, {"constraints": [{"constraintType": "SIZE", "max": 256, "min": 0}], "field": "status", "fieldType": "ENUM", "message": "[默认校验规则]状态的长度不能超过:256", "regexp": null, "required": false}], "links": null}, "type": "Validation"}]}