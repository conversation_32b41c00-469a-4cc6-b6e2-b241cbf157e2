{"access": "Private", "description": "SRM专家可参与业务范围", "key": "TSRM$srm_expert_busi_scope_md", "name": "SRM专家可参与业务范围", "props": {"alias": "TSRM$srm_expert_busi_scope_md", "children": [{"alias": "expertId", "key": "expert_id", "name": "业务专家", "props": {"autoGenerated": false, "columnName": "expert_id", "comment": "业务专家", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_busi_scope_md", "currentModelFieldAlias": "expertId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$srm_expert_md", "relationModelKey": "TSRM$srm_expert_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "busiScope", "key": "busi_scope", "name": "业务范围", "props": {"autoGenerated": false, "columnName": "busi_scope", "comment": "业务范围", "compositeKey": false, "dictPros": {"dictValues": [{"_row_id_": "BID_BUSI", "label": "商务评标", "value": "BID_BUSI"}, {"_row_id_": "BID_TECH", "label": "技术评标", "value": "BID_TECH"}, {"_row_id_": "VEND_INSPECT", "label": "供应商考察", "value": "VEND_INSPECT"}, {"_row_id_": "VEND_EVAL", "label": "供应商考评", "value": "VEND_EVAL"}, {"_row_id_": "INQIURY", "label": "询比价", "value": "INQIURY"}, {"_row_id_": "NEGOTIATION", "label": "商务谈判", "value": "NEGOTIATION"}, {"_row_id_": "7bUSdvz", "label": "谈判小组", "value": "NEGOTIATION_TEAM"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}, {"alias": "id", "key": "id", "name": "ID", "props": {"autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "type": "DataStructField"}, {"alias": "created<PERSON>y", "key": "created_by", "name": "创建人", "props": {"autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_busi_scope_md", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "type": "DataStructField"}, {"alias": "updatedBy", "key": "updated_by", "name": "更新人", "props": {"autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "TSRM$srm_expert_busi_scope_md", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "TSRM$user", "relationModelKey": "TSRM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "type": "DataStructField"}, {"alias": "createdAt", "key": "created_at", "name": "创建时间", "props": {"autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "type": "DataStructField"}, {"alias": "updatedAt", "key": "updated_at", "name": "更新时间", "props": {"autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "type": "DataStructField"}, {"alias": "version", "key": "version", "name": "版本号", "props": {"autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "type": "DataStructField"}, {"alias": "deleted", "key": "deleted", "name": "逻辑删除标识", "props": {"autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "type": "DataStructField"}, {"alias": "originOrgId", "key": "origin_org_id", "name": "所属组织", "props": {"autoGenerated": false, "columnName": "origin_org_id", "comment": "所属组织", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": false, "unique": true}, "type": "DataStructField"}], "desc": null, "props": {"config": {"persist": false, "self": false, "selfRelationFieldAlias": null, "system": false}, "mainField": "expert_id", "mainFieldAlias": "expertId", "orderNumberEnabled": false, "originOrgIdEnabled": true, "physicalDelete": false, "searchModel": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "tableName": "srm_expert_busi_scope_md", "type": "PERSIST"}}, "type": "Model", "validations": [{"access": "Private", "key": "TSRM$srm_expert_busi_scope_md:e32cbad4-100d-4b70-ad0a-6b7802a6bb64", "name": "字段必填&长度校验", "props": {"defaultValidation": true, "fieldValidations": [{"constraints": [{"constraintType": "SIZE", "max": 256, "min": 0}], "field": "busiScope", "fieldType": "ENUM", "message": "[默认校验规则]可履行业务的长度不能超过:256", "regexp": null, "required": false}], "links": null}, "type": "Validation"}]}