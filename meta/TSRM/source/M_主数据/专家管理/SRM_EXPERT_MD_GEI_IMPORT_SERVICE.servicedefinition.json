{"access": "Private", "key": "TSRM$SRM_EXPERT_MD_GEI_IMPORT_SERVICE", "name": "SRM专家库标准导入服务", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1hvm9kn791", "name": "开始", "props": {"globalVariable": null, "input": [{"elements": [{"element": null, "fieldAlias": "sliceData", "fieldKey": "sliceData", "fieldName": "sliceData", "fieldType": "Array", "id": null}, {"elements": [{"fieldAlias": "importMode", "fieldKey": "importMode", "fieldName": "importMode", "fieldType": "Text", "id": null}], "fieldAlias": "context", "fieldKey": "context", "fieldName": "context", "fieldType": "Object", "id": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}], "output": [{"element": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1hvm9kqja3", "name": "标准导入", "props": {"implementation": "standardImportService", "implementationMethod": "importData", "implementationType": "spring", "input": [{"element": null, "fieldAlias": "sliceData", "fieldKey": "sliceData", "fieldName": "sliceData", "fieldType": "Array", "id": null}, {"elements": [{"fieldAlias": "importMode", "fieldKey": "importMode", "fieldName": "importMode", "fieldType": "Text", "id": null}], "fieldAlias": "context", "fieldKey": "context", "fieldName": "context", "fieldType": "Object", "id": null}], "inputMapping": [{"field": {"element": null, "fieldAlias": "sliceData", "fieldKey": "sliceData", "fieldName": "sliceData", "fieldType": "Array", "id": null}, "id": null, "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "valueKey": "sliceData", "valueName": "sliceData"}]}}, {"field": {"elements": null, "fieldAlias": "context", "fieldKey": "context", "fieldName": "context", "fieldType": "Object", "id": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "valueKey": "context", "valueName": "context"}]}}], "output": [{"element": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1i0561eie26", "operator": "EQ", "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hvm9kqja3", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "type": "GenericProperties"}, "type": "GenericNode"}, {"desc": null, "id": null, "key": "node_1hvm9kn792", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"elements": [{"element": null, "fieldAlias": "sliceData", "fieldKey": "sliceData", "fieldName": "sliceData", "fieldType": "Array", "id": null}, {"elements": [{"fieldAlias": "importMode", "fieldKey": "importMode", "fieldName": "importMode", "fieldType": "Text", "id": null}], "fieldAlias": "context", "fieldKey": "context", "fieldName": "context", "fieldType": "Object", "id": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}], "key": "TSRM$SRM_EXPERT_MD_GEI_IMPORT_SERVICE", "name": "SRM专家库标准导入服务", "output": [{"element": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": null, "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}