{"access": "Private", "description": "{}", "key": "TSRM$PAGING_SRM_EXPERT_SERV", "name": "TSRM_分页查询专家库", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": "null", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1h9a9hm2n23", "name": "开始", "nextNodeKey": "node_1ibtgk39k26", "props": {"globalVariable": [{"element": {"element": {"fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null}, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Array", "id": null}, "fieldAlias": "idArr", "fieldKey": "idArr", "fieldName": "专家ID聚合数组", "fieldType": "Array", "id": null}, {"element": {"fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null}, "fieldAlias": "finalIds", "fieldKey": "finalIds", "fieldName": "专家ID", "fieldType": "Array", "id": null}], "input": [{"elements": [{"elements": [{"elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "id": null}, {"elements": null, "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "id": null}, {"element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null}, {"fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null}, {"fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "id": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, {"fieldAlias": "busi_scope", "fieldKey": "busi_scope", "fieldName": "负责业务", "fieldType": "Text", "id": null, "required": false}], "output": [{"elements": [{"fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null}, {"element": {"fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "SRM专家库"}}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1ibtgk39k26", "name": "idArr初始化", "nextNodeKey": "node_1ibtf5b2r1", "props": {"assignments": [{"field": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "idArr", "valueName": "专家ID聚合数组"}]}, "id": "1ibtgk4gv27", "operator": "EQ", "value": {"fieldType": null, "funcExpression": "new ArrayList()\n", "id": null, "type": "FuncValue"}}], "type": "AssignProperties"}, "type": "AssignNode"}, {"children": [{"children": [{"desc": null, "id": null, "key": "node_1ibtf5r9i4", "name": "查询专家-业务范围", "nextNodeKey": "node_1ibtfb2776", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "hjKrunSBjdYPBzd5XH_pC", "key": null, "leftValue": {"fieldType": "Enum", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "TSRM$srm_expert_busi_scope_md", "valueKey": "busiScope", "valueName": "业务范围"}]}, "operator": "EQ", "rightValue": {"fieldType": "Enum", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "busi_scope", "valueName": "负责业务"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "mOdahGKtNEYEreqc-E3um", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "XAjEnijUATNgNBpbp8vKo", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": null, "dataType": "ARRAY", "desensitized": true, "dynamicCondition": null, "maximum": 500, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "queryModelFields": {"allFields": false, "modelKey": "TSRM$srm_expert_busi_scope_md", "queryFields": [{"fieldKey": "expertId", "queryModelFields": {"allFields": false, "modelKey": "TSRM$srm_expert_md", "queryFields": [{"fieldKey": "id"}]}}]}, "relatedModel": {"modelAlias": "TSRM$srm_expert_busi_scope_md", "modelKey": "TSRM$srm_expert_busi_scope_md", "modelName": "SRM专家可参与业务范围"}, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"conditionGroup": null, "fieldKey": "expertId", "modelKey": "TSRM$srm_expert_md", "subQueryRelatedModels": []}], "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"desc": null, "id": null, "key": "node_1ibtfb2776", "name": "收集专家id", "props": {"inputMapping": [{"field": {"element": null, "fieldAlias": "busi_experts", "fieldKey": "busi_experts", "fieldName": "busi_experts", "fieldType": "Array", "id": null}, "id": null, "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_busi_scope_md", "modelKey": "TSRM$srm_expert_busi_scope_md", "modelName": "SRM专家可参与业务范围"}, "valueKey": "NODE_OUTPUT_node_1ibtf5r9i4", "valueName": "[查询专家-业务范围]节点出参"}]}}, {"field": {"element": null, "fieldAlias": "idArr", "fieldKey": "idArr", "fieldName": "idArr", "fieldType": "Array", "id": null}, "id": null, "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "idArr", "valueName": "专家ID聚合数组"}]}}], "language": "JS", "output": [{"element": {"element": {"fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null}, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Array", "id": null}, "fieldAlias": "idArr", "fieldKey": "idArr", "fieldName": "idArr", "fieldType": "Array", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "idArr", "valueName": "专家ID聚合数组"}]}, "id": "1ibtfenf47", "operator": "EQ", "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1ibtfb2776", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "idArr", "valueName": "idArr"}]}}], "outputAssignType": "CUSTOM"}, "script": "        if(busi_experts == null){\n       idArr.push([])\n   }else{\n     idArr.push(busi_experts.map(e => e?.expertId?.id).filter(e => !!e))\n   }\n   return idArr", "scriptEngine": "graalvm", "type": "ScriptProperties"}, "type": "ScriptNode"}], "desc": null, "id": null, "key": "node_1ibtf5b2v2", "name": "负责业务不为空", "nextNodeKey": "node_1ibtf5r9i4", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "9gdU2uZaW9YpWd4xmXqwn", "key": null, "leftValue": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "busi_scope", "valueName": "负责业务"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "De8zBP9jqT0Gap_7PCG40", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "BwNLouap5T-9W9OYrge9V", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ExclusiveConditionProperties"}, "type": "ExclusiveConditionNode"}, {"children": [], "desc": null, "id": null, "key": "node_1ibtf5b2v3", "name": "else", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "desc": null, "id": null, "key": "node_1ibtf5b2r1", "name": "排他分支", "nextNodeKey": "node_1ibtfuapp13", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"children": [{"children": [{"desc": null, "id": null, "key": "node_1ibtfv2oo16", "name": "计算最终的专家Id", "nextNodeKey": "node_1ibtg04uk18", "props": {"inputMapping": [{"field": {"element": null, "fieldAlias": "idArr", "fieldKey": "idArr", "fieldName": "idArr", "fieldType": "Array", "id": null}, "id": null, "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "idArr", "valueName": "专家ID聚合数组"}]}}], "language": "JS", "output": [{"element": {"fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null}, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "ids", "fieldType": "Array", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "finalIds", "valueName": "专家ID"}]}, "id": "1ibtfvqjg17", "operator": "EQ", "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1ibtfv2oo16", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "ids", "valueName": "ids"}]}}], "outputAssignType": "CUSTOM"}, "script": "      if(Array.isArray(idArr) && idArr.length){\n       return idArr.reduce((a, b) => a.filter(c => b.includes(c)))\n    }\n  return []", "scriptEngine": "graalvm", "type": "ScriptProperties"}, "type": "ScriptNode"}, {"children": [{"children": [{"desc": null, "id": null, "key": "node_1ibtg43l023", "name": "追加-1", "props": {"assignments": [{"field": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "finalIds", "valueName": "专家ID"}]}, "id": "1ibtg49qn24", "operator": "ADD", "value": {"constValue": "-1", "fieldType": "Number", "id": null, "type": "ConstValue"}}], "type": "AssignProperties"}, "type": "AssignNode"}], "desc": null, "id": null, "key": "node_1ibtg04ul19", "name": "专家ID条件为空", "nextNodeKey": "node_1ibtg43l023", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "vNauxcDWXx9QSOEhDQLmI", "key": null, "leftValue": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "finalIds", "valueName": "专家ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "WNqtBLFNt7hXyQKj1hDDw", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "qIRWEtDNQxHYCS8-Rm8ep", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ExclusiveConditionProperties"}, "type": "ExclusiveConditionNode"}, {"children": [], "desc": null, "id": null, "key": "node_1ibtg04ul20", "name": "else", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "desc": null, "id": null, "key": "node_1ibtg04uk18", "name": "排他分支", "nextNodeKey": "node_1ibtg2t0l22", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"desc": null, "id": null, "key": "node_1ibtg2t0l22", "name": "分页查询专家", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "l-AyXcpkhsyQIoC7-nQEx", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "TSRM$srm_expert_md", "valueKey": "id", "valueName": "ID"}]}, "operator": "IN", "rightValue": null, "rightValues": [{"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "finalIds", "valueName": "专家ID"}]}], "type": "ConditionLeaf"}], "id": "U0qqeE5c5BMXoeVFiWsTT", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "xUDUAQZnN25CsBnl3p5B-", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": null, "dataType": "PAGING", "desensitized": true, "dynamicCondition": null, "maximum": null, "outputAssign": {"customAssignments": [{"field": {"fieldType": "Paging", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1ibth0a1n28", "operator": "EQ", "value": {"fieldType": "Paging", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1ibtg2t0l22", "valueName": "节点出参"}]}}], "outputAssignType": "CUSTOM"}, "pageable": {"fieldType": "Pageable", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "valueKey": "pageable", "valueName": "pageable"}]}, "queryModelFields": {"allFields": true, "modelKey": "TSRM$srm_expert_md", "queryFields": null}, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "SRM专家库"}, "sortOrders": [{"fieldAlias": "id", "sortType": "DESC"}], "stopWhenDataEmpty": false, "subQueryRelatedModels": [], "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}], "desc": null, "id": null, "key": "node_1ibtfuapq14", "name": "以Id条件过滤", "nextNodeKey": "node_1ibtfv2oo16", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "PCi145hb9qQu5Y5unc22E", "key": null, "leftValue": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "idArr", "valueName": "专家ID聚合数组"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "yxhw1IOyjEO8zM8EH_Owi", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "a3worTP9U9O8AJFJNBCYx", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ExclusiveConditionProperties"}, "type": "ExclusiveConditionNode"}, {"children": [{"desc": null, "id": null, "key": "node_1ibtgbb1r25", "name": "分页查询专家", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "l-AyXcpkhsyQIoC7-nQEx", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "TSRM$srm_expert_md", "valueKey": "id", "valueName": "ID"}]}, "operator": "IN", "rightValue": null, "rightValues": [{"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "finalIds", "valueName": "专家ID"}]}], "type": "ConditionLeaf"}], "id": "U0qqeE5c5BMXoeVFiWsTT", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "xUDUAQZnN25CsBnl3p5B-", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": null, "dataType": "PAGING", "desensitized": true, "dynamicCondition": null, "maximum": null, "outputAssign": null, "pageable": {"fieldType": "Pageable", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "valueKey": "pageable", "valueName": "pageable"}]}, "queryModelFields": {"allFields": true, "modelKey": "TSRM$srm_expert_md", "queryFields": null}, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "SRM专家库"}, "sortOrders": [{"fieldAlias": "id", "sortType": "DESC"}], "stopWhenDataEmpty": false, "subQueryRelatedModels": [], "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}], "desc": null, "id": null, "key": "node_1ibtfuapq15", "name": "else", "nextNodeKey": "node_1ibtgbb1r25", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "desc": null, "id": null, "key": "node_1ibtfuapp13", "name": "排他分支", "nextNodeKey": "node_1h9a9hm2n24", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"desc": null, "id": null, "key": "node_1h9a9hm2n24", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"elements": [{"elements": [{"elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "id": null}, {"elements": null, "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "id": null}, {"element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null}, {"fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null}, {"fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "id": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null}, {"fieldAlias": "busi_scope", "fieldKey": "busi_scope", "fieldName": "负责业务", "fieldType": "Text", "id": null, "required": false}], "key": "TSRM$PAGING_SRM_EXPERT_SERV", "name": "TSRM_分页查询专家库", "output": [{"elements": [{"fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null}, {"element": {"fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "SRM专家库"}}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$SRM_EXPERT_MD_RETRIEVE_PERMISSION", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}