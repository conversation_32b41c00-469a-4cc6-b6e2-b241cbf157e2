{"access": "Private", "description": "{}", "key": "TSRM$SRM_EXPERT_RESERT_PASSWORD", "name": "SRM专家重置密码", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1ifra933e1", "name": "开始", "nextNodeKey": "node_1ifraad1r3", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}}], "output": null, "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1ifraad1r3", "name": "调用扩展服务", "nextNodeKey": "node_1ifra933e2", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(TSRM$srm_expert_md)request", "implementation": "TSRM$TSRM_EXPERT_RESET_PASSWORD_ACTION", "implementationName": "用户管理-专家重置密码", "newAction": true, "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_1ifra933e2", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}}], "key": "TSRM$SRM_EXPERT_RESERT_PASSWORD", "name": "SRM专家重置密码", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$SRM_EXPERT_RESERT_PASSWORD_service_perm_ac_fb84db", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}