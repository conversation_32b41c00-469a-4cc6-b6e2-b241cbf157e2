{"access": "Private", "description": "{}", "key": "TSRM$SRM_EXPERT_SAVE_SERVICE", "name": "SRM新建专家服务", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1ifrlukg96", "name": "开始", "nextNodeKey": "node_1ifrm03ia8", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}}], "output": null, "type": "StartProperties"}, "type": "StartNode"}, {"children": [{"children": [{"desc": null, "id": null, "key": "node_1ifrm0tee11", "name": "赋值", "props": {"assignments": [{"field": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "TSRM$srm_expert_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_addr_type_cf", "modelKey": "ERP_GEN$gen_addr_type_cf", "modelName": "ERP_GEN$gen_addr_type_cf"}, "valueKey": "addressId", "valueName": "地址"}, {"fieldType": null, "modelAlias": "ERP_GEN$gen_addr_type_cf", "valueKey": "id", "valueName": "ID"}]}, "id": "1ifrm0un212", "operator": "EQ", "value": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "TSRM$srm_expert_md", "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "employeeId", "valueName": "关联员工"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "addressId", "valueName": "地址"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}], "desc": null, "id": null, "key": "node_1ifrm03ia9", "name": "条件", "nextNodeKey": "node_1ifrm0tee11", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "FEeZ2bvvsOc6lNV-JRzBZ", "key": null, "leftValue": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "TSRM$srm_expert_md", "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "employeeId", "valueName": "关联员工"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}, {"id": "B1CxP95GF78zX-S9K7bOe", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "TSRM$srm_expert_md", "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "employeeId", "valueName": "关联员工"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}, {"id": "nelB3H4nyDMibs1Mc7Ve4", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "TSRM$srm_expert_md", "relatedModel": {"modelAlias": "sys_common$org_employee_md", "modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md"}, "valueKey": "employeeId", "valueName": "关联员工"}, {"fieldType": null, "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "addressId", "valueName": "地址"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "fGNIvPebEEo98clysqBqk", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "KytynTzkooAPSOwxtuyi5", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ExclusiveConditionProperties"}, "type": "ExclusiveConditionNode"}, {"children": [], "desc": null, "id": null, "key": "node_1ifrm03ia10", "name": "else", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "desc": null, "id": null, "key": "node_1ifrm03ia8", "name": "排他分支", "nextNodeKey": "node_1iftn7oje1", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"children": [{"children": [{"desc": null, "id": null, "key": "node_1ifrok71215", "name": "调用扩展服务", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(TSRM$srm_expert_md)request", "implementation": "TSRM$TSRM_EXPERT_CREATE_WITH_USER_ACTION", "implementationName": "专家管理-新建专家同步创建用户", "newAction": true, "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}], "desc": null, "id": null, "key": "node_1iftn7ojg2", "name": "条件", "nextNodeKey": "node_1ifrok71215", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "cUi6uainZg2cdvXSlWnfo", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "TSRM$srm_expert_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "JdqwiuS0wXUvi4T6NyNMV", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "AaDZFX7n0sNzVF7c7ONYH", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ExclusiveConditionProperties"}, "type": "ExclusiveConditionNode"}, {"children": [{"desc": null, "id": null, "key": "node_1iftn8c524", "name": "更新数据", "nextNodeKey": "node_1iftpsms31", "props": {"conditionGroup": {"conditions": [], "id": "wEwsdGbbHkmT8elqSEwm6", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": null, "modelValue": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}, "valueKey": "request", "valueName": "request"}]}, "outputAssign": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "SRM专家库"}, "type": "CascadeUpdateDataProperties"}, "type": "CascadeUpdateDataNode"}, {"desc": null, "id": null, "key": "node_1iftpsms31", "name": "调用扩展服务", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(TSRM$srm_expert_md)request", "implementation": "TSRM$TSRM_OUTEREXPERT_UPDATE_WITH_USER_ACTION", "implementationName": "专家管理-更新外部专家同步更新用户", "newAction": true, "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}], "desc": null, "id": null, "key": "node_1iftn7ojg3", "name": "else", "nextNodeKey": "node_1iftn8c524", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "desc": null, "id": null, "key": "node_1iftn7oje1", "name": "排他分支", "nextNodeKey": "node_1ifrlukg97", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"desc": null, "id": null, "key": "node_1ifrlukg97", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TSRM$srm_expert_md", "modelKey": "TSRM$srm_expert_md", "modelName": "TSRM$srm_expert_md"}}], "key": "TSRM$SRM_EXPERT_SAVE_SERVICE", "name": "SRM新建专家服务", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$SRM_EXPERT_SAVE_SERVICE_service_perm_ac_4c8756", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}