{"access": "Private", "key": "TSRM$WQ_CAT_SAVE", "name": "类目保存", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcen01e31", "name": "开始", "nextNodeKey": "node_1hcen7n5s6", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "类目", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "relation": null, "required": null}], "name": null, "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "出参", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "relation": null, "required": null}], "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcen7n5s6", "name": "查询数据", "nextNodeKey": "node_1hcen9k7h7", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "psv1wjoHXWVmt_Jdqwnir", "key": "psv1wjoHXWVmt_Jdqwnir", "leftValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": null, "valueKey": "matCateName", "valueName": "类目名称"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "matCateName", "valueName": "类目名称"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "-8d4KvwaakG9MTUlclARD", "key": "-8d4KvwaakG9MTUlclARD", "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": null, "valueKey": "mat<PERSON>ate<PERSON><PERSON>nt", "valueName": "父类目"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "TERP_MIGRATE$gen_mat_cate_md"}, "valueKey": "mat<PERSON>ate<PERSON><PERSON>nt", "valueName": "父类目"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcen9k7h8", "name": "条件", "nextNodeKey": "node_1hcenav1v10", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "CighPwOs0L3qvnMRTIQXR", "key": "CighPwOs0L3qvnMRTIQXR", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "valueKey": "NODE_OUTPUT_node_1hcen7n5s6", "valueName": "[查询数据]节点.output"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcenav1v11", "name": "条件", "nextNodeKey": "node_1hcendbch13", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "MZL4LTaBOqGqzYc-VW9PC", "key": "MZL4LTaBOqGqzYc-VW9PC", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "valueKey": "NODE_OUTPUT_node_1hcen7n5s6", "valueName": "[查询数据]节点.output"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "NEQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcendbch13", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "同一级类目名称不能重复", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcenav1v12", "name": "条件", "nextNodeKey": "node_1hceneo0l14", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "gJAmHgjj2r55BE1gcbL-e", "key": "gJAmHgjj2r55BE1gcbL-e", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "valueKey": "NODE_OUTPUT_node_1hcen7n5s6", "valueName": "[查询数据]节点.output"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hceneo0m15", "name": "条件", "nextNodeKey": "node_1hcenfb9c17", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "xgRxl4bdCN6Hj1rx9yZQA", "key": "xgRxl4bdCN6Hj1rx9yZQA", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcenfb9c17", "name": "更新数据", "nextNodeKey": "node_1hehpcj7r3", "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hehpcj7r3", "name": "赋值", "nextNodeKey": null, "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "data", "valueName": "出参"}]}, "id": "1hehpckqa4", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hceneo0m16", "name": "条件", "nextNodeKey": "node_1hceng18018", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "66QeZ0sQDSP68eFeeVfjX", "key": "66QeZ0sQDSP68eFeeVfjX", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hceng18018", "name": "新增数据", "nextNodeKey": "node_1hehpd7u95", "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}]}, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "type": "CascadeCreateDataProperties"}, "renderType": null, "type": "CascadeCreateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hehpd7u95", "name": "赋值", "nextNodeKey": null, "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "data", "valueName": "出参"}]}, "id": "1hehpd9bs6", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "valueKey": "NODE_OUTPUT_node_1hceng18018", "valueName": "[新增数据]节点.output"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}], "headNodeKeys": ["node_1hceneo0m15", "node_1hceneo0m16"], "id": null, "key": "node_1hceneo0l14", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}], "headNodeKeys": ["node_1hcenav1v11", "node_1hcenav1v12"], "id": null, "key": "node_1hcenav1v10", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcen9k7h9", "name": "条件", "nextNodeKey": "node_1hcenh7tv19", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "qxzRAemf-L1qo5UjhV5Rt", "key": "qxzRAemf-L1qo5UjhV5Rt", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "valueKey": "NODE_OUTPUT_node_1hcen7n5s6", "valueName": "[查询数据]节点.output"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcenh7u020", "name": "条件", "nextNodeKey": "node_1hcenhmn922", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "K9D-u5kkhednUwqN8s4Tx", "key": "K9D-u5kkhednUwqN8s4Tx", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcenhmn922", "name": "更新数据", "nextNodeKey": "node_1hehpdu597", "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hehpdu597", "name": "赋值", "nextNodeKey": null, "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "data", "valueName": "出参"}]}, "id": "1hehpdvhh8", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcenh7u021", "name": "条件", "nextNodeKey": "node_1hcenifin23", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "SkSITb_Hwnm5PTj61cltS", "key": "SkSITb_Hwnm5PTj61cltS", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcenifin23", "name": "新增数据", "nextNodeKey": "node_1hehpeaf19", "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "request", "valueName": "类目"}]}, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "type": "CascadeCreateDataProperties"}, "renderType": null, "type": "CascadeCreateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hehpeaf19", "name": "赋值", "nextNodeKey": null, "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "data", "valueName": "出参"}]}, "id": "1hehpebq810", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "valueKey": "NODE_OUTPUT_node_1hcenifin23", "valueName": "[新增数据]节点.output"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}], "headNodeKeys": ["node_1hcenh7u020", "node_1hcenh7u021"], "id": null, "key": "node_1hcenh7tv19", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}], "headNodeKeys": ["node_1hcen9k7h8", "node_1hcen9k7h9"], "id": null, "key": "node_1hcen9k7h7", "name": "排他分支", "nextNodeKey": "node_1hcen01e32", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hcen01e32", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hcen01e31"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "类目", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$WQ_CAT_SAVE", "name": "类目保存", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "出参", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "relation": null, "required": null}], "props": {"desc": null, "name": null, "permissionKey": "TSRM$WQ_CAT_SAVE_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}