{"access": "Private", "key": "TSRM$WQ_SAVE_COM_CAT", "name": "公司物料库保存", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "key": "node_1hci99s171", "name": "开始", "nextNodeKey": "node_1hci9b0oi3", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "catMat", "fieldKey": "catMat", "fieldName": "公司物料库", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hci9b0oi3", "name": "新增数据", "nextNodeKey": "node_1hci99s172", "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "valueKey": "catMat", "valueName": "公司物料库"}]}, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": "公司物料库表"}, "type": "CascadeCreateDataProperties"}, "renderType": null, "type": "CascadeCreateDataNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hci99s172", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hci99s171"], "input": [{"defaultValue": null, "description": null, "fieldAlias": "catMat", "fieldKey": "catMat", "fieldName": "公司物料库", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$WQ_SAVE_COM_CAT", "name": "公司物料库保存", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$WQ_SAVE_COM_CAT_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}