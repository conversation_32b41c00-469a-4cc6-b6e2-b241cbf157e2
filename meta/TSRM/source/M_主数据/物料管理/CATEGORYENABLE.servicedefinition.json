{"access": "Private", "key": "TSRM$CATEGORYENABLE", "name": "类目启用", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb82pjo61", "name": "开始", "nextNodeKey": "node_1hb83ppr93", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "id", "fieldKey": "id", "fieldName": "类目id", "fieldType": "Number", "id": null, "required": null}], "name": null, "output": [{"defaultValue": null, "description": null, "fieldAlias": "category", "fieldKey": "category", "fieldName": "类目", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "relation": null, "required": null}], "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb83ppr93", "name": "查询数据", "nextNodeKey": "node_1hb83rufp4", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "SyxWIpsxVmYgdkzlK_4Vp", "key": "SyxWIpsxVmYgdkzlK_4Vp", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "类目id"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "category", "valueName": "类目"}]}, "id": "1hb856u7712", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "valueKey": "NODE_OUTPUT_node_1hb83ppr93", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": true, "subQueryRelatedModels": [{"conditionGroup": null, "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "subQueryRelatedModels": [{"conditionGroup": null, "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "subQueryRelatedModels": []}]}], "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb83rufq5", "name": "条件", "nextNodeKey": "node_1hb8ap4i44", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "T1f-u4_peUQHsefUbXf9l", "key": "T1f-u4_peUQHsefUbXf9l", "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "category", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "TERP_MIGRATE$gen_mat_cate_md"}, "valueKey": "mat<PERSON>ate<PERSON><PERSON>nt", "valueName": "父类目"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb8ap4i45", "name": "条件", "nextNodeKey": "node_1hb83urdg8", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "5OfkNh1BNmYACslUatpn5", "key": "5OfkNh1BNmYACslUatpn5", "leftValue": {"constValue": null, "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "category", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "TERP_MIGRATE$gen_mat_cate_md"}, "valueKey": "mat<PERSON>ate<PERSON><PERSON>nt", "valueName": "父类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "status", "valueName": "状态"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb83urdg8", "name": "异常错误", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "errorCode": "COMMON_2B$common_err", "errorMsg": "${errorMessage}", "link": "MetaLink$ErrorCode$COMMON_2B$common_err", "name": null, "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"constValue": "当前节点父类为停用状态不可启用", "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "type": "ErrorProperties"}, "renderType": null, "type": "ErrorNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb8ap4i46", "name": "条件", "nextNodeKey": "node_1hb8b19b37", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "1ssUZVG19D_0-Drefbhpe", "key": "1ssUZVG19D_0-Drefbhpe", "leftValue": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "category", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "TERP_MIGRATE$gen_mat_cate_md"}, "valueKey": "mat<PERSON>ate<PERSON><PERSON>nt", "valueName": "父类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb8b19b37", "name": "赋值", "nextNodeKey": "node_1hb8b2d8v9", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "category", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": null, "valueKey": "status", "valueName": "状态"}]}, "id": "1hb8b1cnd8", "operator": "EQ", "value": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb8b2d8v9", "name": "更新数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "category", "valueName": "类目"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}], "headNodeKeys": ["node_1hb8ap4i45", "node_1hb8ap4i46"], "id": null, "key": "node_1hb8ap4i44", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb83rufq6", "name": "条件", "nextNodeKey": "node_1hb8423el10", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "ckNKrXMmcOYetXGPmHQNl", "key": "ckNKrXMmcOYetXGPmHQNl", "leftValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "category", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "TERP_MIGRATE$gen_mat_cate_md"}, "valueKey": "mat<PERSON>ate<PERSON><PERSON>nt", "valueName": "父类目"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": null, "logicOperator": "AND", "type": "ConditionGroup"}], "id": null, "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb8423el10", "name": "赋值", "nextNodeKey": "node_1hb840c8o9", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "category", "valueName": "类目"}, {"modelAlias": "ERP_GEN$gen_mat_cate_md", "relatedModel": null, "valueKey": "status", "valueName": "状态"}]}, "id": "1hb8425bm11", "operator": "EQ", "value": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb840c8o9", "name": "更新数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "valueKey": "category", "valueName": "类目"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}], "headNodeKeys": ["node_1hb83rufq5", "node_1hb83rufq6"], "id": null, "key": "node_1hb83rufp4", "name": "排他分支", "nextNodeKey": "node_1hb82pjo62", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hb82pjo62", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hb82pjo61"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "id", "fieldKey": "id", "fieldName": "类目id", "fieldType": "Number", "id": null, "required": null}], "key": "COMMON_2B$CATEGORYENABLE", "name": "类目启用", "output": [{"defaultValue": null, "description": null, "fieldAlias": "category", "fieldKey": "category", "fieldName": "类目", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": null}, "relation": null, "required": null}], "props": {"desc": null, "name": null, "permissionKey": "TSRM$CATEGORYENABLE_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}