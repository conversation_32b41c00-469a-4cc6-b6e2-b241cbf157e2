{"access": "Private", "key": "TSRM$BATCH_SET_BALANCE", "name": "批量设置平衡利库", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "key": "node_1hcjhjh5d7", "name": "开始", "nextNodeKey": "node_1hcjijjtd50", "preNodeKey": null, "props": {"desc": null, "globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "isBalance", "fieldKey": "isBalance", "fieldName": "平衡利库", "fieldType": "Boolean", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null, "required": null}, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "批量处理id", "fieldType": "Array", "id": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hcjijjtd50", "name": "在线SQL脚本", "nextNodeKey": "node_1hcjhjh5d8", "preNodeKey": null, "props": {"desc": null, "name": null, "output": null, "outputAssign": null, "sqlPlaceholderMapping": [{"id": null, "key": "isBalance", "value": {"constValue": null, "fieldType": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": null, "valueKey": "isBalance", "valueName": "平衡利库"}]}}, {"id": null, "key": "ids", "value": {"constValue": null, "fieldType": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": null, "valueKey": "ids", "valueName": "批量处理id"}]}}], "sqlScript": "update ext_wq_gen_mat_com_org_md set balance_stock =#{isBalance} where id in (#{ids})", "type": "SqlProperties"}, "renderType": null, "type": "SqlNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hcjhjh5d8", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hcjhjh5d7"], "input": [{"defaultValue": null, "description": null, "fieldAlias": "isBalance", "fieldKey": "isBalance", "fieldName": "平衡利库", "fieldType": "Boolean", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null, "required": null}, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "批量处理id", "fieldType": "Array", "id": null, "required": null}], "key": "COMMON_2B$BATCH_SET_BALANCE", "name": "批量设置平衡利库", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$BATCH_SET_BALANCE_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}