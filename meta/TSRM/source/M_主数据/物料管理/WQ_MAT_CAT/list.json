{"access": "Private", "key": "TSRM$WQ_MAT_CAT:list", "name": "list", "props": {"containerSelect": {"COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail": [{"field": "matCateCode", "selectFields": null}, {"field": "matCateName", "selectFields": null}, {"field": "wqMatType", "selectFields": null}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "selectFields": null}, {"field": "status", "selectFields": null}, {"field": "mat<PERSON>ate<PERSON><PERSON>nt", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matCateName", "selectFields": null}]}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}], "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form": [{"field": "version", "selectFields": null}, {"field": "matCateCode", "selectFields": null}, {"field": "matCateName", "selectFields": null}, {"field": "wqMatType", "selectFields": null}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "selectFields": null}, {"field": "mat<PERSON>ate<PERSON><PERSON>nt", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matCateName", "selectFields": null}]}], "COMMON_2B$WQ_MAT_CAT-tree": []}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$WQ_MAT_CAT-create-btn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}, {"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form"}]}, "buttonType": "default", "confirmOn": "off", "label": "新建 ", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_0_0_0", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$WQ_MAT_CAT-left-button-wrapper", "name": "Space", "props": {"style": {"marginBottom": 12}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$WQ_MAT_CAT-tree-addChildBtn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form"}], "executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { parentId: record.id } })"}, "buttonType": "default", "confirmOn": "off", "label": "新建子类目", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"id": "2dq8uEoKM12gmm30S038Z", "leftValue": {"fieldType": "Boolean", "options": [], "scope": "form", "title": "是否叶子节点", "type": "VarValue", "val": "<PERSON><PERSON><PERSON><PERSON>", "value": "ERP_GEN$gen_mat_cate_md.isLeaf", "valueType": "VAR", "varVal": "<PERSON><PERSON><PERSON><PERSON>", "varValue": [{"valueKey": "<PERSON><PERSON><PERSON><PERSON>", "valueName": "<PERSON><PERSON><PERSON><PERSON>"}]}, "operator": "EQ", "rightValue": {"constValue": "false", "fieldType": "Boolean", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "7Eu_HSFDKaKpxNNWcJKU3", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "PHp2yC0G0FoVoYFv43Egc", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-tree-editBtn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_0_1_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-tree-enableBtn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "id", "fieldName": "类目id", "fieldType": "Number", "valueConfig": {"expression": "record?.id", "type": "expression"}}], "service": "TSRM$CATEGORYENABLE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-detailView-container"}, {"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-tree"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-tree-disableBtn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}, {"fieldType": "Text", "valueConfig": {"action": "Message", "message": "启用成功"}}], "service": "ERP_GEN$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-detailView-container"}, {"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-tree"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_0_1_0_3", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-tree-deleteBtn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "ERP_GEN$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-tree"}, {"action": "PageJump", "target": "list"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_0_1_0_4", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}], "key": "COMMON_2B$WQ_MAT_CAT-tree-recordActions", "name": "RecordActions", "props": {}, "type": "Layout"}], "key": "COMMON_2B$WQ_MAT_CAT-tree", "name": "Tree", "props": {"flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}], "serviceKey": "ERP_GEN$SYS_FindTreeChildrenDataService", "type": "InvokeSystemService"}, "isLazyLoad": true, "labelField": "matCateName", "leafFieldName": "<PERSON><PERSON><PERSON><PERSON>", "leafFieldValue": true, "modelAlias": "ERP_GEN$gen_mat_cate_md", "onSelect$": "(key, info) => $context.mode !== 'design' && navigate({ action: 'show', recordId: info.node.id })", "showScope": "all", "tableCondition": null, "treeField": "mat<PERSON>ate<PERSON><PERSON>nt"}, "type": "Container"}], "key": "COMMON_2B$WQ_MAT_CAT-left-container", "name": "Box", "props": {"style": {"display": "flex", "flexDirection": "column", "height": "100%"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$WQ_MAT_CAT-list-TERP_MIGRATE$gen_mat_cate_md-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$WQ_MAT_CAT-list-TERP_MIGRATE$gen_mat_cate_md-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "9aVEKpQOOhZUxkDL7B2ne", "name": "Status", "props": {"text$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "type$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.type"}}], "key": "COMMON_2B$WQ_MAT_CAT-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"类目配置表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "ERP_GEN$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-tree"}, {"action": "PageJump", "target": "list"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_1_1_0_1_0_0", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindFlowConfig": {"params": [], "service": "TSRM$CATEGORYDISABLEEVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-detailView-container"}, {"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-tree"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_1_1_0_1_0_1", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "id", "fieldName": "类目id", "fieldType": "Number", "valueConfig": {"action": {"selector": "id", "target": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail"}, "type": "action"}}], "service": "TSRM$CATEGORYENABLE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-detailView-container"}, {"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-tree"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_1_1_0_1_0_2", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_1_1_0_1_0_3", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detail-TERP_MIGRATE$gen_mat_cate_md-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$WQ_MAT_CAT-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$WQ_MAT_CAT-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateCode", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "editable": false, "label": "类目编码", "name": "matCateCode", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateName", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "editable": false, "label": "类目名称", "name": "matCateName", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-wqMatType", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "wqMatType", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "物料类型", "name": "wqMatType", "type": "SELECT"}}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-isLeaf", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editable": false, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "BOOL"}}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-status", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "label": "状态", "name": "status", "type": "SELECT"}}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "label": "选择父类目", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "父类目", "name": "mat<PERSON>ate<PERSON><PERSON>nt", "type": "SELFRELATION"}}], "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$WQ_MAT_CAT-MH2iBdBIB_0oE3bFX3By9", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$WQ_MAT_CAT-lnetpcf56IEsHX849wrwz", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}], "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Container"}], "key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "displayName": "详情", "key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"类目配置表\")) : \"新建类目配置表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue$": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "nickname", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "昵称", "name": "nickname", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "username", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户名", "name": "username", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "email", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mobile", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户手机", "name": "mobile", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "username", "modelAlias": "TSRM$user", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": true, "initialValue$": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TSRM$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "nickname", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "昵称", "name": "nickname", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "username", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户名", "name": "username", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "email", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mobile", "modelAlias": "TSRM$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户手机", "name": "mobile", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "username", "modelAlias": "TSRM$user", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": true, "initialValue$": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "initialValue$": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "initialValue$": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue$": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateCode", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue$": null, "label": "类目编码", "lookup": [{"conditionGroup": {"conditions": [{"conditions": [{"id": "QO8kt296cNzEsFkV-WoKH", "leftValue": {"fieldType": "Number", "options": [], "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "ERP_GEN$gen_mat_cate_md.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "5EgzOJTzFuz5yEf_nqwwr", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "JRX0xyNTavlcw3Tajt4z5", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "tk8Rj3cVBbuveZEbfHBSh", "valueRules": null}, {"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "AvLIaUGSl-bvpG1VuuY3B", "valueRules": null}], "name": "matCateCode", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateName", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue$": null, "label": "类目名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "mSIq7J2Z-K5xbUDcYKIEa", "valueRules": null}], "name": "matCateName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-wqMatType", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "wqMatType", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "initialValue$": null, "label": "物料类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "McGbH4IYMuBuNGYdjg_8T", "valueRules": null}], "name": "wqMatType", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-isLeaf", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue$": null, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent", "name": "FormField", "props": {"componentProps": {"fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "label": "选择父类目", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "matCateName", "reverseConstructFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}}, "displayComponentType": "SelfRelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "类目编码", "name": "matCateCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "类目名称", "name": "matCateName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "mat<PERSON>ate<PERSON><PERSON>nt", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType"], "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "label": "选择父类目", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "isRelationColumn": true, "label": "父类目", "name": "mat<PERSON>ate<PERSON><PERSON>nt", "required": false, "type": "SELFRELATION", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "label": "类目编码", "name": "matCateCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "label": "类目名称", "name": "matCateName", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "reverseConstructFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}, "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "initialValue$": "route.query?.parentId ? { id: route.query?.parentId } : null", "label": "父类目", "name": "mat<PERSON>ate<PERSON><PERSON>nt", "rules": [], "type": "SELFRELATION"}, "type": "Widget"}], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": false}, "type": "Layout"}], "key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "name": "FormGroup", "props": {"colon": false, "flow": {"containerKey": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "modelAlias": "ERP_GEN$gen_mat_cate_md", "params$": "{ id: route.recordId }", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "test$": "route.action === 'edit'", "type": "InvokeSystemService"}, "layout": "horizontal", "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "list", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$WQ_MAT_CAT-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "类目", "fieldType": "Model", "modelAlias": "ERP_GEN$gen_mat_cate_md", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form"}, "type": "action"}}], "service": "TSRM$WQ_CAT_SAVE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "操作成功！"}, {"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": "TSRM$WQ_CAT_SAVE", "type": "expression"}], "type": "<PERSON><PERSON><PERSON>"}}, {"action": "Refresh", "target": "COMMON_2B$WQ_MAT_CAT-tree"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac_z_0_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$WQ_MAT_CAT-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$WQ_MAT_CAT-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$WQ_MAT_CAT-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "COMMON_2B$WQ_MAT_CAT-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}], "key": "COMMON_2B$WQ_MAT_CAT-page", "name": "View", "props": {}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["选择更新人", "系统信息", "状态", "停用", "删除成功", "操作成功！", "请输入版本号", "类目名称", "是否叶子节点", "请输入创建时间", "删除", "物料类型", "停用成功", "新建子类目", "保存", "启用", "请输入ID", "用户邮箱", "昵称", "用户名", "类目编码", "ID", "创建人", "请输入更新时间", "新建 ", "启用成功", "用户手机", "选择父类目", "主体信息", "确认启用吗？", "请选择", "版本号", "类目配置表详情", "更新时间", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "更新人", "新建类目配置表", "编辑", "父类目", "请输入", "选择创建人", "取消", "创建时间", "确认删除吗？", "类目配置表", "确认停用吗？"], "i18nScanPaths": ["COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.label", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.editComponentProps.fields.3.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateCode.props.label", "COMMON_2B$WQ_MAT_CAT-tree-disableBtn.props.actionConfig.endLogicOtherConfig.2.message", "COMMON_2B$WQ_MAT_CAT-tree-deleteBtn.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdAt.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-id.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-tree-disableBtn.props.label", "COMMON_2B$WQ_MAT_CAT-detailView-actions-delete.props.label", "COMMON_2B$WQ_MAT_CAT-detailView-actions-disable.props.label", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-isLeaf.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-updatedAt.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-createdAt.props.label", "COMMON_2B$WQ_MAT_CAT-editView-action-save.props.label", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateName.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateCode.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-createdBy.props.componentProps.label", "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.fields.1.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.editComponentProps.fields.2.componentProps.placeholder", "@exp:COMMON_2B$WQ_MAT_CAT-detailView-page-title.props.title", "COMMON_2B$WQ_MAT_CAT-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.editComponentProps.fields.0.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-isLeaf.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.label", "COMMON_2B$WQ_MAT_CAT-detailView-actions-enable.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.editComponentProps.fields.2.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.fields.2.componentProps.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-wqMatType.props.componentProps.placeholder", "@exp:COMMON_2B$WQ_MAT_CAT-editView-page-title.props.title", "COMMON_2B$WQ_MAT_CAT-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.2.message", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-tree-deleteBtn.props.actionConfig.endLogicOtherConfig.2.message", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.editComponentProps.fields.1.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdAt.props.rules.0.message", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.componentProps.label", "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs.props.items.0.label", "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs.props.items.0.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-id.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateName.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs.props.items.1.label", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateCode.props.label", "COMMON_2B$WQ_MAT_CAT-editView-action-save.props.actionConfig.endLogicOtherConfig.0.message", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-isLeaf.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.fields.2.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.editComponentProps.fields.0.label", "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-actions-edit.props.label", "COMMON_2B$WQ_MAT_CAT-editView-action-cancel.props.label", "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-actions-disable.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-status.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-tree-addChildBtn.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.componentProps.label", "COMMON_2B$WQ_MAT_CAT-detailView-actions-disable.props.actionConfig.endLogicOtherConfig.2.message", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-version.props.rules.0.message", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-status.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-create-btn.props.label", "COMMON_2B$WQ_MAT_CAT-tree-enableBtn.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateCode.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.editComponentProps.fields.3.label", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-wqMatType.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateName.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-actions-enable.props.actionConfig.endLogicOtherConfig.2.message", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.componentProps.label", "COMMON_2B$WQ_MAT_CAT-tree-enableBtn.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-wqMatType.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedAt.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.filterFields.0.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-version.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdAt.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-updatedBy.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-id.props.rules.0.message", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-wqMatType.props.label", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateName.props.label", "COMMON_2B$WQ_MAT_CAT-detailView-actions-enable.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.filterFields.1.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.filterFields.0.label", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-isLeaf.props.label", "COMMON_2B$WQ_MAT_CAT-tree-disableBtn.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.editComponentProps.fields.1.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-createdBy.props.label", "9aVEKpQOOhZUxkDL7B2ne.props.text$", "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.componentProps.label", "COMMON_2B$WQ_MAT_CAT-tree-editBtn.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.editComponentProps.fields.2.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedAt.props.rules.0.message", "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-updatedBy.props.componentProps.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent.props.editComponentProps.fields.0.label", "COMMON_2B$WQ_MAT_CAT-tree-deleteBtn.props.label", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-version.props.componentProps.placeholder"]}, "key": "TSRM$WQ_MAT_CAT-list", "permissionKey": "TSRM$WQ_MAT_CAT-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$WQ_MAT_CAT-tree", "label": "树", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-left-container", "label": "区块", "type": "Box"}], "relations": [{"key": "ERP_GEN$SYS_FindTreeChildrenDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-create-btn", "label": "新建 ", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-left-container", "label": "区块", "type": "Box"}, {"key": "COMMON_2B$WQ_MAT_CAT-left-button-wrapper", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-tree-addChildBtn", "label": "新建子类目", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-left-container", "label": "区块", "type": "Box"}, {"key": "COMMON_2B$WQ_MAT_CAT-tree", "label": "树", "type": "Tree"}, {"key": "COMMON_2B$WQ_MAT_CAT-tree-recordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-tree-editBtn", "label": "编辑", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-left-container", "label": "区块", "type": "Box"}, {"key": "COMMON_2B$WQ_MAT_CAT-tree", "label": "树", "type": "Tree"}, {"key": "COMMON_2B$WQ_MAT_CAT-tree-recordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-tree-enableBtn", "label": "启用", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-left-container", "label": "区块", "type": "Box"}, {"key": "COMMON_2B$WQ_MAT_CAT-tree", "label": "树", "type": "Tree"}, {"key": "COMMON_2B$WQ_MAT_CAT-tree-recordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$CATEGORYENABLE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-tree-disableBtn", "label": "停用", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-left-container", "label": "区块", "type": "Box"}, {"key": "COMMON_2B$WQ_MAT_CAT-tree", "label": "树", "type": "Tree"}, {"key": "COMMON_2B$WQ_MAT_CAT-tree-recordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "ERP_GEN$SYS_MasterData_DisableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-tree-deleteBtn", "label": "删除", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-left-container", "label": "区块", "type": "Box"}, {"key": "COMMON_2B$WQ_MAT_CAT-tree", "label": "树", "type": "Tree"}, {"key": "COMMON_2B$WQ_MAT_CAT-tree-recordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "ERP_GEN$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$WQ_CAT_SAVE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-actions-disable", "label": "停用", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$CATEGORYDISABLEEVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-actions-enable", "label": "启用", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$CATEGORYENABLE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detail-TERP_MIGRATE$gen_mat_cate_md-logs", "label": "日志", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-id", "label": "ID", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-version", "label": "版本号", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateCode", "label": "类目编码", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateName", "label": "类目名称", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-wqMatType", "label": "物料类型", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-isLeaf", "label": "是否叶子节点", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent", "label": "父类目", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-A8nfHbhGOrjjnIantYjQu", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_ReverseConstructTreeService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateCode", "label": "类目编码", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-MH2iBdBIB_0oE3bFX3By9", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateName", "label": "类目名称", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-MH2iBdBIB_0oE3bFX3By9", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-wqMatType", "label": "物料类型", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-MH2iBdBIB_0oE3bFX3By9", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-isLeaf", "label": "是否叶子节点", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-MH2iBdBIB_0oE3bFX3By9", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-status", "label": "状态", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-MH2iBdBIB_0oE3bFX3By9", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent", "label": "父类目", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-MH2iBdBIB_0oE3bFX3By9", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-lnetpcf56IEsHX849wrwz", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-lnetpcf56IEsHX849wrwz", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-lnetpcf56IEsHX849wrwz", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$WQ_MAT_CAT-page", "label": "视图", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$WQ_MAT_CAT-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-container", "label": "详情", "type": "View"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-TERP_MIGRATE$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$WQ_MAT_CAT-lnetpcf56IEsHX849wrwz", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$WQ_MAT_CAT-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}