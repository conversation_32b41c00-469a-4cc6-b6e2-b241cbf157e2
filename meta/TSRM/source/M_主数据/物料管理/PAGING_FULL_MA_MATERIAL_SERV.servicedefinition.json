{"access": "Private", "description": "{}", "key": "TSRM$PAGING_FULL_MA_MATERIAL_SERV", "name": "TSRM_分页查询物料", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": "null", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1h9a9hm2n23", "name": "开始", "nextNodeKey": "node_1h9aa6nhd27", "props": {"globalVariable": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null, "required": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "modelKey": "ERP_GEN$gen_mat_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "物料主数据定义表"}, "relation": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1h9aa6nhd27", "name": "查询数据", "nextNodeKey": "node_1h9a9hm2n24", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "OEnOyypGaab0tZYPyZZdw", "key": null, "leftValue": {"fieldType": "Enum", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "ERP_GEN$gen_mat_md", "valueKey": "status", "valueName": "状态"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "GTTMJ8LDu3W4vfc_kiWna", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "wcxXxsZa2nxr_hsPnRTpY", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": null, "dataType": "PAGING", "desensitized": true, "maximum": null, "outputAssign": {"customAssignments": [{"field": {"fieldType": "Paging", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1h9pl7l121", "operator": "EQ", "value": {"fieldType": "Paging", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1h9aa6nhd27", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": {"fieldType": "Pageable", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "valueKey": "pageable", "valueName": "pageable"}]}, "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_md", "queryFields": [{"fieldKey": "genMatTypeCfId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_type_cf", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "matTypeCode"}, {"fieldKey": "matTypeName"}, {"fieldKey": "syncNode"}, {"fieldKey": "syncParty"}]}}, {"fieldKey": "baseUomId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_uom_type_cf", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "uomType"}, {"fieldKey": "uomCode"}, {"fieldKey": "uomDesc"}]}}, {"fieldKey": "weightUomId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_uom_type_cf", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "uomType"}, {"fieldKey": "uomCode"}, {"fieldKey": "uomDesc"}]}}, {"fieldKey": "volumUomId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_uom_type_cf", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "uomType"}, {"fieldKey": "uomCode"}, {"fieldKey": "uomDesc"}]}}, {"fieldKey": "cateId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_cate_md", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "matCateCode"}, {"fieldKey": "matCateName"}, {"fieldKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldKey": "status"}, {"fieldKey": "isLimitPoQualifications"}, {"fieldKey": "isLimitSoQualifications"}, {"fieldKey": "isLimitCtQualifications"}, {"fieldKey": "path"}, {"fieldKey": "wqMatType"}]}}, {"fieldKey": "brandId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_brand_md", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "brandName"}, {"fieldKey": "brandCode"}, {"fieldKey": "brandPic"}]}}, {"fieldKey": "statusId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_status_type_cf", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "matStatusCode"}, {"fieldKey": "matStatusName"}, {"fieldKey": "eventId"}, {"fieldKey": "msgType"}]}}, {"fieldKey": "matSlsId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_sls_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "mat<PERSON>ur<PERSON>d", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_pur_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "matInvId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_inv_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "matMrpId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_mrp_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "uomFormulaTypeId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_uom_formula_type_cf", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "genCharaClassId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_chara_class_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "mat<PERSON>rd", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_prd_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "atpGroupId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_atp_group_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "matFinId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_fin_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "matWmIds", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_mat_wm_md", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "bomUseId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_bom_use_cf", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "lengthUnitId", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$gen_uom_type_cf", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "created<PERSON>y", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$user", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "updatedBy", "queryModelFields": {"allFields": false, "modelKey": "ERP_GEN$user", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "matCode"}, {"fieldKey": "<PERSON><PERSON><PERSON>"}, {"fieldKey": "grossWeight"}, {"fieldKey": "netWeight"}, {"fieldKey": "batchRelv"}, {"fieldKey": "isBatchDetermination"}, {"fieldKey": "shelfLife"}, {"fieldKey": "charas"}, {"fieldKey": "isAutoCountPlan"}, {"fieldKey": "cost"}, {"fieldKey": "matCodeExt"}, {"fieldKey": "status"}, {"fieldKey": "imageUrl"}, {"fieldKey": "remark"}, {"fieldKey": "customMat"}, {"fieldKey": "isKitSls"}, {"fieldKey": "isCompleteSetDel"}, {"fieldKey": "length"}, {"fieldKey": "width"}, {"fieldKey": "height"}, {"fieldKey": "purchaseReferPrice"}, {"fieldKey": "id"}]}, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "物料主数据定义表"}, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"conditionGroup": null, "fieldKey": "genMatTypeCfId", "modelKey": "ERP_GEN$gen_mat_type_cf", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "baseUomId", "modelKey": "ERP_GEN$gen_uom_type_cf", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "weightUomId", "modelKey": "ERP_GEN$gen_uom_type_cf", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "volumUomId", "modelKey": "ERP_GEN$gen_uom_type_cf", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "cateId", "modelKey": "ERP_GEN$gen_mat_cate_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "brandId", "modelKey": "ERP_GEN$gen_brand_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "statusId", "modelKey": "ERP_GEN$gen_mat_status_type_cf", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "matSlsId", "modelKey": "ERP_GEN$gen_mat_sls_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "mat<PERSON>ur<PERSON>d", "modelKey": "ERP_GEN$gen_mat_pur_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "matInvId", "modelKey": "ERP_GEN$gen_mat_inv_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "matMrpId", "modelKey": "ERP_GEN$gen_mat_mrp_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "uomFormulaTypeId", "modelKey": "ERP_GEN$gen_uom_formula_type_cf", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "genCharaClassId", "modelKey": "ERP_GEN$gen_chara_class_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "mat<PERSON>rd", "modelKey": "ERP_GEN$gen_mat_prd_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "atpGroupId", "modelKey": "ERP_GEN$gen_atp_group_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "matFinId", "modelKey": "ERP_GEN$gen_mat_fin_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "matWmIds", "modelKey": "ERP_GEN$gen_mat_wm_md", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "bomUseId", "modelKey": "ERP_GEN$gen_bom_use_cf", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "lengthUnitId", "modelKey": "ERP_GEN$gen_uom_type_cf", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "created<PERSON>y", "modelKey": "ERP_GEN$user", "subQueryRelatedModels": []}, {"conditionGroup": null, "fieldKey": "updatedBy", "modelKey": "ERP_GEN$user", "subQueryRelatedModels": []}], "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"desc": null, "id": null, "key": "node_1h9a9hm2n24", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1h9a9hm2n23"], "id": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null, "required": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "key": "TSRM$PAGING_FULL_MA_MATERIAL_SERV", "name": "TSRM_分页查询物料", "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "modelKey": "ERP_GEN$gen_mat_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "物料主数据定义表"}, "relation": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "TSRM$PAGING_FULL_MA_MATERIAL_SERV_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}