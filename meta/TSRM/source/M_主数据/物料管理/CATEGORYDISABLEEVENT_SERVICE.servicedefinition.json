{"access": "Private", "description": "{}", "key": "TSRM$CATEGORYDISABLEEVENT_SERVICE", "name": "类目停用服务", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "ERP_GEN$gen_mat_cate_md", "name": "类目配置表"}, "modelArrayWhether": false, "notice": null, "notices": null, "permissionKey": "TSRM$CATEGORYDISABLEEVENT_perm_ac", "relations": [{"actionType": "Action", "code": "TSRM$CATE_DISABLE_ACTION", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": null, "sourceCode": null}], "returnModel": null, "returnModelArrayWhether": false, "states": []}, "isDeleted": null, "isEnabled": true, "modelKey": "ERP_GEN$gen_mat_cate_md", "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_ff4fd937f5", "name": "开始", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}}], "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_adb40e194e", "name": "类目停用", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(ERP_GEN$gen_mat_cate_md)request", "implementation": "TSRM$CATE_DISABLE_ACTION", "implementationName": "类目停用", "newAction": true, "output": null, "outputAssign": null, "relatedModel": null, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_a7c5c6347f", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "modelKey": "ERP_GEN$gen_mat_cate_md", "modelName": "类目配置表"}}], "key": "TSRM$CATEGORYDISABLEEVENT_SERVICE", "name": "类目停用服务", "output": [{"elements": [], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "TSRM$CATEGORYDISABLEEVENT_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}