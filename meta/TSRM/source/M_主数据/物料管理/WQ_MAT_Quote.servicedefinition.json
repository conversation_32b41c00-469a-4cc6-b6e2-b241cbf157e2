{"access": "Private", "key": "TSRM$WQ_MAT_Quote", "name": "物料引用", "props": {"isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "key": "node_1hceuobg049", "name": "开始", "nextNodeKey": "node_1hchkbopk11", "preNodeKey": null, "props": {"desc": null, "globalVariable": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Text", "id": null, "required": null}, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "物料id列表", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "comMat", "fieldKey": "comMat", "fieldName": "公司物料", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null, "required": null}, "fieldAlias": "matIds", "fieldKey": "matIds", "fieldName": "物料列表", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "comId", "fieldKey": "comId", "fieldName": "公司Id", "fieldType": "Number", "id": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": [{"children": null, "headNodeKeys": null, "key": "node_1hchqhjrp19", "name": "查询数据", "nextNodeKey": "node_1hchqk3ts20", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"key": "iGmlUG_Veko4xgYk3hoi5", "leftValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "relatedModel": null, "valueKey": "comOrg", "valueName": "公司组织"}, {"modelAlias": "TERP_MIGRATE$org_com_org_cf", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": null, "valueKey": "comId", "valueName": "公司Id"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"key": "eknUlhhlpc4L3yigv69Wh", "leftValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "relatedModel": null, "valueKey": "material", "valueName": "物料"}, {"modelAlias": "ERP_GEN$gen_mat_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hchkbopk11", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": null, "valueKey": "_item", "valueName": "循环元素"}]}, "rightValues": null, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": "公司物料库表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "key": "node_1hchqk3ts21", "name": "条件", "nextNodeKey": "node_1hci9ujck1", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"key": "CY0OrrgD9HIBuEl0H48l6", "leftValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": "公司物料库表"}, "valueKey": "NODE_OUTPUT_node_1hchqhjrp19", "valueName": "[查询数据]节点.output"}, {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hci9ujck1", "name": "查询数据", "nextNodeKey": "node_1hci9vtef2", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"key": "YgL9-flQEPaBao0ZEDsIt", "leftValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "ERP_GEN$gen_mat_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hchkbopk11", "valueName": "循环变量[循环]"}, {"modelAlias": null, "relatedModel": null, "valueKey": "_item", "valueName": "循环元素"}]}, "rightValues": null, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "物料主数据定义表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hci9vtef2", "name": "查询数据", "nextNodeKey": "node_1hcjsnpgc51", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"key": "J10FlbrOrEFrdNlPEWHq9", "leftValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": "TERP_MIGRATE$org_com_org_cf", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": null, "valueKey": "comId", "valueName": "公司Id"}]}, "rightValues": null, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desc": null, "maximum": null, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_com_org_cf", "modelKey": "TERP_MIGRATE$org_com_org_cf", "modelName": "公司组织配置表"}, "sortOrder": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "renderType": null, "type": "RetrieveDataNode"}, {"children": [{"children": null, "headNodeKeys": null, "key": "node_1hcjsnpgc52", "name": "条件", "nextNodeKey": "node_1hcjsq6lj54", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"key": "oTSB49zOkRXvAE2te5ggj", "leftValue": {"constValue": null, "fieldType": "Enum", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_com_org_cf", "modelKey": "TERP_MIGRATE$org_com_org_cf", "modelName": "公司组织配置表"}, "valueKey": "NODE_OUTPUT_node_1hci9vtef2", "valueName": "[查询数据]节点.output"}, {"modelAlias": "TERP_MIGRATE$org_com_org_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "extWqComType", "valueName": "公司类型"}]}, "operator": "EQ", "rightValue": {"constValue": "INTERNAL", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST", "varValue": null}, "rightValues": null, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hcjsq6lj54", "name": "赋值", "nextNodeKey": null, "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Boolean", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "valueKey": "comMat", "valueName": "公司物料"}, {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "relatedModel": null, "valueKey": "balanceStock", "valueName": "是否平衡利库"}]}, "id": "1hcjsq8m955", "operator": "EQ", "value": {"constValue": "true", "fieldType": "Boolean", "type": "VarValue", "valueType": "CONST", "varValue": null}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hcjsnpgc53", "name": "条件", "nextNodeKey": "node_1hcjsrgji56", "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"key": "ZrkkCyYXEAJYtFKW4XFBd", "leftValue": {"constValue": null, "fieldType": "Enum", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_com_org_cf", "modelKey": "TERP_MIGRATE$org_com_org_cf", "modelName": "公司组织配置表"}, "valueKey": "NODE_OUTPUT_node_1hci9vtef2", "valueName": "[查询数据]节点.output"}, {"modelAlias": "TERP_MIGRATE$org_com_org_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "extWqComType", "valueName": "公司类型"}]}, "operator": "EQ", "rightValue": {"constValue": "EXTERNAL", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST", "varValue": null}, "rightValues": null, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hcjsrgji56", "name": "赋值", "nextNodeKey": null, "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Boolean", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "valueKey": "comMat", "valueName": "公司物料"}, {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "relatedModel": null, "valueKey": "balanceStock", "valueName": "是否平衡利库"}]}, "id": "1hcjsrijg57", "operator": "EQ", "value": {"constValue": "false", "fieldType": "Boolean", "type": "VarValue", "valueType": "CONST", "varValue": null}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}], "headNodeKeys": ["node_1hcjsnpgc52", "node_1hcjsnpgc53"], "key": "node_1hcjsnpgc51", "name": "排他分支", "nextNodeKey": "node_1hchkhkn115", "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hchkhkn115", "name": "赋值", "nextNodeKey": "node_1hciad0f21", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Model", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "valueKey": "comMat", "valueName": "公司物料"}, {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": null}, "valueKey": "material", "valueName": "物料"}]}, "id": "1hchkhnmb16", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "物料主数据定义表"}, "valueKey": "NODE_OUTPUT_node_1hci9ujck1", "valueName": "[查询数据]节点.output"}]}}, {"field": {"constValue": null, "fieldType": "Model", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "valueKey": "comMat", "valueName": "公司物料"}, {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "relatedModel": {"modelAlias": "TERP_MIGRATE$org_com_org_cf", "modelKey": "TERP_MIGRATE$org_com_org_cf", "modelName": null}, "valueKey": "comOrg", "valueName": "公司组织"}]}, "id": "1hchkkdk017", "operator": "EQ", "value": {"constValue": null, "fieldType": "Model", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TERP_MIGRATE$org_com_org_cf", "modelKey": "TERP_MIGRATE$org_com_org_cf", "modelName": "公司组织配置表"}, "valueKey": "NODE_OUTPUT_node_1hci9vtef2", "valueName": "[查询数据]节点.output"}]}}, {"field": {"constValue": null, "fieldType": "Enum", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "valueKey": "comMat", "valueName": "公司物料"}, {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "relatedModel": null, "valueKey": "scope", "valueName": "范围"}]}, "id": "1hchqqi0l24", "operator": "EQ", "value": {"constValue": "MATERIAL", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST", "varValue": null}}, {"field": {"constValue": null, "fieldType": "Text", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "valueKey": "comMat", "valueName": "公司物料"}, {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "relatedModel": null, "valueKey": "materialCode", "valueName": "物料编码"}]}, "id": "1hcia4eer3", "operator": "EQ", "value": {"constValue": null, "fieldType": "Text", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "物料主数据定义表"}, "valueKey": "NODE_OUTPUT_node_1hci9ujck1", "valueName": "[查询数据]节点.output"}, {"modelAlias": "ERP_GEN$gen_mat_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "matCode", "valueName": "物料编码"}]}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hciad0f21", "name": "新增数据", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": null}, "valueKey": "comMat", "valueName": "公司物料"}]}, "name": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": "公司物料库表"}, "type": "CascadeCreateDataProperties"}, "renderType": null, "type": "CascadeCreateDataNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hchqk3ts22", "name": "条件", "nextNodeKey": null, "preNodeKey": null, "props": {"conditionGroup": {"conditions": [{"conditions": [{"key": "C0EWkIVieYMkp1pNQvQdK", "leftValue": {"constValue": null, "fieldType": "Number", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "modelKey": "TB2B$ext_wq_gen_mat_com_org_md", "modelName": "公司物料库表"}, "valueKey": "NODE_OUTPUT_node_1hchqhjrp19", "valueName": "[查询数据]节点.output"}, {"modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "desc": null, "name": null, "type": "ConditionProperties"}, "renderType": null, "type": "ConditionNode"}], "headNodeKeys": ["node_1hchqk3ts21", "node_1hchqk3ts22"], "key": "node_1hchqk3ts20", "name": "排他分支", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "type": "ExclusiveBranchProperties"}, "renderType": null, "type": "ExclusiveBranchNode"}], "headNodeKeys": ["node_1hchqhjrp19"], "key": "node_1hchkbopk11", "name": "循环", "nextNodeKey": "node_1hceuobg050", "preNodeKey": null, "props": {"desc": null, "loopData": {"constValue": null, "fieldType": "Array", "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": null, "valueKey": "matIds", "valueName": "物料列表"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "matIds", "fieldKey": "matIds", "fieldName": "物料列表", "fieldType": "Number", "id": null, "required": null}, "loopType": "DATASET_LOOP", "name": null, "stopWhenDataEmpty": false, "type": "LoopProperties"}, "renderType": null, "type": "LoopNode"}, {"children": null, "headNodeKeys": null, "key": "node_1hceuobg050", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hceuobg049"], "input": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null, "required": null}, "fieldAlias": "matIds", "fieldKey": "matIds", "fieldName": "物料列表", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "comId", "fieldKey": "comId", "fieldName": "公司Id", "fieldType": "Number", "id": null, "required": null}], "key": "COMMON_2B$WQ_MAT_Quote", "name": "物料引用", "output": null, "props": {"desc": null, "name": null, "permissionKey": "TSRM$WQ_MAT_Quote_perm_ac", "teamId": 1, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}