{"access": "Private", "key": "TSRM$COMM_2B_COM_MATERAIL:list", "name": "list", "props": {"conditionGroups": {"nxzCFQcf5QZLJTs7CWI26": {"conditions": [], "id": "nxzCFQcf5QZLJTs7CWI26", "logicOperator": "OR", "type": "ConditionGroup"}}, "containerSelect": {"AsHkwalF4McgqfGpbsk5m": [], "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail": [], "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form": [], "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md": []}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "M_Rw501J0pOzpzYBIN8q4", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "balanceStock", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentProps": {"options": [{"id": "VDCQA95Okrd1Xg_UUijRi", "label": "否", "value": "0"}, {"id": "36LeUrWzVCBvbBYy2t6JN", "label": "是", "value": "1"}]}, "editComponentType": "RadioGroup", "label": "是否平衡利库", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": false, "required": true}, "key": "RDk_yt2t1enD6VczY6KYd", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "balanceStock", "value": "TB2B$ext_wq_gen_mat_com_org_md.balanceStock", "valueType": "model"}}], "name": "balanceStock", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "2YVsEinpEPTZ0OiMUJnFZ", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null}, "editComponentType": "InputNumber", "label": "ID", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "readOnly": true, "required": false}, "key": "_NC8QAzaxRt4JLvUGradb", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "id", "value": "TB2B$ext_wq_gen_mat_com_org_md.id", "valueType": "model"}}], "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}], "key": "AsHkwalF4McgqfGpbsk5m", "name": "FormGroup", "props": {"actionConfig": {"endLogic": "Other", "trigger": {"config": {"fields": []}, "event": "onChange"}}, "flow": {"containerKey": "AsHkwalF4McgqfGpbsk5m", "context$": "$context", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_gen_mat_com_org_md"}}], "serviceKey": "TB2B$SYS_FindOneDataService", "type": "InvokeSystemService"}, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "Container"}], "key": "l64WlgO2q-ixoVei94NTh", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "TCUm9sRYflFJRNCmYee99", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["6KFER2KshgemxVNySU-wK"]}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_0_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "PbfVPjzSMykkv_4JEm7-_", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"selector": "", "target": "AsHkwalF4McgqfGpbsk5m"}, "type": "action"}}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"type": "const"}}], "service": "TSRM$SYS_MasterData_UpdateDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["6KFER2KshgemxVNySU-wK"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_COM_MATERAIL-page"}, {"action": "Message", "message": "操作成功！"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_0_1_1", "type": "primary"}, "type": "Widget"}], "key": "M4D192Jpk4PWQxS4smmFy", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "6KFER2KshgemxVNySU-wK", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"width": 560}, "params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "title": "设置平衡利库"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "TTKYYiOKuXsxTrDAd8YJe", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "balanceStock", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentProps": {"options": [{"id": "z_nN4AsbyIkz8aUhd9iBS", "label": "是", "value": true}, {"id": "2uBu3l8x_-Gv2SEwq4sW7", "label": "否", "value": false}]}, "editComponentType": "RadioGroup", "label": "是否平衡利库", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "lyv5aCRXVD9_hZJRAb-P2", "valueRules": null}], "name": "balanceStock", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}], "displayName": "批量平衡利库", "key": "aDerEbTinaGYDI6VpQe5K", "name": "FormGroup", "props": {"flow": {}, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "Container"}], "key": "QS3WgXekayVDyjQiJ6jDV", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "Nhx-wMTXa1gpNkAH3ykEL", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["R_fLLYZPCeL51iWALOGH2"]}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_1_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "Hr6-YVuuHnQXvJ76bb41p", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "isBalance", "fieldName": "平衡利库", "fieldType": "Boolean", "valueConfig": {"action": {"selector": "balanceStock", "target": "aDerEbTinaGYDI6VpQe5K"}, "type": "action"}}, {"element": {"fieldAlias": "element", "fieldName": "element", "fieldType": "Number"}, "fieldAlias": "ids", "fieldName": "批量处理id", "fieldType": "Array", "valueConfig": {"expression": "parent.<PERSON><PERSON><PERSON><PERSON>", "type": "expression"}}], "service": "TSRM$BATCH_SET_BALANCE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["R_fLLYZPCeL51iWALOGH2"]}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_COM_MATERAIL-page"}, {"action": "Message", "message": "操作成功！"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_1_1_1", "type": "primary"}, "type": "Widget"}], "key": "8sMiRokhYAIubyKDo4nGf", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "R_fLLYZPCeL51iWALOGH2", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"width": 560}, "params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "title": "批量平衡利库"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "ids", "fieldName": "ids", "fieldType": "Number", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "name": "ids", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_gen_mat_com_org_md"}}], "service": "TB2B$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_COM_MATERAIL-page"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "COMMON_2B$COMM_2B_COM_MATERAIL-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-multi-delete", "label": "批量删除", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_0_0_1_items_0"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "r3At4Jq7xsCU-7tJ3eVlO", "name": "ImportButton", "props": {"downloadFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}, "downloadServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}}, "isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_0_0_2", "predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}, "predictServiceProps": {"predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}}, "saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}, "saveSubServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}}}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"getUserInfoFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/portal/user/current"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "label": "导出", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_0_0_3", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "type": "Widget"}, {"children": [], "key": "-pPAxiwuB0HlFgFDkdgAb", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "R_fLLYZPCeL51iWALOGH2", "name": "列表设置平衡利库 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [], "type": "Modal"}}, "buttonType": "default", "confirmOn": "off", "disabled$": "mode === \"design\" ? undefined :$context.selectedKeys?.length === 0", "isMultiple": true, "label": "设置平衡利库", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_0_0_4", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "vh1i9KGNaLcLvp7bvjO8u", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "bvbEjLOnozjm0sLFEHKBZ", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId", "extWqNccCode", "extWqSapCodeWq", "extWqNcCodeWq", "extWqK3CodeWq"], "fieldAlias": "comOrg", "label": "选择公司组织", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "parentModelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "hidden": false, "label": "板块", "name": "comOrg", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "orJ769pg1KLeAVQ0ZmOFG", "name": "Field", "props": {"componentProps": {"fieldAlias": "material", "label": "选择物料", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "parentModelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCode", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "物料编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "rr1uCQF5xoyBTCym-8yeV", "trigger": "auto", "valueRules": null}], "name": "matCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "物料名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "fjXh9GaiLB8RLFi9dC3kx", "trigger": "auto", "valueRules": null}], "name": "<PERSON><PERSON><PERSON>", "type": "TEXT"}, {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "genMatTypeCfId", "baseUomId", "weightUomId", "grossWeight", "netWeight", "volumUomId", "matVolum", "cateId", "brandId", "statusId", "batchRelv", "isBatchDetermination", "genCharaClassId", "shelfLife", "charas", "isAutoCountPlan", "cost", "matCodeExt", "status", "atpGroupId", "imageUrl", "remark", "customMat", "bomUseId", "isKitSls", "isCompleteSetDel", "length", "width", "height", "lengthUnitId", "purchaseReferPrice", "mat<PERSON>bbr", "bizStatus", "specModel"], "fieldAlias": "genMatTypeCfId", "labelField": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "editComponentProps": {}, "editComponentType": "RelationSelect", "label": "物料类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "QSACTAZwsaTjK2LHmyG95", "trigger": "auto", "valueRules": null}], "name": "genMatTypeCfId", "type": "OBJECT"}, {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "genMatTypeCfId", "baseUomId", "weightUomId", "grossWeight", "netWeight", "volumUomId", "matVolum", "cateId", "brandId", "statusId", "batchRelv", "isBatchDetermination", "genCharaClassId", "shelfLife", "charas", "isAutoCountPlan", "cost", "matCodeExt", "status", "atpGroupId", "imageUrl", "remark", "customMat", "bomUseId", "isKitSls", "isCompleteSetDel", "length", "width", "height", "lengthUnitId", "purchaseReferPrice", "mat<PERSON>bbr", "bizStatus", "specModel"], "fieldAlias": "baseUomId", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "editComponentProps": {}, "editComponentType": "RelationSelect", "label": "基本计量单位", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "ZtEWNb8stcGrzq7w1EnTH", "trigger": "auto", "valueRules": null}], "name": "baseUomId", "type": "OBJECT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "物料", "name": "material", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "FXqDjfSunfaKScCuvoDdJ", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "balanceStock", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "hidden": false, "label": "是否平衡利库", "name": "balanceStock", "type": "BOOL", "width": 116}, "type": "Widget"}], "key": "hPrY9GY1fMn4voU1H7IjZ", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "name": "Table", "props": {"acceptFilterQuery": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "editComponentProps": {"fields": [], "labelField": ""}, "editComponentType": "InputText", "filterType": "fuzzy", "hidden": false, "label": "板块名称", "name": "comOrg.name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "balanceStock", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "hidden": false, "label": "是否平衡利库", "name": "balanceStock", "type": "BOOL", "width": 116}], "flow": {"containerKey": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "context$": "$context", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "outputParams": [{"elements": [{"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER"}, {"fieldAlias": "created<PERSON>y", "fieldName": "创建人", "fieldType": "OBJECT"}, {"fieldAlias": "updatedBy", "fieldName": "更新人", "fieldType": "OBJECT"}, {"fieldAlias": "createdAt", "fieldName": "创建时间", "fieldType": "DATE"}, {"fieldAlias": "updatedAt", "fieldName": "更新时间", "fieldType": "DATE"}, {"fieldAlias": "version", "fieldName": "版本号", "fieldType": "NUMBER"}, {"fieldAlias": "deleted", "fieldName": "逻辑删除标识", "fieldType": "NUMBER"}, {"fieldAlias": "comOrg", "fieldName": "公司组织", "fieldType": "OBJECT"}, {"fieldAlias": "balanceStock", "fieldName": "是否平衡利库", "fieldType": "BOOL"}, {"fieldAlias": "scope", "fieldName": "范围", "fieldType": "ENUM"}, {"fieldAlias": "material", "fieldName": "物料", "fieldType": "OBJECT"}, {"fieldAlias": "materialCode", "fieldName": "物料编码", "fieldType": "TEXT"}], "fieldAlias": "$result", "fieldName": "取值", "fieldType": "Model", "fixedKey": true, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "valueConfig": {"expression": "", "serviceKey": "TSRM$SYS_PagingDataService", "type": "expression"}}], "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_gen_mat_com_org_md"}}], "serviceKey": "TB2B$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "表格", "mode": "simple", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [], "id": "nxzCFQcf5QZLJTs7CWI26", "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"物料库详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "XOjgLvuUhUQbCbE4H3ks5", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "6KFER2KshgemxVNySU-wK", "name": "设置平衡利库 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [], "type": "Modal"}}, "buttonType": "default", "confirmOn": "off", "label": "设置平衡利库", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_1_1_0_1_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$ext_wq_gen_mat_com_org_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "TB2B$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_1_1_0_1_0_1", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_1_1_0_1_0_2", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_1_1_0_1_0_3", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detail-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-logs", "name": "Logs", "props": {"currentModel": "TSRM$user", "label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-comOrg", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "comOrg", "label": "选择公司组织", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "parentModelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "displayComponentType": "RelationShow", "editable": false, "label": "板块", "name": "comOrg", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-scope", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "scope", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editable": false, "label": "范围", "name": "scope", "type": "SELECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-material", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "material", "label": "选择物料", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "parentModelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "物料", "name": "material", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-materialCode", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "materialCode", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请输入"}, "editable": false, "label": "物料编码", "name": "materialCode", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-balanceStock", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "balanceStock", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editable": false, "label": "是否平衡利库", "name": "balanceStock", "type": "BOOL"}}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_COM_MATERAIL-dE0Yp4GZNiCppwaJ7Ibdb", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TSRM$COMM_2B_COM_MATERAIL-Xnru_5dZfjE8S-eASEaeF", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "context$": "$context", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "serviceKey": "TB2B$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "Container"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"物料库表\")) : \"新建物料库表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "WaGSh1nmDnHsRU5ogvpbv", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ahv9RlDIbxJBsw70D0Ie7", "name": "FormField", "props": {"componentProps": {"fieldAlias": "comOrg", "label": "选择公司组织", "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "parentModelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "status", "ver", "isLatestVersion", "countryId", "timezoneId", "postcode", "coordinate", "area", "phone", "bankaccount", "bankaccountCode", "bankName", "comCorporation", "openDate", "closeDate", "enterpriseType", "assuranceCnt", "old<PERSON>ame", "socialcreditCode", "addressId", "addressDetail", "bizScope", "bizStatus", "registeredCapital", "paidinCapital", "isUnifiedCode", "bizLicenseNo", "bizLicenseScan", "orgCode", "foundDate", "taxpayersNum", "taxpayersType", "bizSector", "registrationAuthority", "nameInEn", "businessRegistrationNo", "issueDate", "staffSize", "officalWebsite", "mail", "intro", "extWqCapitalUnit", "extWqIsSignature"], "fieldAlias": "comId", "label": "选择关联公司", "labelField": "name", "modelAlias": "ERP_GEN$gen_com_type_cf", "parentModelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "关联公司", "name": "comId", "required": true, "type": "OBJECT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "code", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "code", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "hidden": false, "label": "组织名称", "name": "name", "required": true, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TERP_MIGRATE$org_com_org_cf"}}], "serviceKey": "TERP_MIGRATE$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "name", "modelAlias": "TERP_MIGRATE$org_com_org_cf", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "板块", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "gc3V_1L4FNgcJFAAWGzFY", "operator": null, "valueRules": null}], "name": "comOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "t_Kd65EGri8DOS_6kPssR", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "balanceStock", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "是否平衡利库", "name": "balanceStock", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "OH1PWCxuJoBRCoHCO16CT", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "scope", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "范围", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "bhqHjByiJDGgzsFO9bxKF", "operator": null, "valueRules": null}], "name": "scope", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Qrp6vP9S71h3IAJpP2_Nh", "name": "FormField", "props": {"componentProps": {"fieldAlias": "material", "label": "选择物料", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "parentModelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_mat_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "物料编码", "name": "matCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "mat<PERSON>ate<PERSON><PERSON>nt", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path"], "fieldAlias": "cateId", "label": "选择类目ID", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "类目", "name": "cateId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "extWqSpecification", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "规格型号", "name": "extWqSpecification", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料编码", "name": "matCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_md"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "物料", "name": "material", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "6u_gCBqhs5SXEWA8V8b68", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "materialCode", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "物料编码", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "ZMaM7qb_JhhTIxWBsvH6J", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "material.matCode", "value": "TB2B$ext_wq_gen_mat_com_org_md.material.matCode", "valueType": "model"}}], "name": "materialCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "displayName": "公司物料库库", "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "params$": "{ id: route.recordId }", "serviceKey": "TB2B$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "params$": "{ id: route?.query?.copyId }", "serviceKey": "TB2B$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_1_2_3_0_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md", "valueConfig": {"action": {"selector": "", "target": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form"}, "type": "action"}}], "service": "TSRM$MATERIAL_COMMATERIAL_SAVE_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "操作成功"}, {"action": "PageJump", "target": "show"}, {"action": "Refresh", "target": "COMMON_2B$COMM_2B_COM_MATERAIL-page"}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac_z_2_1_2_3_0_1", "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "name": "ColumnPage", "props": {"splitConfig": {"type": "ratio"}}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": ""}, "type": "Meta"}, {"children": [], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "公司物料库"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["物料库详情", "组织名称", "范围", "复制", "选择更新人", "表格", "系统信息", "操作成功！", "新建", "删除成功", "批量删除", "物料类型", "物料库表", "删除", "设置平衡利库", "保存", "批量操作", "选择公司组织", "物料名称", "基本计量单位", "批量平衡利库", "ID", "创建人", "类目", "物料", "板块名称", "是否平衡利库", "组织编码", "主体信息", "请选择", "选择物料", "更新时间", "更新人", "选择类目ID", "板块", "物料编码", "编辑", "新建物料库表", "请输入", "规格型号", "操作成功", "选择创建人", "选择关联公司", "取消", "创建时间", "确认删除吗？", "关联公司"], "i18nScanPaths": ["COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md.props.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-materialCode.props.label", "@exp:COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page-title.props.title", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-updatedBy.props.label", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.filterFields.1.componentProps.placeholder", "Hr6-YVuuHnQXvJ76bb41p.props.actionConfig.endLogicOtherConfig.2.message", "orJ769pg1KLeAVQ0ZmOFG.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch.props.items.0.label", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.fields.0.label", "M_Rw501J0pOzpzYBIN8q4.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.filterFields.0.componentProps.placeholder", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-balanceStock.props.componentProps.placeholder", "Qrp6vP9S71h3IAJpP2_Nh.props.componentProps.placeholder", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.fields.0.label", "orJ769pg1KLeAVQ0ZmOFG.props.editComponentProps.fields.3.label", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.fields.3.label", "6u_gCBqhs5SXEWA8V8b68.props.label", "orJ769pg1KLeAVQ0ZmOFG.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-createdBy.props.componentProps.label", "orJ769pg1KLeAVQ0ZmOFG.props.editComponentProps.fields.0.label", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.filterFields.0.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "orJ769pg1KLeAVQ0ZmOFG.props.editComponentProps.fields.0.componentProps.placeholder", "t_Kd65EGri8DOS_6kPssR.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-editView-action-cancel.props.label", "@exp:COMMON_2B$COMM_2B_COM_MATERAIL-editView-page-title.props.title", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.fields.1.label", "Qrp6vP9S71h3IAJpP2_Nh.props.label", "FXqDjfSunfaKScCuvoDdJ.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-material.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-updatedBy.props.componentProps.label", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.filterFields.1.label", "OH1PWCxuJoBRCoHCO16CT.props.componentProps.placeholder", "WaGSh1nmDnHsRU5ogvpbv.props.componentProps.placeholder", "PbfVPjzSMykkv_4JEm7-_.props.actionConfig.endLogicOtherConfig.2.message", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-createdBy.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "6u_gCBqhs5SXEWA8V8b68.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-edit.props.label", "bvbEjLOnozjm0sLFEHKBZ.props.componentProps.placeholder", "orJ769pg1KLeAVQ0ZmOFG.props.editComponentProps.fields.2.label", "vh1i9KGNaLcLvp7bvjO8u.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-copy.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-comOrg.props.componentProps.label", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.fields.2.label", "TTKYYiOKuXsxTrDAd8YJe.props.componentProps.placeholder", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.fields.2.componentProps.placeholder", "orJ769pg1KLeAVQ0ZmOFG.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-updatedAt.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-material.props.componentProps.label", "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-new.props.label", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.fields.2.componentProps.label", "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md.props.filterFields.0.componentProps.placeholder", "2YVsEinpEPTZ0OiMUJnFZ.props.label", "orJ769pg1KLeAVQ0ZmOFG.props.label", "t_Kd65EGri8DOS_6kPssR.props.label", "TCUm9sRYflFJRNCmYee99.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-scope.props.componentProps.placeholder", "bvbEjLOnozjm0sLFEHKBZ.props.label", "FXqDjfSunfaKScCuvoDdJ.props.componentProps.placeholder", "6KFER2KshgemxVNySU-wK.props.title", "Nhx-wMTXa1gpNkAH3ykEL.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md.props.filterFields.0.label", "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch.props.items.0.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-material.props.componentProps.placeholder", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.fields.1.label", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.fields.2.componentProps.placeholder", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.fields.2.componentProps.label", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-createdAt.props.label", "R_fLLYZPCeL51iWALOGH2.props.title", "WaGSh1nmDnHsRU5ogvpbv.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-comOrg.props.label", "2YVsEinpEPTZ0OiMUJnFZ.props.componentProps.placeholder", "@exp:COMMON_2B$COMM_2B_COM_MATERAIL-page-title.props.title", "COMMON_2B$COMM_2B_COM_MATERAIL-editView-action-save.props.actionConfig.endLogicOtherConfig.0.message", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs.props.items.0.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-comOrg.props.componentProps.placeholder", "bvbEjLOnozjm0sLFEHKBZ.props.componentProps.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-materialCode.props.componentProps.placeholder", "ahv9RlDIbxJBsw70D0Ie7.props.componentProps.placeholder", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.fields.0.componentProps.placeholder", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.filterFields.0.componentProps.placeholder", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.fields.1.componentProps.placeholder", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.fields.2.label", "orJ769pg1KLeAVQ0ZmOFG.props.editComponentProps.fields.1.componentProps.placeholder", "Qrp6vP9S71h3IAJpP2_Nh.props.componentProps.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-balanceStock.props.label", "OH1PWCxuJoBRCoHCO16CT.props.label", "ahv9RlDIbxJBsw70D0Ie7.props.componentProps.label", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-delete.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md.props.filterFields.1.label", "Hr6-YVuuHnQXvJ76bb41p.props.label", "orJ769pg1KLeAVQ0ZmOFG.props.editComponentProps.fields.2.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs.props.items.1.label", "M_Rw501J0pOzpzYBIN8q4.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch.props.label", "PbfVPjzSMykkv_4JEm7-_.props.label", "TTKYYiOKuXsxTrDAd8YJe.props.label", "ahv9RlDIbxJBsw70D0Ie7.props.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.filterFields.1.componentProps.placeholder", "vh1i9KGNaLcLvp7bvjO8u.props.componentProps.placeholder", "ahv9RlDIbxJBsw70D0Ie7.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md.props.label", "Qrp6vP9S71h3IAJpP2_Nh.props.editComponentProps.filterFields.1.label", "COMMON_2B$COMM_2B_COM_MATERAIL-editView-action-save.props.label", "-pPAxiwuB0HlFgFDkdgAb.props.label", "XOjgLvuUhUQbCbE4H3ks5.props.label", "orJ769pg1KLeAVQ0ZmOFG.props.componentProps.label", "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-scope.props.label"]}, "key": "TSRM$COMM_2B_COM_MATERAIL-list", "permissionKey": "TSRM$COMM_2B_COM_MATERAIL-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "TB2B$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "AsHkwalF4McgqfGpbsk5m", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "6KFER2KshgemxVNySU-wK", "label": "设置平衡利库", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "l64WlgO2q-ixoVei94NTh", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "TB2B$SYS_FindOneDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TCUm9sRYflFJRNCmYee99", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "6KFER2KshgemxVNySU-wK", "label": "设置平衡利库", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "M4D192Jpk4PWQxS4smmFy", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "PbfVPjzSMykkv_4JEm7-_", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "6KFER2KshgemxVNySU-wK", "label": "设置平衡利库", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "M4D192Jpk4PWQxS4smmFy", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "aDerEbTinaGYDI6VpQe5K", "label": "批量平衡利库", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "R_fLLYZPCeL51iWALOGH2", "label": "批量平衡利库", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QS3WgXekayVDyjQiJ6jDV", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [], "type": "Container"}, {"description": null, "key": "Nhx-wMTXa1gpNkAH3ykEL", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "R_fLLYZPCeL51iWALOGH2", "label": "批量平衡利库", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "8sMiRokhYAIubyKDo4nGf", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "Hr6-YVuuHnQXvJ76bb41p", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "R_fLLYZPCeL51iWALOGH2", "label": "批量平衡利库", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "8sMiRokhYAIubyKDo4nGf", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "TSRM$BATCH_SET_BALANCE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "M_Rw501J0pOzpzYBIN8q4", "label": "是否平衡利库", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "6KFER2KshgemxVNySU-wK", "label": "设置平衡利库", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "l64WlgO2q-ixoVei94NTh", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "AsHkwalF4McgqfGpbsk5m", "label": "表单组", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "2YVsEinpEPTZ0OiMUJnFZ", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "6KFER2KshgemxVNySU-wK", "label": "设置平衡利库", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "l64WlgO2q-ixoVei94NTh", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "AsHkwalF4McgqfGpbsk5m", "label": "表单组", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "TTKYYiOKuXsxTrDAd8YJe", "label": "是否平衡利库", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "R_fLLYZPCeL51iWALOGH2", "label": "批量平衡利库", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QS3WgXekayVDyjQiJ6jDV", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "aDerEbTinaGYDI6VpQe5K", "label": "批量平衡利库", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-new", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch/items/COMMON_2B$COMM_2B_COM_MATERAIL-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-multi-delete", "label": "批量删除", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "TB2B$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "r3At4Jq7xsCU-7tJ3eVlO", "label": "导入", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-sub-model", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/template/download", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-export", "label": "导出", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/trantor/portal/user/current", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "-pPAxiwuB0HlFgFDkdgAb", "label": "设置平衡利库", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "vh1i9KGNaLcLvp7bvjO8u", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "type": "Table"}, {"key": "hPrY9GY1fMn4voU1H7IjZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "bvbEjLOnozjm0sLFEHKBZ", "label": "板块", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "type": "Table"}, {"key": "hPrY9GY1fMn4voU1H7IjZ", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "orJ769pg1KLeAVQ0ZmOFG", "label": "物料", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "type": "Table"}, {"key": "hPrY9GY1fMn4voU1H7IjZ", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "FXqDjfSunfaKScCuvoDdJ", "label": "是否平衡利库", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-list-TERP_MIGRATE$ext_wq_gen_mat_com_org_md", "label": "表格", "type": "Table"}, {"key": "hPrY9GY1fMn4voU1H7IjZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form", "label": "公司物料库库", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "TB2B$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "SystemService"}, {"key": "TB2B$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "TB2B$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "WaGSh1nmDnHsRU5ogvpbv", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form", "label": "公司物料库库", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "ahv9RlDIbxJBsw70D0Ie7", "label": "板块", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form", "label": "公司物料库库", "type": "FormGroup"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_com_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "t_Kd65EGri8DOS_6kPssR", "label": "是否平衡利库", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form", "label": "公司物料库库", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "OH1PWCxuJoBRCoHCO16CT", "label": "范围", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form", "label": "公司物料库库", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "Qrp6vP9S71h3IAJpP2_Nh", "label": "物料", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form", "label": "公司物料库库", "type": "FormGroup"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "6u_gCBqhs5SXEWA8V8b68", "label": "物料编码", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-form", "label": "公司物料库库", "type": "FormGroup"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-action-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-action-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TSRM$MATERIAL_COMMATERIAL_SAVE_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "XOjgLvuUhUQbCbE4H3ks5", "label": "设置平衡利库", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-delete", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TB2B$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$ext_wq_gen_mat_com_org_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detail-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-comOrg", "label": "板块", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COM_MATERAIL-dE0Yp4GZNiCppwaJ7Ibdb", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$org_com_org_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-scope", "label": "范围", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COM_MATERAIL-dE0Yp4GZNiCppwaJ7Ibdb", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-material", "label": "物料", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COM_MATERAIL-dE0Yp4GZNiCppwaJ7Ibdb", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-materialCode", "label": "物料编码", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COM_MATERAIL-dE0Yp4GZNiCppwaJ7Ibdb", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-field-balanceStock", "label": "是否平衡利库", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COM_MATERAIL-dE0Yp4GZNiCppwaJ7Ibdb", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COM_MATERAIL-Xnru_5dZfjE8S-eASEaeF", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COM_MATERAIL-Xnru_5dZfjE8S-eASEaeF", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COM_MATERAIL-Xnru_5dZfjE8S-eASEaeF", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_COM_MATERAIL-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-view", "label": "视图", "type": "View"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-page", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-TERP_MIGRATE$ext_wq_gen_mat_com_org_md-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TSRM$COMM_2B_COM_MATERAIL-Xnru_5dZfjE8S-eASEaeF", "label": "页签项", "type": "TabItem"}, {"key": "COMMON_2B$COMM_2B_COM_MATERAIL-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}