{"access": "Private", "key": "TSRM$material_management_enabled", "name": "物料管理_启用", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": "null", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "node_1hlhnasg41", "name": "开始", "nextNodeKey": "node_1hlhncs863", "preNodeKey": null, "props": {"desc": null, "globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "mat", "fieldKey": "mat", "fieldName": "mat", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": null}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": null}, "relation": null, "required": null}], "name": null, "output": null, "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hlhncs863", "name": "赋值", "nextNodeKey": "node_1hlhnfq387", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": null}, "valueKey": "mat", "valueName": "mat"}, {"modelAlias": "ERP_GEN$gen_mat_md", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "id": "1hlhndvsd5", "operator": "EQ", "value": {"constValue": null, "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": null}, "valueKey": "request", "valueName": "request"}, {"modelAlias": "ERP_GEN$gen_mat_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}}, {"field": {"constValue": null, "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": null}, "valueKey": "mat", "valueName": "mat"}, {"modelAlias": "ERP_GEN$gen_mat_md", "relatedModel": null, "valueKey": "status", "valueName": "状态"}]}, "id": "1hlhnefk06", "operator": "EQ", "value": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}}], "desc": null, "name": null, "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hlhnfq387", "name": "更新数据", "nextNodeKey": "node_1hlhnasg42", "preNodeKey": null, "props": {"desc": null, "modelValue": {"constValue": null, "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": null}, "valueKey": "mat", "valueName": "mat"}]}, "name": null, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "物料主数据定义表"}, "type": "CascadeUpdateDataProperties"}, "renderType": null, "type": "CascadeUpdateDataNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "node_1hlhnasg42", "name": "结束", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["node_1hlhnasg41"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": null}, "relation": null, "required": null}], "key": "COMMON_2B$material_management_enabled", "name": "物料管理_启用", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "desc": null, "name": null, "permissionKey": "TSRM$material_management_enabled_perm_ac", "teamId": 35, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": "null", "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}