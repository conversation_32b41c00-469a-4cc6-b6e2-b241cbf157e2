{"access": "Private", "key": "TSRM$COMM_2B_MATERIAL:list", "name": "list", "props": {"conditionGroups": {"FuSH22t-xL90NxF9ouQX3": {"conditions": [], "id": "FuSH22t-xL90NxF9ouQX3", "logicOperator": "OR", "type": "ConditionGroup"}}, "containerSelect": {"COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md": [{"field": "matCode", "selectFields": null}, {"field": "<PERSON><PERSON><PERSON>", "selectFields": null}, {"field": "genMatTypeCfId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matTypeName", "selectFields": null}]}, {"field": "brandId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "brandName", "selectFields": null}]}, {"field": "cateId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matCateName", "selectFields": null}]}, {"field": "status", "selectFields": null}, {"field": "extWqSpecification", "selectFields": null}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}]}, "content": {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-list-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-list-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "label": "新建", "permissionKey": "TSRM$COMM_2B_MATERIAL-list_perm_ac_z_2_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否删除选中单据？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}}], "service": "ERP_GEN$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "disabled$": "mode === \"design\" ? undefined :$context.selectedKeys?.length === 0", "isMultiple": true, "label": "删除", "permissionKey": "TSRM$COMM_2B_MATERIAL-list_perm_ac_z_2_0_1", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1-button-3", "name": "ExportButton", "props": {"exportButtonServiceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "label": "导出", "permissionKey": "TSRM$COMM_2B_MATERIAL-list_perm_ac_z_2_0_2", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-list-TERP_MIGRATE$gen_mat_md-logs", "name": "Logs", "props": {"currentModel": "ERP_GEN$gen_mat_md", "label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "show"}]}, "buttonType": "default", "confirmOn": "off", "label": "查看", "permissionKey": "TSRM$COMM_2B_MATERIAL-list_perm_ac_z_2_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_MATERIAL-list_perm_ac_z_2_1_1", "showCondition": {"conditions": [{"conditions": [{"id": "xhWQtLyM_dVlmtZdtb7nM", "leftValue": {"fieldType": "Enum", "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "942pCWfWia_uWVzPT9Mpg", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "zxU7REynU-syHT5yeOvk-", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1-button-3", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "record?.id", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_md"}}], "service": "ERP_GEN$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "permissionKey": "TSRM$COMM_2B_MATERIAL-list_perm_ac_z_2_1_2", "showCondition": {"conditions": [{"conditions": [{"id": "xL-7LCRevaM5LpBnLtzc3", "leftValue": {"fieldType": "Enum", "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "oHIMBBsFfXgcqIGFj_S66", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "43IMuWC4dXQ9mJGZFrc3p", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-R_IZ0pylBYcRJ22zK3Te4", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否确认启用物料？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_GEN$gen_mat_md", "required": null, "valueConfig": {"expression": "record", "type": "expression"}}], "service": "TSRM$material_management_enabled"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "level": "success", "message": "物料启用成功！"}, {"action": "Refresh", "target": ["COMMON_2B$COMM_2B_MATERIAL-list", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "permissionKey": "TSRM$COMM_2B_MATERIAL-list_perm_ac_z_2_1_3", "showCondition": {"conditions": [{"conditions": [{"id": "FODtum6MhbPl6xhwMsb7n", "leftValue": {"fieldType": "Enum", "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "4cfL_P5n8IIiEiO3jU84H", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "yjWCJxk5FPMM17SUFWg3e", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-YKeuJT27M24ws7Hk-nHnC", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否确认停用物料？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_GEN$gen_mat_md", "required": null, "valueConfig": {"expression": "record", "type": "expression"}}], "service": "TSRM$material_management_disable"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "level": "success", "message": "物料停用成功！"}, {"action": "Refresh", "target": ["COMMON_2B$COMM_2B_MATERIAL-list", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "permissionKey": "TSRM$COMM_2B_MATERIAL-list_perm_ac_z_2_1_4", "showCondition": {"conditions": [{"conditions": [{"id": "lLGgPsHL5TBISlngFExy2", "leftValue": {"fieldType": "Enum", "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "bp_kh1j0h-YfZAwPMMJhh", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "LC0ZcXjfInvnCxrDyK1tu", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-toolbar-actions-1", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "x664WXqfAxQNdwg1RAi8b", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "-UZnoTGnxo1CyNoE_e0f0", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料编码", "name": "matCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "KaynKGiXI0yQk5wS56XRF", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "SCsVWePgirMCEiyGhX4SN", "name": "Field", "props": {"componentProps": {"fieldAlias": "genMatTypeCfId", "label": "选择物料类型", "labelField": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matTypeCode", "parentModelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "物料类型编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "LU7KYGyFy9PJDMFP_mGM_", "trigger": "auto", "valueRules": null}], "name": "matTypeCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "matTypeName", "parentModelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "物料类型名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "wLkTA7YvXlSilRSXEPMyZ", "trigger": "auto", "valueRules": null}], "name": "matTypeName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "物料类型", "modelAlias": "ERP_GEN$gen_mat_type_cf", "name": "genMatTypeCfId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "suG4MztRbziWIOzK4Bdpe", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "brandId", "label": "选择品牌ID", "labelField": "brandName", "modelAlias": "ERP_GEN$gen_brand_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_brand_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_brand_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "brandName", "parentModelAlias": "ERP_GEN$gen_brand_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "品牌名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "T6IKvsPtQ3XNeEjpYkioX", "trigger": "auto", "valueRules": null}], "name": "brandName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_brand_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_brand_md", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "label": "品牌ID", "modelAlias": "ERP_GEN$gen_mat_md", "name": "brandId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Pr2CRzBqQIZe3Ky__PBtO", "name": "Field", "props": {"componentProps": {"fieldAlias": "cateId", "label": "选择类目ID", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCateName", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "类目名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "42JvIJT8f7J_8I0iXH49o", "trigger": "auto", "valueRules": null}], "name": "matCateName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "类目", "name": "cateId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-INUSVlcOoUqAqRzik_EGx", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "OtlZyy5KQ4aiY3zCx07_Y", "name": "Field", "props": {"componentProps": {"fieldAlias": "extWqSpecification", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "label": "规格型号", "name": "extWqSpecification", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "r3bS096OI5_MibxjZZfW4", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": false, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "c2wj_Opu5qaK2u-hcfHIe", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": false, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, "type": "Widget"}], "key": "9kGCvk6oCtkejka8ilcv3", "name": "Fields", "props": {}, "type": "Meta"}], "key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "name": "Table", "props": {"acceptFilterQuery": true, "allowClickRowSelect": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料编码", "name": "matCode", "type": "TEXT", "width": 146}, {"componentProps": {"defaultValue": null, "fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "genMatTypeCfId", "label": "选择物料类型", "labelField": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matTypeCode", "parentModelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "物料类型编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "4dHJCer1irmQcE68dhJdd", "trigger": "auto", "valueRules": null}], "name": "matTypeCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "matTypeName", "parentModelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "物料类型名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "5Ze8gCXlWFrnQ3_8KJUh9", "trigger": "auto", "valueRules": null}], "name": "matTypeName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "物料类型", "name": "genMatTypeCfId", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "editComponentProps": {"fields": [], "filterFields": [], "labelField": [], "shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "ID", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "-oGxf7EqmdKWoK_7LZ2Q9", "valueRules": null}], "name": "cateId.id", "required": false, "type": "NUMBER", "width": 120}], "flow": {"containerKey": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_md"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "表格", "mode": "normal", "modelAlias": "ERP_GEN$gen_mat_md", "showConfigure": false, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [], "id": "FuSH22t-xL90NxF9ouQX3", "logicOperator": "OR", "type": "ConditionGroup"}, "toolbar": {"search": false}}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_MATERIAL-list", "name": "Page", "props": {"params": [], "showFooter": false, "showHeader": false}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["表格", "状态", "新建", "删除成功", "停用", "类目名称", "查看", "是否删除选中单据？", "物料类型", "删除", "是否确认启用物料？", "品牌名称", "启用", "是否确认停用物料？", "选择物料类型", "物料名称", "ID", "类目", "请选择", "更新时间", "物料启用成功！", "物料类型编码", "品牌ID", "选择类目ID", "物料编码", "编辑", "物料停用成功！", "请输入", "规格型号", "创建时间", "物料类型名称", "选择品牌ID"], "i18nScanPaths": ["COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md.props.filterFields.3.label", "COMMON_2B$COMM_2B_MATERIAL-R_IZ0pylBYcRJ22zK3Te4.props.actionConfig.beforeLogicConfig.0.text", "KaynKGiXI0yQk5wS56XRF.props.componentProps.placeholder", "x664WXqfAxQNdwg1RAi8b.props.label", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md.props.filterFields.0.label", "-UZnoTGnxo1CyNoE_e0f0.props.componentProps.placeholder", "c2wj_Opu5qaK2u-hcfHIe.props.label", "SCsVWePgirMCEiyGhX4SN.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1-button-1.props.label", "COMMON_2B$COMM_2B_MATERIAL-YKeuJT27M24ws7Hk-nHnC.props.actionConfig.beforeLogicConfig.0.text", "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1-button-2.props.label", "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1-button-2.props.actionConfig.endLogicOtherConfig.1.message", "COMMON_2B$COMM_2B_MATERIAL-R_IZ0pylBYcRJ22zK3Te4.props.actionConfig.endLogicOtherConfig.0.message", "SCsVWePgirMCEiyGhX4SN.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1-button-2.props.actionConfig.beforeLogicConfig.0.text", "r3bS096OI5_MibxjZZfW4.props.label", "COMMON_2B$COMM_2B_MATERIAL-INUSVlcOoUqAqRzik_EGx.props.label", "suG4MztRbziWIOzK4Bdpe.props.label", "SCsVWePgirMCEiyGhX4SN.props.componentProps.label", "SCsVWePgirMCEiyGhX4SN.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_MATERIAL-YKeuJT27M24ws7Hk-nHnC.props.label", "r3bS096OI5_MibxjZZfW4.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md.props.filterFields.2.componentProps.label", "Pr2CRzBqQIZe3Ky__PBtO.props.componentProps.label", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md.props.filterFields.1.componentProps.placeholder", "Pr2CRzBqQIZe3Ky__PBtO.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_MATERIAL-record-actions-1-button-3.props.actionConfig.endLogicOtherConfig.1.message", "OtlZyy5KQ4aiY3zCx07_Y.props.label", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md.props.filterFields.1.label", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md.props.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md.props.filterFields.2.componentProps.placeholder", "Pr2CRzBqQIZe3Ky__PBtO.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-YKeuJT27M24ws7Hk-nHnC.props.actionConfig.endLogicOtherConfig.0.message", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md.props.filterFields.3.componentProps.placeholder", "SCsVWePgirMCEiyGhX4SN.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md.props.label", "-UZnoTGnxo1CyNoE_e0f0.props.label", "c2wj_Opu5qaK2u-hcfHIe.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-INUSVlcOoUqAqRzik_EGx.props.componentProps.placeholder", "suG4MztRbziWIOzK4Bdpe.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md.props.filterFields.2.label", "suG4MztRbziWIOzK4Bdpe.props.editComponentProps.fields.0.label", "SCsVWePgirMCEiyGhX4SN.props.editComponentProps.fields.0.componentProps.placeholder", "OtlZyy5KQ4aiY3zCx07_Y.props.componentProps.placeholder", "suG4MztRbziWIOzK4Bdpe.props.componentProps.label", "COMMON_2B$COMM_2B_MATERIAL-record-actions-1-button-3.props.label", "COMMON_2B$COMM_2B_MATERIAL-R_IZ0pylBYcRJ22zK3Te4.props.label", "Pr2CRzBqQIZe3Ky__PBtO.props.label", "Pr2CRzBqQIZe3Ky__PBtO.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-record-actions-1-button-1.props.label", "suG4MztRbziWIOzK4Bdpe.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-record-actions-1-button-2.props.label", "KaynKGiXI0yQk5wS56XRF.props.label", "x664WXqfAxQNdwg1RAi8b.props.componentProps.placeholder", "SCsVWePgirMCEiyGhX4SN.props.label"]}, "key": "TSRM$COMM_2B_MATERIAL-list", "permissionKey": "TSRM$COMM_2B_MATERIAL-list_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1-button-1", "label": "新建", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1-button-2", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "ERP_GEN$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1-button-3", "label": "导出", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-list-TERP_MIGRATE$gen_mat_md-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-batch-actions-1", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1-button-1", "label": "查看", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1-button-2", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1-button-3", "label": "删除", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "ERP_GEN$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-R_IZ0pylBYcRJ22zK3Te4", "label": "启用", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$material_management_enabled", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-YKeuJT27M24ws7Hk-nHnC", "label": "停用", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-record-actions-1", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "TSRM$material_management_disable", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "x664WXqfAxQNdwg1RAi8b", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "9kGCvk6oCtkejka8ilcv3", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "-UZnoTGnxo1CyNoE_e0f0", "label": "物料编码", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "9kGCvk6oCtkejka8ilcv3", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "KaynKGiXI0yQk5wS56XRF", "label": "物料名称", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "9kGCvk6oCtkejka8ilcv3", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "SCsVWePgirMCEiyGhX4SN", "label": "物料类型", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "9kGCvk6oCtkejka8ilcv3", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "suG4MztRbziWIOzK4Bdpe", "label": "品牌ID", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "9kGCvk6oCtkejka8ilcv3", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_brand_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_brand_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "Pr2CRzBqQIZe3Ky__PBtO", "label": "类目", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "9kGCvk6oCtkejka8ilcv3", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-INUSVlcOoUqAqRzik_EGx", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "9kGCvk6oCtkejka8ilcv3", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "OtlZyy5KQ4aiY3zCx07_Y", "label": "规格型号", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "9kGCvk6oCtkejka8ilcv3", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "r3bS096OI5_MibxjZZfW4", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "9kGCvk6oCtkejka8ilcv3", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "c2wj_Opu5qaK2u-hcfHIe", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-list", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-table-container-TERP_MIGRATE$gen_mat_md", "label": "表格", "type": "Table"}, {"key": "9kGCvk6oCtkejka8ilcv3", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}, "type": "View"}