{"access": "Private", "key": "TSRM$COMM_2B_MATERIAL:detail", "name": "detail", "props": {"containerSelect": {"COMMON_2B$COMM_2B_MATERIAL-detailView-detail": [{"field": "matCode", "selectFields": null}, {"field": "<PERSON><PERSON><PERSON>", "selectFields": null}, {"field": "genMatTypeCfId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matTypeName", "selectFields": null}]}, {"field": "status", "selectFields": null}, {"field": "baseUomId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "volumUomId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "matVolum", "selectFields": null}, {"field": "cateId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matCateName", "selectFields": null}]}, {"field": "brandId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "brandName", "selectFields": null}]}, {"field": "matCodeExt", "selectFields": null}, {"field": "extWqShortName", "selectFields": null}, {"field": "extWqMnemonicCode", "selectFields": null}, {"field": "extWqBarcode", "selectFields": null}, {"field": "extWqSpecification", "selectFields": null}, {"field": "extWqMaterialQuality", "selectFields": null}, {"field": "extWqUsage", "selectFields": null}, {"field": "extWqReceiveStandard", "selectFields": null}, {"field": "extWqRemark", "selectFields": null}, {"field": "extWqImage", "selectFields": null}, {"field": "extWqOverThreshold", "selectFields": null}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}]}, "content": {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detail-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "物料主数据定义表详情", "useExpression": false}, "type": "Meta"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detail-page-header-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { copyId: route.recordId } })"}, "buttonType": "default", "confirmOn": "off", "label": "复制", "permissionKey": "TSRM$COMM_2B_MATERIAL-detail_perm_ac_z_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detail-page-header-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "permissionKey": "TSRM$COMM_2B_MATERIAL-detail_perm_ac_z_1_1", "showCondition": {"conditions": [{"conditions": [{"id": "d98fXUfxDEcFC_lEV25Z0", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "2nnLuBN2ie7kuBiq5tI9I", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "gN_q2h92TkDqlcoM4qPz1", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detail-TERP_MIGRATE$gen_mat_md-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_MATERIAL-detail-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matCode", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "editable": false, "label": "物料编码", "name": "matCode", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matName", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "editable": false, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "type": "TEXT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-genMatTypeCfId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "genMatTypeCfId", "label": "选择物料类型", "labelField": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "物料类型", "name": "genMatTypeCfId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-ujoV0TggDZ7DguhFvLx-M", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "editable": false, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-baseUomId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "baseUomId", "label": "选择基本计量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "基本计量单位", "name": "baseUomId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-volumUomId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "volumUomId", "label": "选择体积单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "体积单位", "name": "volumUomId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matVolum", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matVolum", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "editable": false, "label": "体积", "name": "matVolum", "type": "NUMBER"}}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-cateId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "cateId", "label": "选择类目ID", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "类目", "name": "cateId", "type": "OBJECT"}}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-brandId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "brandId", "label": "选择品牌ID", "labelField": "brandName", "modelAlias": "ERP_GEN$gen_brand_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_brand_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_brand_md"}, "displayComponentType": "RelationShow", "editable": false, "label": "品牌ID", "name": "brandId", "required": false, "type": "OBJECT", "width": 120}}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matCodeExt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matCodeExt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "外部物料编码", "name": "matCodeExt", "type": "TEXT"}}, {"children": [], "key": "lzHTjNzWMEJ10AIFmWP3A", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqShortName", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "简称", "name": "extWqShortName", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "L8Q84JFSZPS3ZOt9hPZ83", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqMnemonicCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "助记码", "name": "extWqMnemonicCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "IMRzQTAEFYAbQdJq6CDZ_", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqBarcode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "条形码", "name": "extWqBarcode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ty8row7wyl5lxFL5l3fYO", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqSpecification", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "规格型号", "name": "extWqSpecification", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "URAtEzW9kW68wXZJnyK0E", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqMaterialQuality", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "材质", "name": "extWqMaterialQuality", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "YqQkNA5IMQSWDpjEYO0F-", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqUsage", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "用途", "name": "extWqUsage", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ldG-O4dOd81VXueSGBAn-", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqReceiveStandard", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "收货标准", "name": "extWqReceiveStandard", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "CKNTsQOBNIzkjz_xDm0J8", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqRemark", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "initialValue": null, "label": "备注", "name": "extWqRemark", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "aXxZwINNo9_X70c6_savT", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "extWqImage", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请上传"}, "displayComponentType": "FileUploadShow", "editable": false, "initialValue": null, "label": "图片", "name": "extWqImage", "required": false, "type": "ATTACHMENT", "width": 120}, "type": "Widget"}, {"children": [], "key": "H9DWduQwEGAlYq1skTdtN", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqOverThreshold", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": 2}, "displayComponentProps": {"precision": 2}, "displayComponentType": "Number", "editable": false, "initialValue": null, "label": "超发阈值", "name": "extWqOverThreshold", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "name": "DetailGroupItem", "props": {"title": ""}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "name": "Detail", "props": {"flow": {"containerKey": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "Container"}], "key": "COMMON_2B$COMM_2B_MATERIAL-detail", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showBack": true, "showFooter": false, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "i18nConfig": {"i18nKeySet": ["超发阈值", "复制", "图片", "状态", "备注", "物料类型", "条形码", "材质", "选择物料类型", "物料名称", "基本计量单位", "请上传", "类目", "用途", "详情组标题", "请选择", "外部物料编码", "更新时间", "收货标准", "选择类目ID", "品牌ID", "编辑", "物料编码", "请输入", "体积", "规格型号", "体积单位", "创建时间", "选择基本计量单位", "物料主数据定义表详情", "选择品牌ID", "助记码", "选择体积单位", "简称"], "i18nScanPaths": ["COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-cateId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-ujoV0TggDZ7DguhFvLx-M.props.label", "L8Q84JFSZPS3ZOt9hPZ83.props.componentProps.placeholder", "L8Q84JFSZPS3ZOt9hPZ83.props.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-cateId.props.componentProps.label", "COMMON_2B$COMM_2B_MATERIAL-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt.props.label", "COMMON_2B$COMM_2B_MATERIAL-detail-page-header-copy.props.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matCode.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-genMatTypeCfId.props.label", "URAtEzW9kW68wXZJnyK0E.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detail-page-header-edit.props.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-baseUomId.props.componentProps.placeholder", "H9DWduQwEGAlYq1skTdtN.props.componentProps.placeholder", "lzHTjNzWMEJ10AIFmWP3A.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-baseUomId.props.componentProps.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matVolum.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-volumUomId.props.componentProps.placeholder", "YqQkNA5IMQSWDpjEYO0F-.props.componentProps.placeholder", "ldG-O4dOd81VXueSGBAn-.props.label", "lzHTjNzWMEJ10AIFmWP3A.props.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-cateId.props.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matName.props.label", "aXxZwINNo9_X70c6_savT.props.label", "URAtEzW9kW68wXZJnyK0E.props.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-baseUomId.props.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matVolum.props.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matName.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matCodeExt.props.componentProps.placeholder", "IMRzQTAEFYAbQdJq6CDZ_.props.componentProps.placeholder", "CKNTsQOBNIzkjz_xDm0J8.props.componentProps.placeholder", "H9DWduQwEGAlYq1skTdtN.props.label", "COMMON_2B$COMM_2B_MATERIAL-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt.props.label", "COMMON_2B$COMM_2B_MATERIAL-ujoV0TggDZ7DguhFvLx-M.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-brandId.props.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-volumUomId.props.componentProps.label", "YqQkNA5IMQSWDpjEYO0F-.props.label", "@exp:COMMON_2B$COMM_2B_MATERIAL-detail-page-title.props.title", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout.props.title", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-genMatTypeCfId.props.componentProps.label", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-genMatTypeCfId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-brandId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-volumUomId.props.label", "COMMON_2B$COMM_2B_MATERIAL-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matCode.props.label", "ty8row7wyl5lxFL5l3fYO.props.label", "ty8row7wyl5lxFL5l3fYO.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-brandId.props.componentProps.label", "aXxZwINNo9_X70c6_savT.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matCodeExt.props.label", "CKNTsQOBNIzkjz_xDm0J8.props.label", "IMRzQTAEFYAbQdJq6CDZ_.props.label", "ldG-O4dOd81VXueSGBAn-.props.componentProps.placeholder"]}, "key": "TSRM$COMM_2B_MATERIAL-detail", "permissionKey": "TSRM$COMM_2B_MATERIAL-detail_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detail-page-header-copy", "label": "复制", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detail-page-header-edit", "label": "编辑", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detail-TERP_MIGRATE$gen_mat_md-logs", "label": "日志", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detail-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matCode", "label": "物料编码", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matName", "label": "物料名称", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-genMatTypeCfId", "label": "物料类型", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-ujoV0TggDZ7DguhFvLx-M", "label": "状态", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-baseUomId", "label": "基本计量单位", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-volumUomId", "label": "体积单位", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matVolum", "label": "体积", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-cateId", "label": "类目", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-brandId", "label": "品牌ID", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_brand_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout-For-DetailField-matCodeExt", "label": "外部物料编码", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "lzHTjNzWMEJ10AIFmWP3A", "label": "简称", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "L8Q84JFSZPS3ZOt9hPZ83", "label": "助记码", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "IMRzQTAEFYAbQdJq6CDZ_", "label": "条形码", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ty8row7wyl5lxFL5l3fYO", "label": "规格型号", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "URAtEzW9kW68wXZJnyK0E", "label": "材质", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "YqQkNA5IMQSWDpjEYO0F-", "label": "用途", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ldG-O4dOd81VXueSGBAn-", "label": "收货标准", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "CKNTsQOBNIzkjz_xDm0J8", "label": "备注", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "aXxZwINNo9_X70c6_savT", "label": "图片", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "H9DWduQwEGAlYq1skTdtN", "label": "超发阈值", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detail-detail-group-item-3-tabs-detail-for-detail-field-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-detail-detail-group-item-3-tabs-detail-for-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-detail", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail", "label": "详情", "type": "Detail"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-detailView-detail-Layout", "label": "详情组标题", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "detail", "type": "DETAIL"}, "type": "View"}