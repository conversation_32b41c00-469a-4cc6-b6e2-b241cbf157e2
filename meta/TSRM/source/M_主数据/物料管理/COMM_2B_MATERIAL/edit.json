{"access": "Private", "key": "TSRM$COMM_2B_MATERIAL:edit", "name": "edit", "props": {"containerSelect": {"COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md": [{"field": "version", "selectFields": null}, {"field": "matCode", "selectFields": null}, {"field": "<PERSON><PERSON><PERSON>", "selectFields": null}, {"field": "extWqShortName", "selectFields": null}, {"field": "genMatTypeCfId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matTypeName", "selectFields": null}]}, {"field": "cateId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matCateName", "selectFields": null}]}, {"field": "baseUomId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "brandId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "brandName", "selectFields": null}]}, {"field": "extWqSpecification", "selectFields": null}, {"field": "extWqMnemonicCode", "selectFields": null}, {"field": "extWqBarcode", "selectFields": null}, {"field": "extWqMaterialQuality", "selectFields": null}, {"field": "extWqUsage", "selectFields": null}, {"field": "matVolum", "selectFields": null}, {"field": "matCodeExt", "selectFields": null}, {"field": "extWqReceiveStandard", "selectFields": null}, {"field": "extWqRemark", "selectFields": null}, {"field": "extWqMainImage", "selectFields": null}, {"field": "extWqImage", "selectFields": null}, {"field": "extWqOverThreshold", "selectFields": null}, {"field": "extWqIntegrateGoogssystem", "selectFields": null}]}, "content": {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-editView-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑物料主数据定义表\" : \"创建物料主数据定义表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "xFRAxiKpUGBjPOOw6g4JM", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TSRM$user", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "TSRM$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "4MlPkShm0Bz3_5cvsDErt", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "TSRM$user", "serviceKey": "TSRM$SYS_PagingDataService", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matCode", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "物料编码", "name": "matCode", "rules": [{"message": "请输入物料编码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matName", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "物料名称", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "TKXLEbkeBBVC6ouo58wrT", "operator": null, "valueRules": null}], "name": "<PERSON><PERSON><PERSON>", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "roWOXRI5XSQIApGGtIlaT", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqShortName", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "简称", "name": "extWqShortName", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Jmu_M4y_F1BW0f4wYxpB2", "name": "FormField", "props": {"componentProps": {"fieldAlias": "genMatTypeCfId", "label": "选择物料类型", "labelField": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "物料类型编码", "name": "matTypeCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "物料类型名称", "name": "matTypeName", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_type_cf"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "label": "物料类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "FFKZSiGheMF0AIR6Hu9Jd", "valueRules": null}], "name": "genMatTypeCfId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "cateId", "label": "选择类目ID", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "类目编码", "name": "matCateCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "类目名称", "name": "matCateName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "类目编码", "name": "matCateCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "类目名称", "name": "matCateName", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "matCateName", "mainField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "showFilterFields": true, "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "类目", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "VOozVJy878_ZnyfOxd1D6", "operator": null, "valueRules": null}], "name": "cateId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "baseUomId", "label": "选择基本计量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": "uomDesc"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "uomType", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "计量单位类型编码", "name": "uomType", "required": true, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "uomCode", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "计量单位编码", "name": "uomCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "计量单位名称", "name": "uomDesc", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "uomDesc", "mainField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "基本计量单位", "name": "baseUomId", "rules": [{"message": "请输入基本计量单位", "required": true}], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-brandId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "brandId", "label": "选择品牌ID", "labelField": "brandName", "modelAlias": "ERP_GEN$gen_brand_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_brand_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_brand_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "brandName", "modelAlias": "ERP_GEN$gen_brand_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "品牌名称", "name": "brandName", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_brand_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_brand_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_brand_md"}}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "brandName", "modelAlias": "ERP_GEN$gen_brand_md", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "品牌ID", "name": "brandId", "required": false, "rules": [], "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "9ublh-yihZac86pigWWVY", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqSpecification", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "规格型号", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "X1ccoti0_5gG7sAPeNgV9", "valueRules": null}], "name": "extWqSpecification", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "wFMJSs156hdNb7o7u94g3", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqMnemonicCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "助记码", "name": "extWqMnemonicCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Xn6zj-Jye8Tka7IVEz49c", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqBarcode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "条形码", "name": "extWqBarcode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "eaxsfOfswBtk1jaN6_K2C", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqMaterialQuality", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "材质", "name": "extWqMaterialQuality", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "hYhqM5UsNPY4_2OymccC9", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqUsage", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "label": "用途", "name": "extWqUsage", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matVolum", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matVolum", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "体积", "name": "matVolum", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matCodeExt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "matCodeExt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "外部物料编码", "name": "matCodeExt", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ibhCFFoNCifO8TafvI9uf", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqReceiveStandard", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "editComponentType": "InputText", "label": "收货标准", "name": "extWqReceiveStandard", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ji4J2Elc3imB3R6VDbl4g", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqRemark", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "editComponentType": "InputText", "label": "备注", "name": "extWqRemark", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Z7cVeiCV3ebLruCCa0u7b", "name": "FormField", "props": {"componentProps": {"fieldAlias": "extWqMainImage", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请上传"}, "displayComponentProps": {"listType": "picture-card"}, "displayComponentType": "FileUploadShow", "editComponentProps": {"limitFile": false, "limitMaxFile": false}, "editComponentType": "FileUpload", "label": "主图", "name": "extWqMainImage", "required": false, "type": "ATTACHMENT", "width": 120}, "type": "Widget"}, {"children": [], "key": "nk8ahgK6TfliFGOkqJEfj", "name": "FormField", "props": {"componentProps": {"fieldAlias": "extWqImage", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请上传"}, "displayComponentType": "FileUploadShow", "editComponentType": "FileUpload", "label": "其它图片", "name": "extWqImage", "required": false, "type": "ATTACHMENT", "width": 120}, "type": "Widget"}, {"children": [], "key": "6Ht7pmXHaH2gXNbZncb3y", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqOverThreshold", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": 2}, "displayComponentProps": {"finishedColorType": "success", "percentShowType": "percent", "precision": 2, "unfinishedColorType": "primary"}, "displayComponentType": "PercentShow", "editComponentProps": {"percentRange": [0, 1], "precision": 2}, "editComponentType": "Percent", "label": "超发阈值", "name": "extWqOverThreshold", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "X4UJobcYObkKe6a1L0SKQ", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "extWqIntegrateGoogssystem", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "label": "是否对接物资系统", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "lMs78ELd7sI-w1hhrsHqB", "valueRules": null}], "name": "extWqIntegrateGoogssystem", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": false}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route?.query?.copyId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "Container"}, {"children": [{"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "permissionKey": "TSRM$COMM_2B_MATERIAL-edit_perm_ac_z_2_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "COMMON_2B$COMM_2B_MATERIAL-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md"}, "type": "action"}}], "service": "ERP_GEN$SYS_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["ROOT"]}, {"action": "OpenView", "openViewConfig": {"page": {"key": "TSRM$COMM_2B_MATERIAL-edit", "name": "detail", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": "ERP_GEN$SYS_SaveDataService", "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "permissionKey": "TSRM$COMM_2B_MATERIAL-edit_perm_ac_z_2_1", "showCondition": {}, "type": "primary"}, "type": "Widget"}], "key": "COMMON_2B$COMM_2B_MATERIAL-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "COMMON_2B$COMM_2B_MATERIAL-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "i18nConfig": {"i18nKeySet": ["超发阈值", "状态", "请输入版本号", "是否叶子节点", "条形码", "保存", "请输入ID", "基本计量单位", "用户名", "请上传", "ID", "创建人", "请输入更新时间", "保存成功!", "逻辑删除标识", "请选择", "版本号", "主图", "更新时间", "物料类型编码", "收货标准", "品牌ID", "选择创建人", "请输入逻辑删除标识", "计量单位编码", "选择品牌ID", "选择更新人", "类目名称", "备注", "计量单位名称", "请输入创建时间", "物料类型", "品牌名称", "请输入物料编码", "材质", "选择物料类型", "物料名称", "创建物料主数据定义表", "类目编码", "类目", "用途", "计量单位类型编码", "外部物料编码", "是否对接物资系统", "更新人", "选择类目ID", "物料编码", "请输入", "规格型号", "体积", "取消", "创建时间", "选择基本计量单位", "其它图片", "编辑物料主数据定义表", "物料类型名称", "助记码", "请输入基本计量单位", "简称"], "i18nScanPaths": ["COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedBy.props.componentProps.label", "Xn6zj-Jye8Tka7IVEz49c.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matVolum.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matCodeExt.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.filterFields.1.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matName.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matVolum.props.componentProps.placeholder", "Jmu_M4y_F1BW0f4wYxpB2.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedBy.props.componentProps.placeholder", "6Ht7pmXHaH2gXNbZncb3y.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedAt.props.componentProps.placeholder", "Z7cVeiCV3ebLruCCa0u7b.props.componentProps.placeholder", "eaxsfOfswBtk1jaN6_K2C.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId.props.componentProps.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedAt.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.filterFields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matCode.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-version.props.rules.0.message", "eaxsfOfswBtk1jaN6_K2C.props.label", "COMMON_2B$COMM_2B_MATERIAL-editView-footer-save.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-brandId.props.label", "Jmu_M4y_F1BW0f4wYxpB2.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId.props.rules.0.message", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-deleted.props.rules.0.message", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.fields.3.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-brandId.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedBy.props.label", "nk8ahgK6TfliFGOkqJEfj.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.componentProps.placeholder", "9ublh-yihZac86pigWWVY.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId.props.editComponentProps.fields.0.label", "wFMJSs156hdNb7o7u94g3.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-id.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdAt.props.label", "Xn6zj-Jye8Tka7IVEz49c.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-id.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-editView-footer-save.props.actionConfig.endLogicOtherConfig.2.message", "@exp:COMMON_2B$COMM_2B_MATERIAL-editView-title.props.title", "Jmu_M4y_F1BW0f4wYxpB2.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdBy.props.componentProps.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdBy.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-deleted.props.componentProps.placeholder", "ibhCFFoNCifO8TafvI9uf.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-brandId.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matName.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId.props.editComponentProps.fields.1.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdBy.props.componentProps.placeholder", "hYhqM5UsNPY4_2OymccC9.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-version.props.label", "ji4J2Elc3imB3R6VDbl4g.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.label", "Jmu_M4y_F1BW0f4wYxpB2.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedBy.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-deleted.props.label", "roWOXRI5XSQIApGGtIlaT.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdBy.props.editComponentProps.fields.0.componentProps.placeholder", "ibhCFFoNCifO8TafvI9uf.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId.props.componentProps.placeholder", "X4UJobcYObkKe6a1L0SKQ.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.fields.1.label", "X4UJobcYObkKe6a1L0SKQ.props.label", "6Ht7pmXHaH2gXNbZncb3y.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedAt.props.rules.0.message", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdAt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.fields.2.componentProps.placeholder", "wFMJSs156hdNb7o7u94g3.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matCode.props.rules.0.message", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId.props.editComponentProps.fields.2.componentProps.placeholder", "9ublh-yihZac86pigWWVY.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-brandId.props.componentProps.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdAt.props.rules.0.message", "roWOXRI5XSQIApGGtIlaT.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-brandId.props.editComponentProps.fields.0.label", "COMMON_2B$COMM_2B_MATERIAL-editView-footer-cancel.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matCode.props.componentProps.placeholder", "nk8ahgK6TfliFGOkqJEfj.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-id.props.rules.0.message", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId.props.editComponentProps.fields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.filterFields.1.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.fields.3.label", "Z7cVeiCV3ebLruCCa0u7b.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId.props.editComponentProps.fields.2.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-version.props.componentProps.placeholder", "Jmu_M4y_F1BW0f4wYxpB2.props.editComponentProps.fields.0.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdBy.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.componentProps.label", "Jmu_M4y_F1BW0f4wYxpB2.props.componentProps.label", "ji4J2Elc3imB3R6VDbl4g.props.label", "Jmu_M4y_F1BW0f4wYxpB2.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.fields.1.componentProps.placeholder", "hYhqM5UsNPY4_2OymccC9.props.label", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matCodeExt.props.componentProps.placeholder", "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId.props.editComponentProps.filterFields.0.label"]}, "key": "TSRM$COMM_2B_MATERIAL-edit", "permissionKey": "TSRM$COMM_2B_MATERIAL-edit_perm_ac", "resources": [{"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_CopyDataConverterService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-editView-footer-cancel", "label": "取消", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-editView-footer-save", "label": "保存", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [{"key": "ERP_GEN$SYS_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-id", "label": "ID", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdBy", "label": "创建人", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedBy", "label": "更新人", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "TSRM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}, {"key": "TSRM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TSRM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-createdAt", "label": "创建时间", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-updatedAt", "label": "更新时间", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-version", "label": "版本号", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-deleted", "label": "逻辑删除标识", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matCode", "label": "物料编码", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matName", "label": "物料名称", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "roWOXRI5XSQIApGGtIlaT", "label": "简称", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "Jmu_M4y_F1BW0f4wYxpB2", "label": "物料类型", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-cateId", "label": "类目", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-baseUomId", "label": "基本计量单位", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-brandId", "label": "品牌ID", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_brand_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_brand_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "9ublh-yihZac86pigWWVY", "label": "规格型号", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "wFMJSs156hdNb7o7u94g3", "label": "助记码", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "Xn6zj-Jye8Tka7IVEz49c", "label": "条形码", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "eaxsfOfswBtk1jaN6_K2C", "label": "材质", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "hYhqM5UsNPY4_2OymccC9", "label": "用途", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matVolum", "label": "体积", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md-for-widget-matCodeExt", "label": "外部物料编码", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ibhCFFoNCifO8TafvI9uf", "label": "收货标准", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ji4J2Elc3imB3R6VDbl4g", "label": "备注", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "Z7cVeiCV3ebLruCCa0u7b", "label": "主图", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "nk8ahgK6TfliFGOkqJEfj", "label": "其它图片", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "6Ht7pmXHaH2gXNbZncb3y", "label": "超发阈值", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "X4UJobcYObkKe6a1L0SKQ", "label": "是否对接物资系统", "path": [{"key": "COMMON_2B$COMM_2B_MATERIAL-editView", "label": "页面", "type": "Page"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-TERP_MIGRATE$gen_mat_md", "label": "表单组", "type": "FormGroup"}, {"key": "COMMON_2B$COMM_2B_MATERIAL-total-config-container-TERP_MIGRATE$gen_mat_md", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "title": "edit", "type": "FORM"}, "type": "View"}